{"flutter": {"platforms": {"android": {"default": {"projectId": "castle-restaurant-cc4b4", "appId": "1:499010556073:android:2d27bcc7d2dba4b1db99b8", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "castle-restaurant-cc4b4", "appId": "1:499010556073:ios:95c37c0db1a3b5b5db99b8", "uploadDebugSymbols": true, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "castle-restaurant-cc4b4", "appId": "1:499010556073:ios:4065ed28e4bda3b1db99b8", "uploadDebugSymbols": true, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "castle-restaurant-cc4b4", "configurations": {"android": "1:499010556073:android:2d27bcc7d2dba4b1db99b8", "ios": "1:499010556073:ios:95c37c0db1a3b5b5db99b8", "macos": "1:499010556073:ios:4065ed28e4bda3b1db99b8", "web": "1:499010556073:web:d0344a97db92f878db99b8", "windows": "1:499010556073:web:3c61c8027d661317db99b8"}}}}}}