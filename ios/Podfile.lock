PODS:
  - AppAuth (1.6.2):
    - AppAuth/Core (= 1.6.2)
    - AppAuth/ExternalUserAgent (= 1.6.2)
  - AppAuth/Core (1.6.2)
  - AppAuth/ExternalUserAgent (1.6.2):
    - AppAuth/Core
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info (0.0.1):
    - Flutter
  - Firebase/Analytics (10.15.0):
    - Firebase/Core
  - Firebase/Auth (10.15.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.15.0)
  - Firebase/Core (10.15.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.15.0)
  - Firebase/CoreOnly (10.15.0):
    - FirebaseCore (= 10.15.0)
  - Firebase/Crashlytics (10.15.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.15.0)
  - Firebase/Messaging (10.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.15.0)
  - Firebase/RemoteConfig (10.15.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 10.15.0)
  - firebase_analytics (10.5.0):
    - Firebase/Analytics (= 10.15.0)
    - firebase_core
    - Flutter
  - firebase_auth (4.10.0):
    - Firebase/Auth (= 10.15.0)
    - firebase_core
    - Flutter
  - firebase_core (2.16.0):
    - Firebase/CoreOnly (= 10.15.0)
    - Flutter
  - firebase_crashlytics (3.3.6):
    - Firebase/Crashlytics (= 10.15.0)
    - firebase_core
    - Flutter
  - firebase_messaging (14.6.8):
    - Firebase/Messaging (= 10.15.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (4.2.6):
    - Firebase/RemoteConfig (= 10.15.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (10.15.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAnalytics (10.15.0):
    - FirebaseAnalytics/AdIdSupport (= 10.15.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.15.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAppCheckInterop (10.15.0)
  - FirebaseAuth (10.15.0):
    - FirebaseAppCheckInterop (~> 10.0)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
    - RecaptchaInterop (~> 100.0)
  - FirebaseCore (10.15.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreExtension (10.15.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.15.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.15.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.15.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.15.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseRemoteConfig (10.15.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseSessions (10.15.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.10)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_ringtone_player (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - GoogleMaps (< 8.0)
  - google_sign_in_ios (0.0.1):
    - Flutter
    - GoogleSignIn (~> 6.2)
  - GoogleAppMeasurement (10.15.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.15.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.15.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.2.5):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (6.2.1):
    - GoogleMaps/Maps (= 6.2.1)
  - GoogleMaps/Base (6.2.1)
  - GoogleMaps/Maps (6.2.1):
    - GoogleMaps/Base
  - GoogleSignIn (6.2.4):
    - AppAuth (~> 1.5)
    - GTMAppAuth (~> 1.3)
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
  - GoogleUtilities/AppDelegateSwizzler (7.11.5):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.5):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.5):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.11.5):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.11.5):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.5)"
  - GoogleUtilities/Reachability (7.11.5):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.11.5):
    - GoogleUtilities/Logger
  - GTMAppAuth (1.3.1):
    - AppAuth/Core (~> 1.6)
    - GTMSessionFetcher/Core (< 3.0, >= 1.5)
  - GTMSessionFetcher/Core (2.3.0)
  - libphonenumber (0.0.1):
    - Flutter
    - libPhoneNumber-iOS
  - libPhoneNumber-iOS (0.9.15)
  - location (0.0.1):
    - Flutter
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - onesignal_flutter (5.0.3):
    - Flutter
    - OneSignalXCFramework (= 5.0.2)
  - OneSignalXCFramework (5.0.2):
    - OneSignalXCFramework/OneSignalComplete (= 5.0.2)
  - OneSignalXCFramework/OneSignal (5.0.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalComplete (5.0.2):
    - OneSignalXCFramework/OneSignal
    - OneSignalXCFramework/OneSignalInAppMessages
    - OneSignalXCFramework/OneSignalLocation
  - OneSignalXCFramework/OneSignalCore (5.0.2)
  - OneSignalXCFramework/OneSignalExtension (5.0.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalInAppMessages (5.0.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLocation (5.0.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalNotifications (5.0.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOSCore (5.0.2):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalOutcomes (5.0.2):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalUser (5.0.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - platform_device_id (0.0.1):
    - Flutter
  - PromisesObjC (2.3.1)
  - PromisesSwift (2.3.1):
    - PromisesObjC (= 2.3.1)
  - ReachabilitySwift (5.0.0)
  - RecaptchaInterop (100.0.0)
  - share_plus (0.0.1):
    - Flutter
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info (from `.symlinks/plugins/device_info/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - Flutter (from `Flutter`)
  - flutter_ringtone_player (from `.symlinks/plugins/flutter_ringtone_player/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/ios`)
  - libphonenumber (from `.symlinks/plugins/libphonenumber/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - onesignal_flutter (from `.symlinks/plugins/onesignal_flutter/ios`)
  - OneSignalXCFramework (< 6.0, >= 5.0.0)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - platform_device_id (from `.symlinks/plugins/platform_device_id/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfig
    - FirebaseSessions
    - FMDB
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - libPhoneNumber-iOS
    - nanopb
    - OneSignalXCFramework
    - PromisesObjC
    - PromisesSwift
    - ReachabilitySwift
    - RecaptchaInterop

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info:
    :path: ".symlinks/plugins/device_info/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  Flutter:
    :path: Flutter
  flutter_ringtone_player:
    :path: ".symlinks/plugins/flutter_ringtone_player/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/ios"
  libphonenumber:
    :path: ".symlinks/plugins/libphonenumber/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  onesignal_flutter:
    :path: ".symlinks/plugins/onesignal_flutter/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  platform_device_id:
    :path: ".symlinks/plugins/platform_device_id/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  AppAuth: 3bb1d1cd9340bd09f5ed189fb00b1cc28e1e8570
  connectivity_plus: 07c49e96d7fc92bc9920617b83238c4d178b446a
  device_info: d7d233b645a32c40dfdc212de5cf646ca482f175
  Firebase: 66043bd4579e5b73811f96829c694c7af8d67435
  firebase_analytics: 84fbf469b4964adb5410e25f3f45433dac480ccf
  firebase_auth: ca31bca59224dc727ea0df7f066f30158a8acc3b
  firebase_core: 77172d0a9d8d19d07606e24406e4c2fc14d3265b
  firebase_crashlytics: 4365238d33a7a43164f1049f892365de4aee999f
  firebase_messaging: 6aff54d420b7ce7080c26dd131b08bc632666852
  firebase_remote_config: 1c6248049dbcb610764f5ccf567674b2ebb20813
  FirebaseABTesting: 7fa3bca17f79ac433301d20d5cd33401f7738dca
  FirebaseAnalytics: 47cef43728f81a839cf1306576bdd77ffa2eac7e
  FirebaseAppCheckInterop: a8c555b1c2db1d9445e6c3a08a848167ddb7eb51
  FirebaseAuth: a55ec5f7f8a5b1c2dd750235c1bb419bfb642445
  FirebaseCore: 2cec518b43635f96afe7ac3a9c513e47558abd2e
  FirebaseCoreExtension: d3f1ea3725fb41f56e8fbfb29eeaff54e7ffb8f6
  FirebaseCoreInternal: 2f4bee5ed00301b5e56da0849268797a2dd31fb4
  FirebaseCrashlytics: a83f26fb922a3fe181eb738fb4dcf0c92bba6455
  FirebaseInstallations: cae95cab0f965ce05b805189de1d4c70b11c76fb
  FirebaseMessaging: 0c0ae1eb722ef0c07f7801e5ded8dccd1357d6d4
  FirebaseRemoteConfig: 64b6ada098c649304114a817effd7e5f87229b11
  FirebaseSessions: ee59a7811bef4c15f65ef6472f3210faa293f9c8
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_ringtone_player: 15eba85187230b87b2512f0e1b92225618bc03e7
  flutter_secure_storage: 23fc622d89d073675f2eaa109381aefbcf5a49be
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  google_maps_flutter_ios: abdac20d6ce8931f6ebc5f46616df241bfaa2cfd
  google_sign_in_ios: 1256ff9d941db546373826966720b0c24804bcdd
  GoogleAppMeasurement: 722db6550d1e6d552b08398b69a975ac61039338
  GoogleDataTransport: 54dee9d48d14580407f8f5fbf2f496e92437a2f2
  GoogleMaps: 20d7b12be49a14287f797e88e0e31bc4156aaeb4
  GoogleSignIn: 5651ce3a61e56ca864160e79b484cd9ed3f49b7a
  GoogleUtilities: 13e2c67ede716b8741c7989e26893d151b2b2084
  GTMAppAuth: 0ff230db599948a9ad7470ca667337803b3fc4dd
  GTMSessionFetcher: 3a63d75eecd6aa32c2fc79f578064e1214dfdec2
  libphonenumber: bd14d5abfc5939ab0b0767234e2d8e2593af258e
  libPhoneNumber-iOS: 0a32a9525cf8744fe02c5206eb30d571e38f7d75
  location: d5cf8598915965547c3f36761ae9cc4f4e87d22e
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  onesignal_flutter: fa2984cb96f6aba3646dfef4d27abbcc94f382dc
  OneSignalXCFramework: d47a350b15e106a5dc310e3f97867e28dad6589e
  package_info_plus: fd030dabf36271f146f1f3beacd48f564b0f17f7
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  platform_device_id: 81b3e2993881f87d0c82ef151dc274df4869aef5
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  PromisesSwift: 28dca69a9c40779916ac2d6985a0192a5cb4a265
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  share_plus: 599aa54e4ea31d4b4c0e9c911bcc26c55e791028
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  sqflite: 31f7eba61e3074736dff8807a9b41581e4f7f15a
  url_launcher_ios: 08a3dfac5fb39e8759aeb0abbd5d9480f30fc8b4

PODFILE CHECKSUM: 5b07b19f56777cfe7edba903606052870a5fe3db

COCOAPODS: 1.13.0
