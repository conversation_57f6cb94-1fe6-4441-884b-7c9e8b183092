import 'dart:io';

import 'package:firebase_core/firebase_core.dart';

import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:matjari/cuatome_http_override.dart';

import 'firebase_options.dart';
import 'src/app.dart';
import 'src/core/values/boxes.dart';
import 'src/data/models/hive/cart_item.dart';
import 'src/data/models/hive/product.dart';

import 'package:flutter/services.dart';
import 'package:google_maps_flutter_android/google_maps_flutter_android.dart';
import 'package:google_maps_flutter_platform_interface/google_maps_flutter_platform_interface.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  SystemChrome.setPreferredOrientations(
      [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);

  //init hive
  await Hive.initFlutter();

  //register adapterS
  Hive.registerAdapter(CartItemAdapter());
  Hive.registerAdapter(ProductAdapter());

  // cart box
  await Hive.openBox<CartItem>(Boxes.cart);
  // favorites box
  await Hive.openBox<Product>(Boxes.favorites);
  // app cache box
  await Hive.openBox(Boxes.appCache);
  // settings box
  Hive.openBox<String>(Boxes.settings);
//http override
  HttpOverrides.global = CustomHttpOverrides();

  // Require Hybrid Composition mode on Android.
  final GoogleMapsFlutterPlatform mapsImplementation =
      GoogleMapsFlutterPlatform.instance;
  if (mapsImplementation is GoogleMapsFlutterAndroid) {
    mapsImplementation.useAndroidViewSurface = true;
  }

  runApp(const MatjariApp());
}
