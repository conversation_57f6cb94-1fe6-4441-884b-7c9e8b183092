// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAseuOQfHSYME4vGkikW51d_m8YeLrZk2g',
    appId: '1:499010556073:web:d0344a97db92f878db99b8',
    messagingSenderId: '499010556073',
    projectId: 'castle-restaurant-cc4b4',
    authDomain: 'castle-restaurant-cc4b4.firebaseapp.com',
    storageBucket: 'castle-restaurant-cc4b4.firebasestorage.app',
    measurementId: 'G-X8MC1S52T4',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAOb2GSGQ_XjkvHdV44dKTYGCzHv_n-snQ',
    appId: '1:499010556073:android:2d27bcc7d2dba4b1db99b8',
    messagingSenderId: '499010556073',
    projectId: 'castle-restaurant-cc4b4',
    storageBucket: 'castle-restaurant-cc4b4.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDF71jQbVivbgne8KfcuKz64JN0llqh6q0',
    appId: '1:499010556073:ios:95c37c0db1a3b5b5db99b8',
    messagingSenderId: '499010556073',
    projectId: 'castle-restaurant-cc4b4',
    storageBucket: 'castle-restaurant-cc4b4.firebasestorage.app',
    iosBundleId: 'app.qirtas',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDF71jQbVivbgne8KfcuKz64JN0llqh6q0',
    appId: '1:499010556073:ios:4065ed28e4bda3b1db99b8',
    messagingSenderId: '499010556073',
    projectId: 'castle-restaurant-cc4b4',
    storageBucket: 'castle-restaurant-cc4b4.firebasestorage.app',
    iosBundleId: 'com.qirtas',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAseuOQfHSYME4vGkikW51d_m8YeLrZk2g',
    appId: '1:499010556073:web:3c61c8027d661317db99b8',
    messagingSenderId: '499010556073',
    projectId: 'castle-restaurant-cc4b4',
    authDomain: 'castle-restaurant-cc4b4.firebaseapp.com',
    storageBucket: 'castle-restaurant-cc4b4.firebasestorage.app',
    measurementId: 'G-BM5M540LJC',
  );

}