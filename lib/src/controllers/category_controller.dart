import 'package:get/get.dart';
import 'package:matjari/src/data/providers/api/api_provider.dart';
import 'package:matjari/src/data/services/network_service.dart';

import '../core/utils/common_functions.dart';
import '../core/values/app_config.dart';
import '../data/enums/page_status.dart';
import '../data/models/hive/product.dart';

class CategoryController extends GetxController {
  //first page status
  var firtPageStatus = LoadingStatus.loaded.obs;
  //next page status
  var nextPageStatus = LoadingStatus.loaded.obs;
  ApiProvider apiProvider = AppConfigs.apiProvider;
  // construtor
  CategoryController({required int id}) {
    currentId.value = id;
  }
  var currentId = 0.obs;
  // static instance
  static CategoryController get instance {
    if (!Get.isRegistered<CategoryController>(tag: Get.arguments.toString())) {
      Get.put(CategoryController(id: Get.arguments),
          tag: Get.arguments.toString());
    }
    return Get.find(tag: Get.arguments.toString());
  }

  // products
  var products = Rx<List<Product>>([]);
//current page
  int currentPage = 0;
//max page
  int maxPage = 1;
  // get categry products
  Future<void> getCategoryProducts([int page = 1]) async {
    if (page == 1) {
      currentPage = 0;
      products.value = [];
    }

    // check internet connection
    NetworkService.instance.checkConnectivity(() async {
      try {
        if (currentPage == 0) {
          firtPageStatus.value = LoadingStatus.loading;
        } else {
          nextPageStatus.value = LoadingStatus.loading;
        }
        // get products
        var cId = currentId.value;
        var response = await apiProvider.getCategoryProducts(cId, page: page);
        if (response.status == 0) {
          Get.log(
              'get category products success ${response.result.products.length}');
          if (currentId.value != cId) {
            return;
          }

          currentPage = response.result.currentPage;
          maxPage = response.result.maxPage;
          products.value.addAll(response.result.products);

          products.refresh();

          if (currentPage == 1) {
            firtPageStatus.value = LoadingStatus.loaded;
          } else {
            nextPageStatus.value = LoadingStatus.loaded;
          }
        } else {
          CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب المنتجات");
          if (currentPage == 0) {
            firtPageStatus.value = LoadingStatus.error;
          } else {
            nextPageStatus.value = LoadingStatus.error;
          }
        }
      } catch (e) {
        if (currentPage == 0) {
          firtPageStatus.value = LoadingStatus.error;
        } else {
          nextPageStatus.value = LoadingStatus.error;
        }
      }
    }, () {
      if (currentPage == 0) {
        firtPageStatus.value = LoadingStatus.networkError;
      }
    });
  }

  @override
  void onInit() {
    getCategoryProducts();
    currentId.listen((id) async {
      await getCategoryProducts();
      products.refresh();
    });
    super.onInit();
  }
}
