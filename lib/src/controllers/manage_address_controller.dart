import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:matjari/src/controllers/address_controller.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/data/enums/page_status.dart';
import 'package:matjari/src/data/models/area.dart';
import 'package:matjari/src/data/models/city.dart';
import 'package:matjari/src/data/models/country.dart';
import 'package:matjari/src/data/providers/api/api_provider.dart';
import 'package:matjari/src/data/services/loading_service.dart';
import 'package:matjari/src/data/services/network_service.dart';
import 'package:matjari/src/data/vms/address_vm.dart';

import '../core/values/app_config.dart';
import '../data/models/address.dart';

class ManageAddressController extends GetxController {
  var countriesLoading = LoadingStatus.initial.obs;
  var citiesLoading = LoadingStatus.initial.obs;
  var areasLoading = LoadingStatus.initial.obs;
  ApiProvider apiProvider = AppConfigs.apiProvider;
  ManageAddressController({this.address});
  // static instance
  static ManageAddressController get instance => Get.find();

  // address object for edit
  final Address? address;

  // default camera position
  final CameraPosition _kGooglePlex = const CameraPosition(
    target: LatLng(15.650916087643456, 44.164914675056934),
    zoom: 6.858909606933594,
  );
  // initail camera position
  late Rx<CameraPosition> intialCameraPosition =
      Rx<CameraPosition>(_kGooglePlex);
  // country list
  late Rx<List<Country>?> countryList = Rx<List<Country>?>(null);
  // cities list
  late Rx<List<City>?> citiesList = Rx<List<City>?>(null);
  // areas list
  late Rx<List<Area>?> areasList = Rx<List<Area>?>(null);
  // selected country
  late Rx<int?> selectedCountry = Rx<int?>(null);
  // selected city
  late Rx<int?> selectedCity = Rx<int?>(null);
  // selected area
  late Rx<int?> selectedArea = Rx<int?>(null);

  void getAllCountries() async {
    NetworkService.instance.checkConnectivity(() async {
      countriesLoading.value = LoadingStatus.loading;
      var response = await apiProvider.getAllCountries();
      if (response.status == 0) {
        countryList.value = response.countries;
        countriesLoading.value = LoadingStatus.loaded;
      } else {
        CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب البلدان");
        countriesLoading.value = LoadingStatus.error;
      }
    });
  }

  void getCountryCities(int? countryId) async {
    if (countryId != null && countryId != selectedCountry.value) {
      selectedCountry.value = countryId;
      citiesList.value = null;
      areasList.value = null;
      selectedCity.value = null;
      selectedArea.value = null;
      citiesLoading.value = LoadingStatus.loading;
      NetworkService.instance.checkConnectivity(() async {
        var response = await apiProvider.getAllCities(countryId);
        if (response.status == 0) {
          citiesList.value = response.cities;
          citiesLoading.value = LoadingStatus.loaded;
          Get.log("cities loaded");
        } else {
          CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب المدن");
          citiesLoading.value = LoadingStatus.error;
        }
      });
    }
  }

  void getCityAreas(int? cityId) async {
    Get.log("city id: $cityId");
    if (cityId != null && cityId != selectedCity.value) {
      selectedCity.value = cityId;
      selectedArea.value = null;
      areasList.value = null;
      areasLoading.value = LoadingStatus.loading;
      NetworkService.instance.checkConnectivity(() async {
        var response = await apiProvider.getAllAreas(cityId);
        print("response.status: ${response.status} cityId: $cityId");
        if (response.status == 0) {
          areasList.value = response.areas;
          areasLoading.value = LoadingStatus.loaded;
          Get.log("areas loaded");
        } else if (response.status == 1) {
          areasList.value = [];
          areasLoading.value = LoadingStatus.loaded;
          if (response.message != null) {
            CommonFunctions.showErrorMessage(response.message ?? "");
          }
          Get.log("areas loaded");
        } else {
          CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب المناطق");
          areasLoading.value = LoadingStatus.error;
        }
      });
    }
  }

  // add address
  void addAddress(AddressVm addressVm) async {
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          LoadingService.instance.show();
          var response = await apiProvider.addAddress(addressVm.toFormData());
          LoadingService.instance.hide();
          if (response.status == 0) {
            AddressController.instance.addAddress(response.address!);
            if (kDebugMode) {
              print(response.message);
            }
            // show success message
            Get.back();
            Get.back();
            CommonFunctions.showSuccessMessage(response.message);
          } else {
            CommonFunctions.showErrorMessage("حصل خطأ أثناء إضافة العنوان");
          }
        } catch (e) {
          LoadingService.instance.hide();
          CommonFunctions.handleError(e);
        }
      },
    );
  }

  // update address
  void updateAddress(AddressVm addressVm) async {
    addressVm.addressId = address?.addressId;
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          LoadingService.instance.show();
          var response = await apiProvider.editAddress(addressVm.toFormData());
          LoadingService.instance.hide();
          print(response.toJson());
          if (response.status == 0) {
            AddressController.instance.updateAddress(response.address!);
            if (kDebugMode) {
              print(response.message);
            }
            // show success message
            Get.back();
            Get.back();
            CommonFunctions.showSuccessMessage(response.message);
          } else {
            CommonFunctions.showErrorMessage("حصل خطأ أثناء تعديل العنوان");
          }
        } catch (e) {
          print(e);
          LoadingService.instance.hide();
          CommonFunctions.handleError(e);
        }
      },
    );
  }

  void manageAddress(AddressVm addressVm) async {
    if (address != null) {
      updateAddress(addressVm);
    } else {
      addAddress(addressVm);
    }
  }

  @override
  void onInit() {
    getAllCountries();
    print("address: ${address?.toJson()}");
    if (address != null) {
      getCountryCities(address!.countryId);
      getCityAreas(address!.cityId);
      selectedCountry.value = address!.countryId;
      selectedCity.value = address!.cityId;
      selectedArea.value = address!.areaId;
    } else {
      getCountryCities(235);
    }
    super.onInit();
  }
}
