import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide FormData;
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/values/app_config.dart';
import 'package:matjari/src/data/models/order.dart';
import 'package:matjari/src/data/models/payment_method.dart';
import 'package:matjari/src/data/providers/api/api_provider.dart';
import 'package:matjari/src/data/services/auth_services.dart';
import 'package:matjari/src/data/services/init_service.dart';
import 'package:matjari/src/data/services/network_service.dart';
import 'package:matjari/src/view/components/bottom_sheets/confirm_bottom_sheet.dart';
import 'package:matjari/src/view/components/bottom_sheets/payment_confirm_bottom_sheet.dart';

import '../data/enums/page_status.dart';
import '../data/services/loading_service.dart';
import '../data/vms/add_payment_info_vm.dart';
import 'order_controller.dart';

class OrderDetailsController extends GetxController {
  // page status
  Rx<LoadingStatus> pageStatus = LoadingStatus.loaded.obs;

  // static instance
  static OrderDetailsController get instance => Get.find(tag: tag);

//get argument from prev page
  var order = Rx<Order?>(null);

//controller tag

  static String get tag {
    return (Get.arguments is Map
        ? Get.arguments["order_id"].toString()
        : Get.arguments?.orderId.toString() ?? Random().toString());
  }

  //page title
  var pageTitle = Rx<String>(Get.arguments is Map
      ? Get.arguments["order_id"].toString()
      : Get.arguments?.orderId.toString() ?? "");

  // payment methods
  Rx<List<PaymentMethod>> paymentMethods = Rx<List<PaymentMethod>>([]);

  @override
  void onInit() async {
    await getOrder();
    getPaymentMethods();

    // //confirm payment
    if ((!(Get.arguments is Map && Get.arguments['message'] != null))) {
      confirmPaymentBottomSheet();
    } else if ((Get.arguments is Map && Get.arguments['message'] != null)) {
      confirmBotoomSheet(Get.arguments['message'].replaceAll("\\n", " "));
    }
    super.onInit();
  }

  Future<void> getOrder([int? orderId]) async {
    if (Get.arguments != null) {
      if (Get.arguments is Map) {
        await _getOrder(
            orderId ?? int.parse(Get.arguments["order_id"].toString()));
      } else {
        // order = Get.arguments;
        await _getOrder(orderId ?? int.parse(Get.arguments.orderId.toString()));
      }
    } else if (orderId != null) {
      await _getOrder(orderId);
    }
  }

  void confirmBotoomSheet(String message) {
    Get.bottomSheet(
      ConfirmBottomSheet(
          title: "تاكيد العملية",
          message: message,
          onConfirm: () {
            Get.back();
            confirmPaymentBottomSheet();
          }),
    );
  }

  void confirmPaymentBottomSheet() {
    if (order.value?.orderPaymentType == 4) {
      if (((order.value?.orderPaymentStatus ?? 0) < 3) &&
          (order.value?.orderStatus ?? 0) <= 1) {
        Get.bottomSheet(
            PaymentConfirmBottomSheet(
              order: order.value,
              onConfirm: (code, transferNumber, transferCompany) {
                confirmPayment(order.value?.orderId ?? 0, code);
              },
              titleText: order.value?.orderPaymentId == 6 ||
                      order.value?.orderPaymentId == 10
                  ? "voucher_code".tr
                  : "code".tr,
              hintText: order.value?.orderPaymentId == 6 ||
                      order.value?.orderPaymentId == 10
                  ? "voucher_code_hint"
                      .trParams({"name": order.value?.orderPaymentName ?? ""})
                  : "code_hint"
                      .trParams({"name": order.value?.orderPaymentName ?? ""}),
            ),
            isDismissible: false);
      }
    }
  }

  void confirmPayment(int orderId, String code) {
    NetworkService.instance.checkConnectivity(() async {
      try {
        LoadingService.instance.show();

        var response = await AppConfigs.apiProvider.sendConfirmPayment(
            FormData.fromMap({"order_id": orderId, "code": code}));
        LoadingService.instance.hide();
        //close bottomsheet

        if (response.data["status"] == 0) {
          CommonFunctions.showSuccessMessage(response.data["message"]);
          order.value = Order.fromJson(response.data["result"]);
          order.refresh();
        } else {
          CommonFunctions.showErrorMessage(response.data["message"]);
        }
      } catch (e) {
        LoadingService.instance.hide();
        Get.back();

        CommonFunctions.handleError(e);
      }
    });
  }

  // get order
  Future<void> _getOrder(int orderId) async {
    pageStatus.value = LoadingStatus.loading;
    await NetworkService.instance.checkConnectivity(() async {
      try {
        var apiProvider = AppConfigs.apiProvider;
        var response = await apiProvider.getOrder(orderId);

        // Get.log("response: ${response.toJson()}");
        if (response.data["status"] == 0) {
          order.value = Order.fromJson(response.data["result"]);
          pageTitle.value = order.value?.orderId.toString() ?? "";
          pageStatus.value = LoadingStatus.loaded;
        } else if (response.data["status"] == 1) {
          // order.value = ;
          pageStatus.value = LoadingStatus.loaded;
        } else if (response.data["status"] == 2) {
          CommonFunctions.showErrorMessage(response.data["message"]);
          pageStatus.value = LoadingStatus.error;
        }
      } catch (e) {
        pageStatus.value = LoadingStatus.error;
      }
    }, () {
      pageStatus.value = LoadingStatus.networkError;
    });
  }

  Future<void> cancelOrder(int orderId) async {
    await NetworkService.instance.checkConnectivity(() async {
      try {
        LoadingService.instance.show();

        var response = await AppConfigs.apiProvider.cancelOrder(orderId);
        // Get.log('response: ${response.response()}');
        LoadingService.instance.hide();

        if (response.data["status"] == 0) {
          Get.back();

          //refresh orders if order page is open
          if (Get.isRegistered<OrderController>()) {
            await OrderController.instance.getOrders();
          }

          CommonFunctions.showSuccessMessage(response.data["message"]);
        } else {
          CommonFunctions.showErrorMessage(response.data["message"]);
        }
      } catch (e) {
        LoadingService.instance.hide();
        Get.back();

        CommonFunctions.handleError(e);
      }
    });
  }

  Future<void> addPaymentInfo(AddPaymentInfoVm addPaymentInfoVm) async {
    await NetworkService.instance.checkConnectivity(() async {
      try {
        LoadingService.instance.show();

        var response = await AppConfigs.apiProvider
            .addPaymentInfo(addPaymentInfoVm.toFormData());
        LoadingService.instance.hide();
        //close bottomsheet
        Get.back();

        if (response.data["status"] == 0) {
          await getOrder(order.value?.orderId ?? 0);

          // //change order paymentmethod
          // order.value?.orderPaymentId = addPaymentInfoVm.paymentId;
          // order.value?.orderPaymentTransferCompany =
          //     addPaymentInfoVm.transferCompany ?? "";

          // order.value?.orderPaymentTransferNumber =
          //     addPaymentInfoVm.transferNumber ?? "";
          // //get the payment method
          // var payment = InitService.instance.paymentMethods.value
          //     .where(
          //         (element) => element.paymentId == addPaymentInfoVm.paymentId)
          //     .first;
          // order.value?.orderPaymentName = payment.paymentName;
          // order.value?.orderPaymentType = payment.paymentType;

          // order.refresh();

          // //change order paymentmethod in orderController
          // if (Get.isRegistered<OrderController>()) {
          //   //get the index of  oredre that we change it payment method

          //   var index = OrderController.instance.orders.value.indexWhere(
          //       (order) => order.orderId == this.order.value?.orderId);

          //   OrderController.instance.orders.value[index] = order.value!;
          // }

          if ((order.value?.orderPaymentId ?? 0) == 5) {
            confirmBotoomSheet(
                (response.data['message'] as String).replaceAll("\\n", " "));
          } else {
            //show success message
            CommonFunctions.showSuccessMessage(response.data["message"]);
            confirmPaymentBottomSheet();
          }
        } else {
          CommonFunctions.showErrorMessage(response.data["message"]);
        }
      } catch (e) {
        LoadingService.instance.hide();
        Get.back();

        CommonFunctions.handleError(e);
      }
    });
  }

  Future<void> getPaymentMethods() async {
    await NetworkService.instance.checkConnectivity(
      () async {
        var dioInstance = AppConfigs.dioInstance;
        dioInstance.options.queryParameters = {
          'language': Get.locale?.languageCode,
          'token': AuthService.instance.user.value?.token,
          'device_id': AuthService.instance.deviceId.value,
          'app_key': AppConfigs.appKey,
          'currency_id': order.value?.orderCountryId
        };
        var response = await ApiProvider(dioInstance)
            .getPaymentMethods(order.value?.orderAddressId);

        Get.log('response: ${response.toJson()}');
        if (response.status == 0) {
          paymentMethods.value = response.paymentMethods;
        } else if (response.status == 1) {
          paymentMethods.value = [];
        } else {
          // CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب طرق الدفع");
        }
      },
      () {},
    );
  }
}
