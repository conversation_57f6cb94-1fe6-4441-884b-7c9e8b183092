import 'dart:math';

import 'package:get/get.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/values/app_config.dart';
import 'package:matjari/src/data/services/network_service.dart';

import '../data/enums/page_status.dart';
import '../data/models/hive/product.dart';

class ProductDetailsController extends GetxController {
  // page status
  Rx<LoadingStatus> pageStatus = LoadingStatus.loaded.obs;

  // static instance
  // static ProductDetailsController get instance => Get.find();

//get argument from prev page
  Product? product;

//tag for this instance of controller
  static String get tag {
    if (Get.arguments is Map) {
      return Get.arguments.toString();
    } else {
      return Get.arguments?.id.toString() ?? Random().toString();
    }
  }

  @override
  void onInit() {
    if (Get.arguments is Map) {
      getProduct(Get.arguments["product_id"]);
    } else {
      product = Get.arguments;
    }

    super.onInit();

    //increment visitis count
    visitProduct();
  }

  // get product
  void getProduct(int productId) {
    pageStatus.value = LoadingStatus.loading;
    NetworkService.instance.checkConnectivity(() async {
      var apiProvider = AppConfigs.apiProvider;
      var response = await apiProvider.getProduct(productId);

      Get.log("response: ${response.data}");
      if (response.data["status"] == 0) {
        product = Product.fromJson(response.data["result"]);
        pageStatus.value = LoadingStatus.loaded;
      } else if (response.data["status"] == 1) {
        // order.value = ;
        pageStatus.value = LoadingStatus.error;
      } else if (response.data["status"] == 2) {
        CommonFunctions.showErrorMessage(response.data["message"]);
        pageStatus.value = LoadingStatus.error;
      }
    }, () {
      pageStatus.value = LoadingStatus.networkError;
    });
  }

  void visitProduct() {
    try {
      //increment visitis count
      AppConfigs.apiProvider.visitProduct(product?.id);
    // ignore: empty_catches
    } catch (e) {}
  }
}
