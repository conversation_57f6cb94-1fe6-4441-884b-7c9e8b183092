import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/data/enums/page_status.dart';
import 'package:matjari/src/data/models/configurations.dart';
import 'package:matjari/src/data/models/page_info.dart';
import 'package:matjari/src/data/services/network_service.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../core/values/app_config.dart';

class AboutAppController extends GetxController {
  // static instance
  static AboutAppController get instance => Get.find();
  var apiProvider = AppConfigs.apiProvider;
  var pageStatus = LoadingStatus.loading.obs;
  var configurations = Rx<Configurations?>(Configurations.defaultValue());
  var pages = Rx<List<PageInfo>>([]);

  // app version
  var appVersion = ''.obs;

  // app buildnumber
  var buildnumber = 0.obs;
  Future<void> checkAppVersion() async {
    PackageInfo.fromPlatform().then((value) {
      appVersion.value = value.version;
    });
  }

  Future<void> checkAppBuildNumber() async {
    PackageInfo.fromPlatform().then((value) {
      buildnumber.value = int.parse(value.buildNumber);
    });
  }

  // get about info
  Future<void> getAboutInfo() async {
    pageStatus.value = LoadingStatus.loading;
    // check connectivity
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response = await apiProvider.getAboutInfo();
          if (response.status == 0) {
            pages.value = response.pagesInfo;
            // configurations.value = response.result.configurations;
            Get.log(configurations.value?.website ?? "");
            pageStatus.value = LoadingStatus.loaded;
          } else {
            pageStatus.value = LoadingStatus.error;
          }
        } catch (e) {
          pageStatus.value = LoadingStatus.error;
        }
      },
      () {
        pageStatus.value = LoadingStatus.networkError;
      },
    );
  }


  // get about info
  Future<void> getConfiguration() async {
    pageStatus.value = LoadingStatus.loading;
    // check connectivity
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response = await apiProvider.getConfigurations();
          if (response.status == 0) {
            // pages.value = response.configurations;
            configurations.value = response.configurations;
            Get.log(configurations.value?.website ?? "");
            pageStatus.value = LoadingStatus.loaded;
          } else {
            pageStatus.value = LoadingStatus.error;
          }
        } catch (e) {
          pageStatus.value = LoadingStatus.error;
        }
      },
      () {
        pageStatus.value = LoadingStatus.networkError;
      },
    );
  }


  void rateApp() {
    if (defaultTargetPlatform == TargetPlatform.android) {
      CommonFunctions.openUrl(
          "https://play.google.com/store/apps/details?id=app.qirtas");
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      CommonFunctions.openUrl(
          "https://itunes.apple.com/us/app/matjari-food-delivery-app/id1498420000?mt=8");
    }
  }

  @override
  void onInit() async {
    checkAppVersion();
    checkAppBuildNumber();
    getAboutInfo();
    getConfiguration();

    super.onInit();
  }
}
