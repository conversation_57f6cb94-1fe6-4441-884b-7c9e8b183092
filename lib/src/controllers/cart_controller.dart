import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/home_controller.dart';
import 'package:matjari/src/core/values/app_config.dart';
import 'package:matjari/src/data/models/payment_method.dart';
import 'package:matjari/src/data/models/responses/apply_coupon_response.dart';
import 'package:matjari/src/data/providers/api/api_provider.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';
import 'package:matjari/src/data/vms/apply_coupon_vm.dart';

import 'package:matjari/src/data/vms/order_vm.dart';

import '../core/routes/app_pages.dart';
import '../core/utils/common_functions.dart';
import '../data/models/address.dart';
import '../data/models/delivery_time.dart';
import '../data/services/loading_service.dart';
import '../data/services/network_service.dart';

class CartController extends GetxController {
  ApiProvider get apiProvider => AppConfigs.apiProvider;
  // static instance
  static CartController get instance => Get.find();
  // selected address
  final Rx<Address?> selectedAddress = Rx<Address?>(null);
  // payment methods
  final Rx<List<PaymentMethod>> paymentMethods = Rx<List<PaymentMethod>>([]);
  // delivery times
  final Rx<List<DeliveryTime>> deliveryTimes = Rx<List<DeliveryTime>>([]);
  // get payment methods
  Future<void> getPaymentMethods([int? addressId]) async {
    await NetworkService.instance.checkConnectivity(() async {
      var response = await apiProvider
          .getPaymentMethods(addressId ?? selectedAddress.value?.addressId);
      Get.log('response: ${response.toJson()}');
      if (response.status == 0) {
        paymentMethods.value = response.paymentMethods;
      } else if (response.status == 1) {
        paymentMethods.value = [];
      } else {
        CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب طرق الدفع");
      }
    });
  }

  // get delivery times
  Future<void> getDeliveryTimes() async {
    await NetworkService.instance.checkConnectivity(() async {
      var response = await apiProvider.getDeliveryTimes();
      Get.log('response: ${response.toJson()}');
      if (response.status == 0) {
        deliveryTimes.value = response.deliveryTimes;
      } else if (response.status == 1) {
        deliveryTimes.value = [];
      } else {
        CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب وقت التوصيل");
      }
    });
  }

  // add order
  Future<void> addOrder(OrderVM orderVM) async {
    final cartItems = HiveProvider.allCartItems;

    if (cartItems.fold<double>(0.0, (total, item) => total + item.total) <
        (HomeController.instance.minOrderPrice ?? 0)) {
      Get.showSnackbar(
        GetSnackBar(
          backgroundColor: Colors.red,
          // borderColor: Colors.grey.shade200,
          // animationDuration:const Duration(microseconds: 1),
          borderRadius: 16,
          duration: const Duration(seconds: 3),
          margin: const EdgeInsets.only(right: 30, left: 30, bottom: 90),
          messageText: Text(
            "لا يمكن الطلب بأقل من ${HomeController.instance.minOrderPrice?.toInt() ?? ''}",
            style: const TextStyle(
                color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
          ),
        ),
      );

      return;
    }

    Get.log('orderVM: ${orderVM.toJson()}');

    await NetworkService.instance.checkConnectivity(() async {
      try {
        LoadingService.instance.show();
        var response = await apiProvider.addOrder(
          orderVM.toFormData(),
        );
        LoadingService.instance.hide();
        Get.log('response: ${response.toJson()}');
        if (response.status == 0) {
          Get.back();
          Get.back();
          if (response.order?.orderPaymentId == 5) {
            Get.toNamed(Routes.ORDER_DETAILS_PAGE, arguments: {
              "order_id": response.order?.orderId,
              'message': response.message
            });
            HiveProvider.emptyCart();
          } else {
            Get.toNamed(Routes.ORDER_DETAILS_PAGE, arguments: response.order);
            HiveProvider.emptyCart();
            CommonFunctions.showSuccessMessage(response.message);
          }
        } else if (response.status == 1) {
          CommonFunctions.showErrorMessage(response.message);
        } else if (response.status == 2) {
          Get.toNamed(Routes.LOGIN_PAGE);
          CommonFunctions.showErrorMessage(response.message);
        } else {
          CommonFunctions.showErrorMessage("حصل خطأ أثناء إضافة الطلب");
        }
      } catch (e) {
        Get.log(e.toString());
        LoadingService.instance.hide();
        CommonFunctions.handleError(e);
      }
    });
  }

  Future<ApplyCouponResult?> applayCoupon(ApplyCouponVm applyCouponVm) async {
    return await NetworkService.instance.checkConnectivity(() async {
      Get.focusScope?.unfocus();
      try {
        LoadingService.instance.show();

        var response = await apiProvider.checkCouponDiscount(
          applyCouponVm.toFormData(),
        );
        LoadingService.instance.hide();

        if (response.status == 0) {
          CommonFunctions.showSuccessMessage("تم تطبيق كوبون الخصم بنجاح");
          return response.result;
        } else {
          CommonFunctions.showErrorMessage(response.message ?? "");
        }
      } catch (e) {
        LoadingService.instance.hide();

        CommonFunctions.handleError(e);
      }
    });
  }

  @override
  void onInit() {
    getDeliveryTimes();

    super.onInit();
  }
}
