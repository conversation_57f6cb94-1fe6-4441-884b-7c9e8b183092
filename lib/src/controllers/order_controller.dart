import 'package:get/get.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/values/app_config.dart';
import 'package:matjari/src/data/enums/page_status.dart';
import 'package:matjari/src/data/models/order.dart';
import 'package:matjari/src/data/services/network_service.dart';

import '../data/models/payment_method.dart';

class OrderController extends GetxController {
  //first page status
  var firtPageStatus = LoadingStatus.loaded.obs;
  //next page status
  var nextPageStatus = LoadingStatus.initial.obs;
  // static instance
  static OrderController get instance => Get.find<OrderController>();
  // orders
  Rx<List<Order>> orders = Rx<List<Order>>([]);
  // payment methods
  Rx<List<PaymentMethod>> paymentMethods = Rx<List<PaymentMethod>>([]);
  //current page
  int currentPage = 0;
//max page
  int maxPage = 1;

  // get orders
  Future<void> getOrders([int page = 1]) async {
    if (page == 1) {
      currentPage = 0;
      orders.value.clear();
      nextPageStatus.value = LoadingStatus.initial;
    }

    if (currentPage == 0) {
      firtPageStatus.value = LoadingStatus.loading;
    } else {
      nextPageStatus.value = LoadingStatus.loading;
    }

    NetworkService.instance.checkConnectivity(() async {
      try {
        var apiProvider = AppConfigs.apiProvider;
        var response = await apiProvider.getOrders(page);
        Get.log("response: ${response.toJson()}");

        firtPageStatus.value = LoadingStatus.loaded;
        nextPageStatus.value = LoadingStatus.loaded;

        if (response.status == 0) {
          orders.value.addAll(response.orderResult?.orders ?? []);
          orders.refresh();

          currentPage = response.orderResult?.currentPage ?? 0;
          maxPage = response.orderResult?.maxPage ?? 1;
        } else if (response.status == 1) {
          orders.value = [];
        } else if (response.status == 2) {
          CommonFunctions.showErrorMessage(response.message);
        }
      } catch (e) {
        if (currentPage == 0) {
          firtPageStatus.value = LoadingStatus.error;
        } else {
          nextPageStatus.value = LoadingStatus.error;
        }
      }
    }, () {
      if (currentPage == 0) {
        firtPageStatus.value = LoadingStatus.networkError;
      } else {
        nextPageStatus.value = LoadingStatus.networkError;
      }
    });
  }

  Future<void> getPaymentMethods() async {
    await NetworkService.instance.checkConnectivity(() async {
      var response = await AppConfigs.apiProvider.getPaymentMethods();
      Get.log('response: ${response.toJson()}');
      if (response.status == 0) {
        paymentMethods.value = response.paymentMethods;
      } else if (response.status == 1) {
        paymentMethods.value = [];
      } else {
        CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب طرق الدفع");
      }
    });
  }

  @override
  void onInit() {
    getOrders();
    super.onInit();
  }
}
