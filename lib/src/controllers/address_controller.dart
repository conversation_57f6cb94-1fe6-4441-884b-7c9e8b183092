import 'package:get/get.dart';
import 'package:matjari/src/data/enums/page_status.dart';
import 'package:matjari/src/data/providers/api/api_provider.dart';
import 'package:matjari/src/data/services/loading_service.dart';

import '../core/routes/app_pages.dart';
import '../core/utils/common_functions.dart';
import '../core/values/app_config.dart';
import '../data/models/address.dart';
import '../data/services/network_service.dart';

class AddressController extends GetxController {
  final bool forSelecting;
  AddressController({this.forSelecting = false});
  // page status
  var pageStatus = LoadingStatus.loading.obs;
  ApiProvider apiProvider = AppConfigs.apiProvider;
  // static instance
  static AddressController get instance => Get.find();
  //  addresses
  final Rx<List<Address>> addresses = Rx<List<Address>>([]);
  // get all addresses
  Future<void> getAllAddresses() async {
    pageStatus.value = LoadingStatus.loading;
    await NetworkService.instance.checkConnectivity(() async {
    try {
        var response = await apiProvider.getAllAddresses();
      if (response.status == 0) {
        addresses.value = response.addresses ?? [];
        pageStatus.value = LoadingStatus.loaded;
      } else if (response.status == 1) {
        addresses.value = [];
        pageStatus.value = LoadingStatus.loaded;
      } else if (response.status == 2) {
        pageStatus.value = LoadingStatus.error;
        CommonFunctions.showErrorMessage(response.message ?? "");
        Get.toNamed(Routes.LOGIN_PAGE);
      } else {
        CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب العناوين");
        pageStatus.value = LoadingStatus.error;
      }
      
    } catch (e) {
       pageStatus.value = LoadingStatus.error;
    }
    }, () {
      pageStatus.value = LoadingStatus.networkError;
    });
  }

  // add to address list
  void addAddress(Address address) {
    addresses.value.insert(0, address);

    addresses.refresh();
  }

  // update address
  void updateAddress(Address address) {
    var index = addresses.value
        .indexWhere((element) => element.addressId == address.addressId);
    if (index != -1) {
      addresses.value[index] = address;
    }
    addresses.refresh();
  }

  // delete from address list
  void deleteFromAddressList(int id) {
    var index =
        addresses.value.indexWhere((element) => element.addressId == id);
    if (index != -1) {
      addresses.value.removeAt(index);
    }
    addresses.refresh();
  }

  // delete address
  void deleteAddress(int id) async {
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          LoadingService.instance.show();
          var response = await apiProvider.deleteAddress(id);
          LoadingService.instance.hide();
          print(
              " delete address response: ${response.status} id : $id message: ${response.message}");
          if (response.status == 0) {
            deleteFromAddressList(id);
            // show success message
            CommonFunctions.showSuccessMessage(response.message);
          } else {
            CommonFunctions.showErrorMessage("حصل خطأ أثناء حذف العناوين");
          }
        } catch (e) {
          LoadingService.instance.hide();
          CommonFunctions.handleError(e);
        }
      },
    );
  }

  @override
  void onInit() {
    getAllAddresses();
    super.onInit();
  }
}
