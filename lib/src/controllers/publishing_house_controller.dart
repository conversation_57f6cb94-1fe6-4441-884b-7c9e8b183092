import 'package:get/get.dart';
import 'package:matjari/src/data/providers/api/api_provider.dart';
import 'package:matjari/src/data/services/network_service.dart';

import '../core/utils/common_functions.dart';
import '../core/values/app_config.dart';
import '../data/enums/page_status.dart';
import '../data/models/hive/product.dart';

class PublishingHouseController extends GetxController {
  //first page status
  var firtPageStatus = LoadingStatus.loaded.obs;
  //next page status
  var nextPageStatus = LoadingStatus.loaded.obs;
  ApiProvider apiProvider = AppConfigs.apiProvider;

  // static instance
  static PublishingHouseController get instance => Get.find();
  // products
  var products = Rx<List<Product>>([]);
  //current page
  int currentPage = 0;
//max page
  int maxPage = 1;
  // // get categry products
  // Future<void> getCategoryProducts() async {
  //   // check internet connection
  //   NetworkService.instance.checkConnectivity(() async {
  //     pageStatus.value = LoadingStatus.loading;
  //     // get products
  //     var response = await apiProvider.getPublisherProducts(Get.arguments["publisher_id"]);

  //     if (response.status == 0) {
  //       // Get.log(
  //       //     'get category products success ${response.result.products.length}');
  //       products.value = response.result.products;
  //       pageStatus.value = LoadingStatus.loaded;
  //     } else {
  //       CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب المنتجات");
  //       pageStatus.value = LoadingStatus.error;
  //     }
  //   });
  // }

// get categry products
  Future<void> getCategoryProducts([int page = 1]) async {
    if (page == 1) {
      currentPage = 0;
      products.value = [];
    }
    // check internet connection
    NetworkService.instance.checkConnectivity(() async {
      if (currentPage == 0) {
        firtPageStatus.value = LoadingStatus.loading;
      } else {
        nextPageStatus.value = LoadingStatus.loading;
      }
      // get products
      var response = await apiProvider
          .getPublisherProducts(Get.arguments["publisher_id"], page: page);
      if (response.status == 0) {
        Get.log(
            'get category products success ${response.result.products.length}');

        products.value.addAll(response.result.products);

        products.refresh();
        currentPage = response.result.currentPage;
        maxPage = response.result.maxPage;

        if (currentPage == 1) {
          firtPageStatus.value = LoadingStatus.loaded;
        } else {
          nextPageStatus.value = LoadingStatus.loaded;
        }
      } else {
        CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب المنتجات");
        if (currentPage == 0) {
          firtPageStatus.value = LoadingStatus.error;
        } else {
          nextPageStatus.value = LoadingStatus.error;
        }
      }
    }, () {
      if (currentPage == 0) {
        firtPageStatus.value = LoadingStatus.networkError;
      }
    });
  }

  @override
  void onInit() {
    getCategoryProducts();

    super.onInit();
  }
}
