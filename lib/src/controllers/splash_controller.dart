// import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/values/boxes.dart';
import 'package:matjari/src/core/values/colors.dart';
import 'package:matjari/src/data/models/hive/cart_item.dart';
import 'package:matjari/src/data/models/hive/product.dart';
import 'package:matjari/src/data/providers/api/api_provider.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';
import 'package:matjari/src/data/services/auth_services.dart';
import 'package:matjari/src/data/services/init_service.dart';
import 'package:matjari/src/data/services/network_service.dart';

import 'package:flutter/material.dart';
import 'package:flutter_ringtone_player/flutter_ringtone_player.dart';
import 'package:flutter_svg/svg.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/view/components/images/custom_cached_network_image.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../controllers/order_details_controller.dart';
import '../core/values/app_config.dart';
import '../data/services/loading_service.dart';

class SplashController extends GetxController {
  @override
  void onInit() async {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.dark,
        statusBarColor: AppColors.greyColor));

    var takenTime = Stopwatch()..start();
    //inital FirebaseMessaging
    try {
      FirebaseMessaging.instance.getToken();
    } catch (e) {
      Get.log(e.toString());
    }

    try {
      //init crashlytics
      initCrashlytics();

      //init network services
      Get.put(await NetworkService().init());

      // init app config
      await initializing();

      //init auth service
      await initServcies();
      takenTime.stop();

      //sync cart items
      InitService.instance.checkProductsAvailability();
    } catch (e) {
      Get.log(e.toString());
    }

    if (takenTime.elapsedMilliseconds < 5000) {
      await Future.delayed(
          Duration(seconds: 5 - takenTime.elapsedMilliseconds ~/ 1000));
    }

    //show curency page if not set
    if (HiveProvider.getSelectedCurrency() == null) {
      Get.offAndToNamed(Routes.CURRENCY_PAGE);
    } else {
      Get.offAndToNamed(Routes.HOME_PAGE);
    }

    //
    try {
      //onTap firebase notification when app is terminated handeler
      onBackgroundMessageOpendFirebaseHandeler();

      //set notification tap handeler --onesignal
      setNotificationOpenHandeler();

      super.onInit();
    } catch (e) {
      Get.log(e.toString());
    }
  }

  void initCrashlytics() {
    //send crashlytics event  on release mode
    if (kReleaseMode) {
      //inital FirebaseCrashlytics
      FlutterError.onError = (errorDetails) {
        FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
      };

      // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
      PlatformDispatcher.instance.onError = (error, stack) {
        FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
        return true;
      };
    }
  }

  Future<void> initServcies() async {
    try {
      Get.lazyPut(() => LoadingService());

      var authService = Get.put<AuthService>(AuthService());

      await authService.init();

      Get.put(InitService());
      // ignore: empty_catches
    } catch (e) {}
  }

  Future<void> initializing() async {
    await initializeRemoteConfig();
    //
    //---firebase---
    //listen to notification when app is opend
    //android
    firebaseOnMessage();
    //ios
    FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
        alert: true, badge: true, sound: true);

    //when  notification opend and app in opend and it is in background
    onMessageOpenedAppFirebase();

    //------one signal-------
    //initialize one signal sdk
    initializeOneSignal();
  }

  Future<void> initializeRemoteConfig() async {
    try {
      // if in debug mode change remote config settings
      if (kDebugMode) {
        await FirebaseRemoteConfig.instance.setConfigSettings(
          RemoteConfigSettings(
            fetchTimeout: const Duration(minutes: 1),
            minimumFetchInterval: const Duration(minutes: 5),
          ),
        );
      }

      await FirebaseRemoteConfig.instance.setDefaults(<String, dynamic>{
        'api_url': 'https://platformdgtl.net/medical-market/connect/v1/',
        'app_key':
            'ZGJmOGJmZjhkYWU3MjJmNzc0ZmE5ODBlNzM1N2ExZmVhZTc3ZTM4OThiMjg4ZmRjZDIyMDQ5ZTg4Y2VjZjg3YjIyMDItNUB2aUQtdG5laWxjLW1yb2Z0YWxwLWlyYWp0YW0',
        'onesignal_app_id': '************************************'
      });

      try {
        await FirebaseRemoteConfig.instance.fetchAndActivate();
        // ignore: empty_catches
      } catch (e) {}

      NetworkService.instance.addListener((value) async {
        Get.log('connected: $value');

        if (value) {
          try {
            await FirebaseRemoteConfig.instance.fetchAndActivate();

            FirebaseRemoteConfig.instance.onConfigUpdated.listen((event) async {
              await FirebaseRemoteConfig.instance.activate();
            });

            // ignore: empty_catches
          } catch (e) {}
        }
      });
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  Future<void> initializeOneSignal() async {
    await OneSignal.Debug.setLogLevel(
      OSLogLevel.none,
    );
    print("Onesignal app id ${CommonFunctions.getOneSignalAppId()}");
    OneSignal.initialize(CommonFunctions.getOneSignalAppId());
    if (Platform.isAndroid) {
      var androidInfo = await DeviceInfoPlugin().androidInfo;
      var sdkInt = androidInfo.version.release;
      // if ((int.tryParse(sdkInt) ?? 0) > 11) {
      OneSignal.Notifications.requestPermission(true);
      // }
    } else {
      OneSignal.Notifications.requestPermission(true);
    }
  }

  void setNotificationOpenHandeler() async {
    OneSignal.Notifications.addClickListener((event) {
      if (event.notification.additionalData?.containsKey("order_id") ?? false) {
        //refresh page if it opend
        if (Get.currentRoute == Routes.ORDER_DETAILS_PAGE) {
          OrderDetailsController.instance.getOrder(int.parse(
              event.notification.additionalData!["order_id"].toString()));
        } else {
          Get.toNamed(Routes.ORDER_DETAILS_PAGE,
              arguments: event.notification.additionalData);
        }
      }
    });
  }

  void onMessageOpenedAppFirebase() async {
    FirebaseMessaging.onMessageOpenedApp.listen((event) {
      if (event.notification != null) {
        if (event.data.containsKey("order_id")) {
          //refresh page if it opend
          if (Get.currentRoute == Routes.ORDER_DETAILS_PAGE) {
            OrderDetailsController.instance
                .getOrder(int.parse(event.data["order_id"].toString()));
          } else {
            Get.toNamed(Routes.ORDER_DETAILS_PAGE, arguments: event.data);
          }
        }
      }
    });
  }

  firebaseOnMessage() {
    FirebaseMessaging.onMessage.listen(
      (event) {
        if (event.notification != null &&
            (event.notification?.android != null)) {
          Get.log("notification arraive from firebase ");
          Get.log(event.data.toString());
          FlutterRingtonePlayer();

          showOverlayNotification((context) {
            return SafeArea(
              child: Material(
                color: Colors.transparent,
                child: Container(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.white,
                      border: Border.all(color: Colors.grey.shade200)),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(12),
                      hoverColor: Colors.transparent,
                      onTap: !event.data.containsKey("order_id")
                          ? null
                          : () {
                              //refresh page if it opend
                              if (Get.currentRoute ==
                                  Routes.ORDER_DETAILS_PAGE) {
                                OrderDetailsController.instance.getOrder(
                                    int.parse(
                                        event.data["order_id"].toString()));
                              } else {
                                Get.toNamed(Routes.ORDER_DETAILS_PAGE,
                                    arguments: event.data);
                              }

                              OverlaySupportEntry.of(context)?.dismiss();
                            },
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox.fromSize(
                                          size: const Size(40, 40),
                                          child: ClipOval(
                                              child: SvgPicture.asset(
                                                  Assets.logo))),
                                      Expanded(
                                        child: Padding(
                                          padding:
                                              const EdgeInsetsDirectional.only(
                                                  start: 8.0),
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              Text(
                                                event.notification!.title ?? "",
                                                style: const TextStyle(
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ),
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    top: 8.0),
                                                child: Text(
                                                  event.notification!.body ??
                                                      "",
                                                  softWrap: true,
                                                  style: const TextStyle(
                                                      color: Colors.grey,
                                                      fontSize: 13),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                IconButton(
                                    icon: const Icon(Icons.close),
                                    onPressed: () {
                                      OverlaySupportEntry.of(context)
                                          ?.dismiss();
                                    })
                              ],
                            ),
                            if (event.notification!.android?.imageUrl != null)
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: SizedBox(
                                  height: 200,
                                  child: CustomCachedNetworkImage(
                                    imageUrl:
                                        event.notification!.android!.imageUrl!,
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          }, duration: Duration.zero);
        }
      },
    );
  }

  Future<void> onBackgroundMessageOpendFirebaseHandeler() async {
    var mess = await FirebaseMessaging.instance.getInitialMessage();

    if (mess?.data != null) {
      if (mess!.data.containsKey("order_id")) {
        Get.toNamed(Routes.ORDER_DETAILS_PAGE, arguments: mess.data);
      }
    }
  }
}
