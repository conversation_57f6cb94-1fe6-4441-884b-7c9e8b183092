
import 'package:get/get.dart';

import '../core/values/country_codes.dart';

class CountryCodeController extends GetxController {
  CountryCodeController({
    required this.countryCode,
    this.favoriteCountries,
    this.filteredCountries,
  });
  String countryCode;
  var countries = countryCodes.obs;
  List<String>? favoriteCountries;
  List<String>? filteredCountries = [];
  @override
  void onInit() {
    if (filteredCountries?.isNotEmpty ?? false) {
      countries.value = countryCodes
          .where((element) => filteredCountries!.contains(element["dial_code"]))
          .toList();
    }
    // sortCountries();
    countries.refresh();
    super.onInit();
  }

  void search(String value) {
    var filtered = countryCodes.where(
      (element) {
        var name = (element['code']!.tr);
        var code = (element['code']);
        var dialCode = (element['dial_code']);
        return name.contains(value) ||
            code!.contains(value.toUpperCase()) ||
            dialCode!.contains(value);
      },
    ).toList();
    countries.value = filtered;
    // sortCountries();
    countries.refresh();
    print(countries);
  }

  // void sortCountries() {
  //   countries.sort((a, b) {
  //     // loop through favorite countries
  //     for (var i = 0; i < favoriteCountries.length; i++) {
  //       if (a["code"] == favoriteCountries[i]) return -1;
  //     }
  //     if (a["code"] == countryCode) return -1;

  //     return 1;

  //     // return a['code']!.compareTo(b['code']!);
  //   });
  // }
}
