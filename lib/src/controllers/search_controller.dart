import 'package:get/get.dart';
import 'package:matjari/src/data/services/network_service.dart';

import '../core/utils/common_functions.dart';
import '../core/values/app_config.dart';
import '../data/enums/page_status.dart';
import '../data/models/hive/product.dart';

class SearchController extends GetxController {
  SearchController({required this.searchText});
  // page status
  Rx<LoadingStatus> pageStatus = LoadingStatus.loaded.obs;
  var searchText = ''.obs;
  // products
  Rx<List<Product>> products = Rx<List<Product>>([]);
  // search
  void search() {
    NetworkService.instance.checkConnectivity(() async {
      pageStatus.value = LoadingStatus.loading;
      var response = await AppConfigs.apiProvider.search(searchText.value);
      if (response.status == 0) {
        products.value = response.searchResult.products;
        pageStatus.value = LoadingStatus.loaded;
      } else if (response.status == 1) {
        products.value = [];
        pageStatus.value = LoadingStatus.loaded;
      } else {
        CommonFunctions.showErrorMessage("حصل خطأ أثناء البحث");
        pageStatus.value = LoadingStatus.error;
      }
    }, () {
      pageStatus.value = LoadingStatus.networkError;
    });
  }

  @override
  void onInit() {
    if (searchText.value.isNotEmpty) {
      search();
    }
    super.onInit();
  }
}
