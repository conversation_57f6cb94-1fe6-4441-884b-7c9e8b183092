import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:overlay_support/overlay_support.dart';

import 'bindings/initializing_binding.dart';
import 'core/routes/app_pages.dart';
import 'core/theme/app_themes.dart';
import 'core/translations/app_translations.dart';

class MatjariApp extends StatefulWidget {
  const MatjariApp({Key? key}) : super(key: key);

  @override
  State<MatjariApp> createState() => _MatjariAppState();
}

class _MatjariAppState extends State<MatjariApp> {
  @override
  Widget build(BuildContext context) {
    return OverlaySupport(
      child: GetMaterialApp(
        debugShowCheckedModeBanner: false,
        initialRoute: Routes.SPLASH_PAGE,
        enableLog: true,
        theme: appThemeData,
        defaultTransition: Transition.fade,
        initialBinding: InitializingBinding(),
        locale: const Locale("ar"),
        translations: AppTranslations(),
        builder: (ctx, child) {
          final MediaQueryData data = MediaQuery.of(context);
          return MediaQuery(
            data: data.copyWith(textScaler: const TextScaler.linear(1.0)),
            child: child ?? Container(),
          );
        },
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale("ar"),
          // Locale("en"),
        ],
        getPages: AppPages.pages,
      ),
    );
  }
}
