import 'package:badges/badges.dart';
import 'package:flutter/material.dart' hide Badge;
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:matjari/src/controllers/home_controller.dart';
import 'package:matjari/src/controllers/publishing_house_controller.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/utils/extenstions/double_extenstion.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/core/values/colors.dart';
import 'package:matjari/src/data/enums/page_status.dart';
import 'package:matjari/src/data/models/hive/cart_item.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';
import 'package:matjari/src/view/components/buttons/custom_filled_button.dart';
import 'package:matjari/src/view/components/errors/no_internet.dart';
import 'package:matjari/src/view/components/list_views/product_list_view.dart';
import 'package:matjari/src/view/components/loading/loading.dart';
import 'package:matjari/src/view/components/master_page.dart';

class PublishingHousePage extends GetView<PublishingHouseController> {
  PublishingHousePage({Key? key}) : super(key: key);
  final scrollController = Rx<ScrollController>(ScrollController());
  var atEnd = Rx<bool>(false);
  @override
  Widget build(BuildContext context) {
    scrollController.value.addListener(() {
      // Do something when the scroll position changes.

      if (scrollController.value.position.atEdge && atEnd.value == false) {
        if (controller.currentPage != controller.maxPage) {
          controller
              .getCategoryProducts(controller.currentPage + 1)
              .then((value) {
            atEnd.value = false;
          });
        }
      }
      atEnd.value = scrollController.value.position.atEdge;
    });
    return MasterPage(
      title: Get.arguments["publisher_name"],
      body: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(
              () {
                if (controller.firtPageStatus.value == LoadingStatus.loading) {
                  return Loading(
                    height: Get.height * 0.6,
                  );
                }
                if (controller.firtPageStatus.value ==
                    LoadingStatus.networkError) {
                  return Expanded(
                    child: Center(
                      child: NoInternet(onRetry: () {
                        controller.getCategoryProducts();
                      }),
                    ),
                  );
                }
                return Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController.value,
                    child: Column(
                      children: [
                        ProductListView(
                          products: controller.products.value,
                        ),
                        //pagination
                        Obx(() {
                          if (controller.nextPageStatus.value ==
                              LoadingStatus.loading) {
                            return Container(
                              margin: const EdgeInsets.only(bottom: 30),
                              child: const Loading(
                                height: 60,
                              ),
                            );
                          }
                          return const SizedBox(
                            height: 70,
                          );
                        })
                      ],
                    ),
                  ),
                );
              },
            ),
          ]),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      floatingActionButton: ValueListenableBuilder<Box<CartItem>>(
          valueListenable: HiveProvider.cartItems,
          builder: (context, box, widget) {
            var total = HiveProvider.cartItemsTotal;
            var count = HiveProvider.cartItemsCount;
            if (total > 0) {
              return Material(
                color: Colors.transparent,
                child: CustomFilledButton(
                    padding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Badge(
                          position: BadgePosition.custom(top: 0, start: 0),
                          badgeStyle: BadgeStyle(
                            padding: const EdgeInsets.all(6),
                            badgeColor: AppColors.redColor,
                          ),
                          badgeContent: Center(
                            child: Text(
                              count.toString(),
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          child: const Icon(
                            MatjariIcons.cart,
                            size: 45,
                          ),
                        ),
                        Text(
                          '${total.toStringValue()}  ${HomeController.instance.currencyCode}',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                    onPressed: () {
                      Get.toNamed(Routes.CART_PAGE);
                    }),
              );
            }
            return FloatingActionButton(
              heroTag: "cart",
              onPressed: () {
                Get.toNamed(Routes.CART_PAGE);
              },
              backgroundColor: Get.theme.primaryColor,
              child: const Icon(
                MatjariIcons.cart,
                size: 40,
              ),
            );
          }),
    );
  }
}
