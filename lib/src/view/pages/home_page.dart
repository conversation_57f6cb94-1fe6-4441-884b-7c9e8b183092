import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/data/services/auth_services.dart';

import 'package:matjari/src/view/components/home/<USER>/home_bottom_navigation_bar.dart';
import 'package:matjari/src/view/components/master_page.dart';
import 'package:matjari/src/view/pages/cart_page.dart';
import 'package:matjari/src/view/pages/favorites_page.dart';

import '../../controllers/home_controller.dart';
import '../../core/routes/app_pages.dart';

import '../../data/models/user.dart';
import '../components/page_views/home_page_view.dart';
import '../components/page_views/profile_page_view.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  @override
  Widget build(BuildContext context) {
    return MasterPage(
      titleWidget: SizedBox(
        width: double.maxFinite,
        child: ObxValue<Rx<User?>>(
          (context) {
            return CommonFunctions.getGreetingWidget();
          },
          AuthService.instance.user,
        ),
      ),
      resizeToAvoidBottomInset: false,
      extendBody: true,

      // leading: IconButton(
      //   icon: SvgPicture.asset(
      //     Assets.language,
      //     color: Colors.white,
      //   onPressed: () {
      //     CommonFunctions.showChangeLocaleBottomSheet();
      //   },
      // ),
      actions: [
        IconButton(
          icon: SvgPicture.asset(Assets.info, height: 25),
          onPressed: () {
            Get.toNamed(Routes.ABOUT_APP_PAGE);
          },
        ),
      ],
      bottomNavigationBar: const HomeBottomNavigationBar(),

      // floatingActionButton: const CartButton(),
      body: PageView(
        physics: const NeverScrollableScrollPhysics(),
        controller: HomeController.instance.pageViewController,
        onPageChanged: (index) {
          HomeController.instance.currentIndex.value = index;
        },
        children: const [
          HomePageView(),
          ProfilePageView(),
          FavoritesPage(
            hideAppBar: true,
          ),
          CartPage(
            hideAppBar: true,
          )
        ],
      ),
    );
  }
}
