import 'package:flutter/material.dart' hide SearchController;
import 'package:get/get.dart';
import 'package:matjari/src/controllers/search_controller.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/view/components/form_fields/custom_text_form_field.dart';
import 'package:matjari/src/view/components/icons/directionality_icon.dart';
import 'package:matjari/src/view/components/list_views/product_list_view.dart';
import 'package:matjari/src/view/components/master_page.dart';

import '../../data/enums/page_status.dart';
import '../components/errors/no_internet.dart';
import '../components/errors/server_error.dart';
import '../components/loading/loading.dart';

class SearchPage extends GetView<SearchController> {
  const SearchPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MasterPage(
      body: Column(
        children: [
          CustomTextFormField(
            hintText: 'searchHint'.tr,
            prefixIcon: const DirectionalityIcon(MatjariIcons.search),
            margin: const EdgeInsets.all(16),
            controller: TextEditingController.fromValue(TextEditingValue(
              text: controller.searchText.value,
            )),
            suffixIcon: const DirectionalityIcon(
              MatjariIcons.left_arrow,
              size: 15,
            ),
            textInputAction: TextInputAction.search,
            onChanged: (value) {
              controller.searchText.value = value ?? "";
            },
            onFieldSubmitted: (value) {
              if (value != null && value.isNotEmpty) {
                controller.search();
              }
            },
          ),
          Expanded(
            child: Obx(() {
              if (controller.pageStatus.value == LoadingStatus.loading) {
                return const Loading();
              } else if (controller.pageStatus.value == LoadingStatus.error) {
                return ServerError(
                  onRetry: () {
                    controller.search();
                  },
                );
              } else if (controller.pageStatus.value ==
                  LoadingStatus.networkError) {
                return NoInternet(onRetry: () {
                  controller.search();
                });
              }
              return ProductListView(
                searchResult: true,
                products: controller.products.value,
              );
            }),
          ),
        ],
      ),
    );
  }
}
