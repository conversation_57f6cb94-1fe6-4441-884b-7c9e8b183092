import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/order_details_controller.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/utils/extenstions/string_extension.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/data/models/hive/cart_item.dart';
import 'package:matjari/src/data/models/hive/product.dart';
import 'package:matjari/src/data/models/payment_method.dart';
import 'package:matjari/src/data/services/init_service.dart';
import 'package:matjari/src/view/components/buttons/custom_filled_button.dart';
import 'package:matjari/src/view/components/form_fields/payment_method_form_field.dart';
import 'package:matjari/src/view/components/master_page.dart';
import 'package:matjari/src/view/components/order_status_stepper.dart';
import '../../core/values/app_constants.dart';
import '../../data/enums/page_status.dart';
import '../components/errors/no_internet.dart';
import '../components/errors/server_error.dart';
import '../components/images/custom_cached_network_image.dart';
import '../components/list_titles/order_text_info.dart';
import '../components/list_titles/order_widget_info.dart';
import '../components/list_views/order_product_list.dart';
import '../components/list_views/checkout_order_product_list.dart';
import '../components/loading/loading.dart';

class OrderDetailsPage extends GetView<OrderDetailsController> {
  OrderDetailsPage({super.key});

  @override
  final String? tag = OrderDetailsController.tag;
  @override
  Widget build(BuildContext context) {
    var pageTitle = (Get.arguments is Map
        ? Get.arguments["order_id"].toString()
        : Get.arguments?.orderId.toString() ?? "");

    // var orderController = OrderController.instance;

    return MasterPage(
      title: "#$pageTitle",
      body: Obx(() {
        if (!Get.isRegistered<OrderDetailsController>()) {
          Get.put(OrderDetailsController(), tag: OrderDetailsController.tag);
        }
        if (controller.pageStatus.value == LoadingStatus.loading) {
          return const Loading();
        } else if (controller.pageStatus.value == LoadingStatus.error) {
          return ServerError(
            onRetry: () async {
              await controller.getOrder();
              await controller.getPaymentMethods();
            },
          );
        } else if (controller.pageStatus.value == LoadingStatus.networkError) {
          return NoInternet(onRetry: () async {
            await controller.getOrder();
            await controller.getPaymentMethods();
          });
        }

        // if (!OrderController.instance.orders.value
        //     .any((element) => element.orderId == controller.order.orderId)) {
        //   OrderController.instance.orders.value.add(controller.order);
        // }

        return RefreshIndicator(
          onRefresh: () async {
            await controller.getOrder(controller.order.value!.orderId);
          },
          child: ListView(
            padding: const EdgeInsets.only(
              bottom: 16,
            ),
            children: [
              // order status
              // OrderTextInfo(
              //     title: 'orderStatus'.tr,
              //     value: order.orderStatusString,
              //     icon: Icon(
              //       AppConstants.orderStatusIconValues[order.orderStatus],
              //       color: Colors.grey,
              //       size: 25,
              //     )),

              //order status

              OrderWidgetInfo(
                title: "حالة الطلب",
                withDivider: true,
                widget: OrderStatusStepper(
                  currentIndex: controller.order.value!.orderStatus,
                  orderStatusUpdateDate:
                      controller.order.value!.orderStatusUpdateDate,
                  order: controller.order.value!,
                ),
              ),

              // order address
              OrderTextInfo(
                  title: 'deliveryAddress'.tr,
                  value: controller.order.value!.orderAddress,
                  icon: const Icon(
                    MatjariIcons.addresses,
                    color: Colors.grey,
                    size: 25,
                  )),
              // delivery time
              OrderTextInfo(
                title: 'deliveryTime'.tr,
                value: controller.order.value!.orderDeliveryTime.decodeUnicode(),
                // icon: SvgPicture.asset(Assets.time,
                //     color: Colors.grey, height: 25),
              ),
              // payment method

              OrderWidgetInfo(
                title: 'paymentMethod'.tr,
                widget: Obx(
                  () {
                    // orderController.orders.value[orderController.orders.value
                    //         .indexWhere((element) =>
                    //             element.orderId == controller.order.orderId)] =
                    //     controller.order;

                    // var controllerOrder = orderController.orders.value
                    //     .firstWhereOrNull((element) =>
                    //         element.orderId == controller.order.orderId)
                    //     .obs;

                    //get payment method from init services

                    var paymentMethod = controller.paymentMethods.value;

                    print(paymentMethod
                        .firstWhereOrNull((element) =>
                            element.paymentId ==
                            controller.order.value!.orderPaymentId)
                        ?.paymentIcon);

                    return PaymentMethodFormField(
                      key: UniqueKey(),
                      controller: controller,
                      onPaymentMethodChange: (paymentMethod) {
                        controller.addPaymentInfo(paymentMethod);
                      },
                      icon: Container(
                        padding: const EdgeInsetsDirectional.only(
                          end: 8,
                          top: 6,
                        ),
                        child: Center(
                          child: CustomCachedNetworkImage(
                            imageUrl: paymentMethod
                                    .firstWhereOrNull((element) =>
                                        element.paymentId ==
                                        controller.order.value!.orderPaymentId)
                                    ?.paymentIcon ??
                                "",
                            errorIcon: Assets.payment,
                            height: 25,
                          ),
                        ),
                      ),
                      order: controller.order.value!,
                      margin: const EdgeInsets.only(
                          right: 16, left: 16, bottom: 16),
                      initialValue: PaymentMethod(
                        paymentId: controller.order.value!.orderPaymentId,
                        paymentName: controller.order.value!.orderPaymentName,
                        paymentDescription:
                            controller.order.value!.orderPaymentDescription,
                        paymentAccountNumber:
                            controller.order.value!.orderPaymentAccountNumber,
                        paymentType: controller.order.value!.orderPaymentType,
                        paymentTypeLabel:
                            controller.order.value!.orderPaymentTypeLabel,
                      ),
                      paymentMethods: paymentMethod,
                    );
                  },
                ),
              ),
              // order notes

              if (controller.order.value!.orderNotes != "")
                OrderTextInfo(
                  title: 'orderNotes'.tr,
                  value: controller.order.value!.orderNotes,
                  icon: SvgPicture.asset(Assets.notes, height: 25),
                ),

              // order products
              OrderWidgetInfo(
                title: 'orderItems'.tr,
                withDivider: false,
                widget: OrderProductList(
                  order: controller.order.value!,
                  orderFullTotalPrice:
                      controller.order.value!.orderFullTotalPrice,
                  orderTotalPrice: controller.order.value!.orderTotalPrice,
                  orderCurrencyVar: controller.order.value!.orderCurrencyVar,
                  deliveryFees: controller.order.value!.orderDeliveryFees,
                  items: controller.order.value!.products
                      .map(
                        (e) => CartItem(
                          totlePrice: e.itemTotalPrice,
                          product: Product(
                              id: e.itemId,
                              name: e.itemName,
                              price: e.itemPrice,
                              image: e.itemImage,
                              imageThumb: e.itemImageThumb,
                              itemCurrencyVar: e.itemCurrencyVar),
                          quantity: e.itemQuantity,
                        ),
                      )
                      .toList(),
                ),
              ),

              //  if (controller.order.orderNotes != "")
              if (controller.order.value!.orderPaymentStatus < 3 &&
                  controller.order.value!.orderStatus <= 1)
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: CustomFilledButton(
                      child: Text('cancelOrder'.tr),
                      onPressed: () {
                        CommonFunctions.showConfirmBottomSheet(
                            title: "تأكيد الغاء الطلب",
                            message: "هل أنت متاكد من إلغاء الطلب؟",
                            onConfirm: () {
                              Get.back();
                              controller
                                  .cancelOrder(controller.order.value!.orderId);
                            });
                      }),
                ),
            ],
          ),
        );
      }),
    );
  }
}
