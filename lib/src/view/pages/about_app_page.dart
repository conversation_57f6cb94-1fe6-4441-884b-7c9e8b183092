import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/about_app_controller.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/core/values/colors.dart';
import 'package:matjari/src/view/components/about_app/about_group_label.dart';
import 'package:matjari/src/view/components/about_app/contact_circle.dart';
import 'package:matjari/src/view/components/errors/no_internet.dart';
import 'package:matjari/src/view/components/errors/server_error.dart';
import 'package:matjari/src/view/components/loading/loading.dart';
import 'package:matjari/src/view/components/master_page.dart';
import 'package:share_plus/share_plus.dart';

import '../../core/utils/matjari_icons.dart';
import '../../data/enums/page_status.dart';
import '../components/about_app/about_header.dart';
import '../components/list_titles/icon_list_tile.dart';

class AboutAppPage extends GetView<AboutAppController> {
  const AboutAppPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    controller.getAboutInfo();
    return MasterPage(
      title: 'aboutApp'.tr,
      body: Obx(
        () {
          if (controller.pageStatus.value == LoadingStatus.loading) {
            return const Loading();
          } else if (controller.pageStatus.value == LoadingStatus.error) {
            return ServerError(
              onRetry: () {
                controller.getAboutInfo();
              },
            );
          } else if (controller.pageStatus.value ==
              LoadingStatus.networkError) {
            return NoInternet(onRetry: () {
              controller.getAboutInfo();
            });
          }
          var pages = controller.pages.value;
          var configurations = controller.configurations.value;
          return SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AboutHeader(
                  appDescription:
                      controller.configurations.value?.appDescription ?? "",
                  version:
                      "${controller.appVersion.value}+${controller.buildnumber.value}",
                ),
                Divider(),
                // AboutGroupLabel(label: 'information'.tr),
                for (int i = 0; i < pages.length; i++)
                  IconListTile(
                    title: pages[i].pageTitle,
                    icon: MatjariIcons.about,
                    onTap: () {
                      if (pages[i].pageUrl.isNotEmpty) {
                        CommonFunctions.openUrl(pages[i].pageUrl);
                      } else {
                        Get.toNamed(Routes.ABOUT_PAGE, arguments: pages[i]);
                      }
                    },
                    withDivider: i != pages.length - 1,
                  ),

                Divider(),
                // contact us label
                // AboutGroupLabel(label: 'contactUs'.tr),
                // phone
                Padding(
                  padding: EdgeInsetsDirectional.symmetric(horizontal: 16),
                  child: Wrap(
                      alignment: WrapAlignment.center,
                      runSpacing: 16,
                      spacing: 8,
                      children: [
                        if (configurations?.phones.isNotEmpty ?? false)
                          ContactCircle(
                            icon: MatjariIcons.phone,
                            onTap: () {
                              CommonFunctions.openUrl(
                                  'tel:${configurations?.phones[0]}');
                            },
                          ),
                        if (configurations?.mobile.isNotEmpty ?? false)
                          ContactCircle(
                            icon: MatjariIcons.mobile,
                            onTap: () {
                              CommonFunctions.openUrl(
                                  'tel:${configurations?.mobile.replaceAll(' ', '')}');
                            },
                          ),
                        if (configurations?.whatsapp.isNotEmpty ?? false)
                          ContactCircle(
                            icon: MatjariIcons.whatsapp,
                            onTap: () {
                              CommonFunctions.openUrl(
                                  'https://api.whatsapp.com/send?phone=${configurations?.whatsapp.replaceAll(' ', '')}');
                            },
                          ),
                        if (configurations?.email.isNotEmpty ?? false)
                          ContactCircle(
                            icon: MatjariIcons.email,
                            onTap: () {
                              CommonFunctions.openUrl(
                                  'mailto:${configurations?.email}');
                            },
                          ),
                        if (configurations?.website.isNotEmpty ?? false)
                          ContactCircle(
                            icon: MatjariIcons.website,
                            onTap: () {
                              // open website
                              CommonFunctions.openUrl(configurations!.website);
                            },
                          ),
                      ]),
                ),
                Divider(),
                Wrap(
                  spacing: 8,
                  children: [
                    if (configurations?.facebook.isNotEmpty ?? false)
                      ContactCircle(
                        icon: MatjariIcons.facebook,
                        onTap: () {
                          // open facebook
                          CommonFunctions.openUrl(configurations!.facebook);
                        },
                      ),
                    if (configurations?.twitter.isNotEmpty ?? false)
                      ContactCircle(
                        icon: MatjariIcons.twitter,
                        onTap: () {
                          // open twitter
                          CommonFunctions.openUrl(configurations!.twitter);
                        },
                      ),
                    if (configurations?.instagram.isNotEmpty ?? false)
                      ContactCircle(
                        icon: MatjariIcons.instagram,
                        onTap: () {
                          // open instagram
                          CommonFunctions.openUrl(configurations!.instagram);
                        },
                      ),
                    if (configurations?.youtube.isNotEmpty ?? false)
                      ContactCircle(
                        icon: MatjariIcons.youtube,
                        onTap: () {
                          // open youtube
                          CommonFunctions.openUrl(configurations!.youtube);
                        },
                      ),
                    if (configurations?.tiktok.isNotEmpty ?? false)
                      ContactCircle(icon: MatjariIcons.tiktok, onTap: () {})
                  ],
                ),

                // IconListTile(
                //   title: 'phone'.tr,
                //   icon: MatjariIcons.website,
                //   onTap: () {
                //     // TODO: launch phone
                //     CommonFunctions.openUrl('tel:${configurations?.phones[0]}');
                //   },
                //   withDivider: true,
                // ),
                // mobile
                // if (configurations?.mobile.isNotEmpty ?? false)
                //   IconListTile(
                //     title: configurations!.mobile,
                //     icon: MatjariIcons.mobile,
                //     onTap: () {
                //       // open mobile number
                //       CommonFunctions.openUrl(
                //           'tel:${configurations.mobile.replaceAll(' ', '')}');
                //     },
                //   ),
                // // whatsapp
                // if (configurations?.whatsapp.isNotEmpty ?? false)
                //   IconListTile(
                //     title: configurations!.whatsapp,
                //     icon: MatjariIcons.whatsapp,
                //     onTap: () {
                //       // open whatsapp number
                //       CommonFunctions.openUrl(
                //           'https://api.whatsapp.com/send?phone=${configurations.whatsapp.replaceAll(' ', '')}');
                //     },
                //   ),
                // // email
                // if (configurations?.email.isNotEmpty ?? false)
                //   IconListTile(
                //     title: configurations!.email,
                //     icon: MatjariIcons.email,
                //     onTap: () {
                //       // open email
                //       CommonFunctions.openUrl('mailto:${configurations.email}');
                //     },
                //   ),
                // // website
                // if (configurations?.website.isNotEmpty ?? false)
                //   IconListTile(
                //     title: configurations!.website,
                //     icon: MatjariIcons.website,
                //     onTap: () {
                //       // open website
                //       CommonFunctions.openUrl(configurations.website);
                //     },
                //     withDivider: false,
                //   ),
                // // follow us label
                // AboutGroupLabel(label: 'followUs'.tr),
                // // facebook
                // if (configurations?.facebook.isNotEmpty ?? false)
                //   IconListTile(
                //     title: 'facebook'.tr,
                //     icon: MatjariIcons.facebook,
                //     onTap: () {
                //       // open facebook
                //       CommonFunctions.openUrl(configurations!.facebook);
                //     },
                //     withDivider: true,
                //   ),
                // // twitter
                // if (configurations?.twitter.isNotEmpty ?? false)
                //   IconListTile(
                //     title: 'twitter'.tr,
                //     icon: MatjariIcons.twitter,
                //     onTap: () {
                //       // open twitter
                //       CommonFunctions.openUrl(configurations!.twitter);
                //     },
                //     withDivider: true,
                //   ),
                // // instagram
                // if (configurations?.instagram.isNotEmpty ?? false)
                //   IconListTile(
                //     title: 'instagram'.tr,
                //     icon: MatjariIcons.instagram,
                //     onTap: () {
                //       // open instagram
                //       CommonFunctions.openUrl(configurations!.instagram);
                //     },
                //     withDivider: true,
                //   ),
                // // youtube
                // if (configurations?.youtube.isNotEmpty ?? false)
                //   IconListTile(
                //     title: 'youtube'.tr,
                //     icon: MatjariIcons.youtube,
                //     onTap: () {
                //       // open youtube
                //       CommonFunctions.openUrl(configurations!.youtube);
                //     },
                //     withDivider: true,
                //   ),

                // // tiktok
                // if (configurations?.tiktok.isNotEmpty ?? false)
                //   IconListTile(
                //     title: 'tiktok'.tr,
                //     icon: MatjariIcons.tiktok,
                //     onTap: () {
                //       // open tiktok
                //       CommonFunctions.openUrl(configurations!.tiktok);
                //     },
                //     withDivider: true,
                //   ),
                // matjari label
                // AboutGroupLabel(label: configurations?.appName ?? ""),
                // share
                Divider(),
                IconListTile(
                  title: 'share'.tr,
                  icon: MatjariIcons.share,
                  iconMatchTextDirection: true,
                  onTap: () {
                    // share app

                    // TODO: Add share message
                    Share.share(
                      "لتحميل تطبيق ${configurations?.appName ?? ""}:\n اندرويد ${configurations?.shareAndroid}\n ايفون ${configurations?.shareIos}",
                    );
                  },
                  withDivider: true,
                ),
                // rate
                IconListTile(
                  title: 'rate'.tr,
                  icon: MatjariIcons.star,
                  iconMatchTextDirection: true,
                  onTap: () {
                    AboutAppController.instance.rateApp();
                  },
                  withDivider: false,
                ),
                InkWell(
                  onTap: () {
                    CommonFunctions.openUrl("https://www.platform.ye/");
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text('developedBy'.tr),
                        const SizedBox(width: 8),
                        SvgPicture.asset(
                          Assets.platform,
                          height: 30,
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          );
        },
      ),
    );
  }
}
