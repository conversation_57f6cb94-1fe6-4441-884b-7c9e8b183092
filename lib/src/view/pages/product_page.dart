import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../../controllers/product_details_controller.dart';
import '../../core/utils/matjari_icons.dart';
import '../../core/values/colors.dart';
import '../../data/enums/page_status.dart';
import '../../data/models/hive/cart_item.dart';
import '../../data/models/hive/product.dart';
import '../../data/providers/local/hive_provider.dart';
import '../components/buttons/add_to_cart.dart';
import '../components/buttons/custom_filled_button.dart';
import '../components/errors/no_internet.dart';
import '../components/errors/server_error.dart';
import '../components/loading/loading.dart';
import '../components/master_page.dart';
import '../components/product/product_slider.dart';

class ProductPage extends GetView<ProductDetailsController> {
  ProductPage({super.key});

  // late Product product;
  final String? tag = ProductDetailsController.tag;

  @override
  Widget build(BuildContext context) {
    Get.put(ProductDetailsController(), tag: tag);

    return MasterPage(
      resizeToAvoidBottomInset: true,
      extendBody: false,
      bottomNavigationBar: SafeArea(
        child: Obx(
          () => controller.pageStatus.value != LoadingStatus.loaded
              ? const SizedBox.shrink()
              : Container(
                  decoration: BoxDecoration(
                      borderRadius:
                          const BorderRadius.vertical(top: Radius.circular(24)),
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.5),
                          spreadRadius: 5,
                          blurRadius: 7,
                          offset: const Offset(0, 3), //
                        )
                      ]),
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          ValueListenableBuilder<Box<Product>>(
                              valueListenable:
                                  HiveProvider.getLisenableFavoriteItem(
                                      controller.product!.id),
                              builder: (context, box, widget) {
                                var inFavorite = HiveProvider.isInFavorites(
                                    controller.product!.id);

                                return CustomFilledButton(
                                  backgroundColor: Colors.transparent,
                                  padding: const EdgeInsets.all(8),
                                  onPressed: () {
                                    inFavorite
                                        ? HiveProvider.removeFromFavorites(
                                            controller.product!.id)
                                        : HiveProvider.addToFavorites(
                                            controller.product!);
                                  },
                                  child: Icon(
                                    inFavorite
                                        ? MatjariIcons.heart_fill
                                        : MatjariIcons.heart,
                                    color:  Color(0xffba7441),
                                    size: 30,
                                  ),
                                );
                              }),
                          const SizedBox(
                            width: 32,
                          ),
                          Text(
                            '${controller.product!.itemPriceCurrencyVar}',
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Get.theme.primaryColor),
                          ),
                        ],
                      ),
                      ValueListenableBuilder<Box<CartItem>>(
                          valueListenable: HiveProvider.getLisenableCartItem(
                              controller.product!.id),
                          builder: (context, box, widget) {
                            Get.log(
                                'ProductListTile: ${controller.product!.id} from cart lisenable');

                            final cartItem = box.get(controller.product!.id);

                            return AddToCartButton(
                              productId: controller.product!.id,
                              quantity: cartItem?.quantity ?? 0,
                              onPressed: () {
                                HiveProvider.addCartItem(
                                  CartItem(
                                      product: controller.product!,
                                      quantity: 1),
                                );
                              },
                              fontSize: 14,
                            );
                          }),
                    ],
                  ),
                ),
        ),
      ),
      body: Obx(() {
        if (controller.pageStatus.value == LoadingStatus.initial) {
          return const SizedBox.shrink();
        }
        if (controller.pageStatus.value == LoadingStatus.loading) {
          return const Loading();
        } else if (controller.pageStatus.value == LoadingStatus.error) {
          return ServerError(
            onRetry: () {
              controller.getProduct(Get.arguments is Map
                  ? Get.arguments["product_id"]
                  : controller.product?.id);
            },
          );
        } else if (controller.pageStatus.value == LoadingStatus.networkError) {
          return NoInternet(onRetry: () {
            controller.getProduct(Get.arguments is Map
                ? Get.arguments["product_id"]
                : controller.product?.id);
          });
        }
        final itemImages = [controller.product!.image];
        // itemImages.addAll(controller.product!.itemImages ?? []);

        return SingleChildScrollView(
          primary: false,
          padding:
              const EdgeInsets.only(right: 16, left: 16, bottom: 8, top: 16),
          child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: ProductSlider(slides: itemImages),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        controller.product?.name ?? "",
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, height: 1.5),
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Text(
                    controller.product?.description ?? "",
                    style: TextStyle(
                        height: 1.7,
                        color: Colors.black.withValues(alpha: 0.6)),
                  ),
                )
              ]),
        );
      }),
    );
  }
}
