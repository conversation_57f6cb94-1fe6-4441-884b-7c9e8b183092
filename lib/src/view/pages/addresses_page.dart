import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/address_controller.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/data/enums/page_status.dart';
import 'package:matjari/src/view/components/errors/no_internet.dart';
import 'package:matjari/src/view/components/errors/server_error.dart';
import 'package:matjari/src/view/components/loading/loading.dart';
import 'package:matjari/src/view/components/master_page.dart';

import '../components/list_views/address_list_view.dart';

class AddressesPage extends GetView<AddressController> {
  final bool forSelecting;
  const AddressesPage({
    Key? key,
    this.forSelecting = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MasterPage(
      title: 'myAddresses'.tr,
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      floatingActionButton: Obx(() => controller.addresses.value.isNotEmpty
          ? FloatingActionButton(
              backgroundColor: Get.theme.primaryColor,
              onPressed: () {
                Get.toNamed(Routes.MANAGE_ADDRESS_PAGE);
              },
              child: const Icon(MatjariIcons.plus),
            )
          : const SizedBox()),
      body: RefreshIndicator(
        onRefresh: () async {
          await controller.getAllAddresses();
        },
        child: Obx(() {
          if (controller.pageStatus.value == LoadingStatus.loading) {
            return const Loading();
          } else if (controller.pageStatus.value == LoadingStatus.error) {
            return ServerError(
              onRetry: () {
                controller.getAllAddresses();
              },
            );
          } else if (controller.pageStatus.value ==
              LoadingStatus.networkError) {
            return NoInternet(onRetry: () {
              controller.getAllAddresses();
            });
          }
          return AdressListView(
            addresses: controller.addresses.value,
          );
        }),
      ),
    );
  }
}
