import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:matjari/src/controllers/splash_controller.dart';
import 'package:matjari/src/core/values/colors.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../core/values/assets.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class SplashPage extends GetView<SplashController> {
  const SplashPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
          child: SizedBox(
        width: double.maxFinite,
        child: Stack(
          fit: StackFit.expand,
          alignment: Alignment.center,
          // mainAxisAlignment: MainAxisAlignment.center,
          // crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Flexible(
            //     flex: 2,
            //     fit: FlexFit.tight,
            //     child: Container(
            //       height: 140,
            //       width: 140,
            //       padding: const EdgeInsets.only(top: 110.0),
            //       child: const FadeInSvg(svgAssets: Assets.logo),
            // )),
            LottieBuilder.asset(Assets.castle),

            Positioned(
                bottom: 0,
                child: FutureBuilder<PackageInfo>(
                    future: PackageInfo.fromPlatform(),
                    builder: (context, asyncSnapshot) {
                      if (asyncSnapshot.data == null) {
                        return SizedBox.shrink();
                      }
                      return Text(
                        "v${asyncSnapshot.data!.version}+${asyncSnapshot.data!.buildNumber}",
                        style: TextStyle(
                            color: AppColors.primaryColor,
                            fontSize: 14,
                            fontWeight: FontWeight.bold),
                      );
                    }))

            // Padding(
            //   padding: const EdgeInsets.all(50.0),
            //   child: SpinKitCircle(
            //     color: Get.theme.primaryColor,
            //     // size: 28,
            //   ),
            // )
          ],
        ),
      )),
    );
  }
}

class FadeInSvg extends StatefulWidget {
  const FadeInSvg({super.key, required this.svgAssets});
  final String svgAssets;
  @override
  State<FadeInSvg> createState() => _FadeInSvgState();
}

class _FadeInSvgState extends State<FadeInSvg> {
  double opacity = 0.0;
  @override
  void initState() {
    super.initState();

    Future.delayed(const Duration(microseconds: 500)).then((value) {
      setState(() {
        opacity = 1.0;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: opacity,
      duration: const Duration(seconds: 2),
      child: SvgPicture.asset(
        widget.svgAssets,
        height: 140,
        width: 140,
      ),
    );
  }
}
