import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/values/app_styles.dart';
import 'package:matjari/src/data/models/page_info.dart';
import 'package:matjari/src/view/components/images/custom_cached_network_image.dart';
import 'package:matjari/src/view/components/master_page.dart';

class AboutUsPage extends StatelessWidget {
  const AboutUsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    PageInfo pageInfo = Get.arguments;
    return MasterPage(
      title: pageInfo.pageTitle,
      body: SingleChildScrollView(
        padding: AppStyles.pagesPadding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (pageInfo.pageImage.isNotEmpty)
              CustomCachedNetworkImage(
                margin: const EdgeInsets.only(bottom: 16),
                imageUrl: pageInfo.pageImage,
              ),
            SizedBox(
              width: double.maxFinite,
              child: Text(
                pageInfo.pageContent,
                style:const TextStyle(height: 1.7),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
