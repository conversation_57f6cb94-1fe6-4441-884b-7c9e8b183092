import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/core/values/colors.dart';
import 'package:matjari/src/data/models/order.dart';
import 'package:matjari/src/view/components/images/custom_cached_network_image.dart';
import 'package:matjari/src/view/components/map/route_view_live_state.dart';

import '/src/view/components/master_page.dart';

class MapRoutePage extends StatelessWidget {
  const MapRoutePage({super.key});

  @override
  Widget build(BuildContext context) {
    Order order = Get.arguments;
    return Container();
    // return MasterPage(
    //   body: Stack(children: [
    //     RouteViewLive(
    //       margin: const EdgeInsets.only(bottom: 50),
    //       orderCoordinate: LatLng(order.orderLatitude, order.orderLongitude),
    //       lineColor: Get.theme.primaryColor,
    //       clientName: order.orderClientName,
    //       driverName: order.orderDriverName,
    //       dbRefPath: "drivers/${order.orderDriverId}/${order.orderId}",
    //     ),
    //     // PositionedDirectional(
    //     //   height: 60,
    //     //   child: Container(
    //     //     width: 40,
    //     //     height: 60,
    //     //     child: CustomCachedNetworkImage(
    //     //       imageUrl: "",
    //     //     ),
    //     //   ),
    //     // ),
    //     Positioned(
    //       height: 50,
    //       bottom: 0,
    //       right: 0,
    //       left: 0,
    //       child: InkWell(
    //         onTap: () {
    //           CommonFunctions.openUrl('tel://${order.orderDriverMobile}');
    //         },
    //         child: Container(
    //           height: double.maxFinite,
    //           decoration: BoxDecoration(
    //               color: Colors.grey.shade50,
    //               border: const Border(top: BorderSide(color: Colors.white))),
    //           padding: const EdgeInsetsDirectional.only(start: 24),
    //           child: Row(
    //             children: [
    //               // const SizedBox(
    //               //   width: 70,
    //               // ),
    //               Text(
    //                 order.orderDriverName,
    //                 style: const TextStyle(fontWeight: FontWeight.bold),
    //               ),
    //               const SizedBox(
    //                 width: 14,
    //               ),
    //               Icon(
    //                 MatjariIcons.phone,
    //                 color: Get.theme.primaryColor,
    //               )
    //             ],
    //           ),
    //         ),
    //       ),
    //     ),
    //   ]),
    // );
  }
}
