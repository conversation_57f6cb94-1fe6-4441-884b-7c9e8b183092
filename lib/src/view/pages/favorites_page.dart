import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';
import 'package:matjari/src/view/components/list_views/product_list_view.dart';
import 'package:matjari/src/view/components/master_page.dart';

import '../../data/models/hive/product.dart';

class FavoritesPage extends StatelessWidget {
  const FavoritesPage({super.key, this.hideAppBar = false});

  final bool hideAppBar;
  @override
  Widget build(BuildContext context) {
    return MasterPage(
      title: 'myFavorite'.tr,
      hideAppBar: hideAppBar,
      body: ValueListenableBuilder<Box<Product>>(
        valueListenable: HiveProvider.favorites,
        builder: (context, box, widget) {
          var products = box.values.toList();
          return ProductListView(
            favorites: true,
            products: products,
            physics: const ScrollPhysics(),
          );
        },
      ),
    );
  }
}
