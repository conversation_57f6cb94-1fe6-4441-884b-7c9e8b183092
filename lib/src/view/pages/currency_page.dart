import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/data/enums/page_status.dart';
import 'package:matjari/src/data/models/currency.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';
import 'package:matjari/src/data/services/init_service.dart';
import 'package:matjari/src/data/services/network_service.dart';
import 'package:matjari/src/view/components/buttons/custom_filled_button.dart';
import 'package:matjari/src/view/components/errors/no_internet.dart';
import 'package:matjari/src/view/components/errors/server_error.dart';
import 'package:matjari/src/view/components/list_titles/currency_list_tail.dart';
import 'package:matjari/src/view/components/loading/loading.dart';

class CurrencyPage extends StatefulWidget {
  const CurrencyPage({super.key});

  @override
  State<CurrencyPage> createState() => _CurrencyPageState();
}

class _CurrencyPageState extends State<CurrencyPage> {
  @override
  void initState() {
    if (InitService.instance.currencies.value.isEmpty) {
      InitService.instance
          .getCurrencies()
          .then((value) => animatedListOnLoad());
    } else {
      animatedListOnLoad();
    }

    if (Get.arguments is Currency) {
      selectedCurrency = Get.arguments;

      tapedListTailIndex = InitService.instance.currencies.value.indexWhere(
          (element) => element.currencyId == selectedCurrency?.currencyId);
    }

    super.initState();
  }

  void animatedListOnLoad() async {
    for (int i = 0; i < InitService.instance.currencies.value.length; i++) {
      await Future.delayed(const Duration(milliseconds: 200))
          .then((value) => animatedListKey.currentState?.insertItem(i));
    }
  }

  var animatedListKey = GlobalKey<AnimatedListState>();

  var tapedListTailIndex = -1;
  Currency? selectedCurrency;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: !Navigator.of(context).canPop()
          ? null
          : AppBar(
              systemOverlayStyle: const SystemUiOverlayStyle(
                  statusBarIconBrightness: Brightness.dark),
              leading: IconButton(
                icon: const Icon(
                  MatjariIcons.back,
                  size: 20,
                ),
                onPressed: () => Get.back(),
              ),
              backgroundColor: Colors.transparent,
            ),
      bottomNavigationBar: tapedListTailIndex == -1
          ? null
          : TweenAnimationBuilder(
              duration: const Duration(milliseconds: 300),
              tween: Tween(begin: 1.0, end: 0.0),
              builder: (context, double value, child) {
                return AnimatedSlide(
                  curve: Curves.easeInOut,
                  offset: Offset(0, value),
                  duration: const Duration(milliseconds: 500),
                  // crossFadeState: 0,
                  // opacity: value??00,
                  child: Container(
                    margin: const EdgeInsets.symmetric(
                        horizontal: 10, vertical: 10),
                    child: CustomFilledButton(
                      fullWidth: true,
                      child: Text("save".tr),
                      onPressed: () {
                        if (HiveProvider.allCartItems.isNotEmpty ||
                            HiveProvider.favoriteProducts.isNotEmpty) {
                          NetworkService.instance.checkConnectivity(() async {
                            HiveProvider.putSelectedCurrency(selectedCurrency!)
                                .then((value) {
                              //sync cart items
                              InitService.instance.checkProductsAvailability();
                            });

                            if (Get.arguments is Currency) {
                              Get.back();
                            } else {
                              Get.offAndToNamed(Routes.HOME_PAGE);
                            }
                          });
                        } else {
                          HiveProvider.putSelectedCurrency(selectedCurrency!);

                          if (Get.arguments is Currency) {
                            Get.back();
                          } else {
                            Get.offAndToNamed(Routes.HOME_PAGE);
                          }
                        }
                      },
                    ),
                  ),
                );
              }),
      body: SafeArea(
        child: Obx(() {
          if (InitService.instance.currencyPageStatus.value ==
              LoadingStatus.loading) {
            return Center(
              child: Loading(
                height: Get.height * 0.6,
              ),
            );
          } else if (InitService.instance.currencyPageStatus.value ==
              LoadingStatus.networkError) {
            return Center(
              child: NoInternet(onRetry: () {
                InitService.instance
                    .getCurrencies()
                    .then((value) => animatedListOnLoad());
              }),
            );
          } else if (InitService.instance.currencyPageStatus.value ==
              LoadingStatus.error) {
            return Center(child: ServerError(
              onRetry: () {
                InitService.instance
                    .getCurrencies()
                    .then((value) => animatedListOnLoad());
              },
            ));
          }

          return Center(
            // alignment: Alignment.topCenter,
            child: SingleChildScrollView(
              child: Container(
                margin: const EdgeInsets.only(top: 50),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(bottom: 20),
                      child: SvgPicture.asset(
                        Assets.logo,
                        width: 120,
                      ),
                    ),
                    Text(
                      "selectCurrency".tr,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontSize: 18, color: Theme.of(context).primaryColor),
                    ),
                    Container(
                      margin: const EdgeInsets.symmetric(
                          vertical: 28, horizontal: 18),
                      child: AnimatedList(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        key: animatedListKey,
                        initialItemCount: 0,
                        itemBuilder: (context, index, animation) {
                          return SlideTransition(
                              position: animation.drive(Tween<Offset>(
                                begin: const Offset(1, 0),
                                end: const Offset(0, 0),
                              )),
                              child: CurrencyListTail(
                                  key: Key('${index + tapedListTailIndex}'),
                                  currentIndex: index,
                                  tapedListTailIndex: tapedListTailIndex,
                                  onTap: () {
                                    setState(() {
                                      tapedListTailIndex = index;
                                    });
                                    selectedCurrency = InitService
                                        .instance.currencies.value[index];
                                  },
                                  currency: InitService
                                      .instance.currencies.value[index]));
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
