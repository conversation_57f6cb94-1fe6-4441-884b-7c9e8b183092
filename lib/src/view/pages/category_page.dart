import 'package:badges/badges.dart';
import 'package:flutter/material.dart' hide Badge;
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:matjari/src/controllers/category_controller.dart';
import 'package:matjari/src/controllers/home_controller.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/utils/extenstions/double_extenstion.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/core/values/colors.dart';
import 'package:matjari/src/data/enums/page_status.dart';
import 'package:matjari/src/data/models/hive/cart_item.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';
import 'package:matjari/src/view/components/buttons/custom_filled_button.dart';
import 'package:matjari/src/view/components/errors/no_internet.dart';
import 'package:matjari/src/view/components/list_views/product_list_view.dart';
import 'package:matjari/src/view/components/loading/loading.dart';
import 'package:matjari/src/view/components/master_page.dart';

import '../../core/values/assets.dart';
import '../components/category/category_filter.dart';
import '../components/tab_bars/category_tab_bar.dart';

// ignore: must_be_immutable
class CategoryPage extends StatelessWidget {
  CategoryPage({super.key});
  final scrollController = Rx<ScrollController>(ScrollController());
  var atEnd = Rx<bool>(false);
  final String? tag = Get.arguments.toString();

  final CategoryController controller = CategoryController.instance;

  @override
  Widget build(BuildContext context) {
    if (!Get.isRegistered<CategoryController>(tag: Get.arguments.toString())) {
      Get.put(CategoryController(id: Get.arguments),
          tag: Get.arguments.toString());
    }

    scrollController.value.addListener(() {
      // Do something when the scroll position changes.

      if (scrollController.value.position.atEdge && atEnd.value == false) {
        if ((controller.currentPage != controller.maxPage) &&
            controller.nextPageStatus.value != LoadingStatus.loading) {
          controller
              .getCategoryProducts(controller.currentPage + 1)
              .then((value) {
            atEnd.value = false;
          });
        }
      }
      atEnd.value = scrollController.value.position.atEdge;
    });

    return MasterPage(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CategoryTabBar(
            categories: HomeController.instance.sections.value,
          ),
          // CategoryFilter(
          //   onSelected: (value) {
          //     controller.filter = value;

          //     controller.getCategoryProducts();
          //   },
          // ),
          Obx(
            () {
              // if (Get.find<CategoryController()>() ) {
              //   return const SizedBox.shrink();
              // }
              if (controller.isClosed) {
                return const SizedBox.shrink();
              }
              if (controller.firtPageStatus.value == LoadingStatus.loading) {
                return Expanded(
                  child: Loading(
                    height: Get.height * 0.6,
                  ),
                );
              }
              if (controller.firtPageStatus.value ==
                  LoadingStatus.networkError) {
                return Expanded(
                  child: Center(
                    child: NoInternet(onRetry: () {
                      controller.getCategoryProducts();
                    }),
                  ),
                );
              }
              return Expanded(
                child: SingleChildScrollView(
                  controller: scrollController.value,
                  child: Column(
                    children: [
                      ProductListView(products: controller.products.value),
                      //pagination
                      Obx(() {
                        if (controller.nextPageStatus.value ==
                            LoadingStatus.loading) {
                          return Container(
                            margin: const EdgeInsets.only(bottom: 30),
                            child: const Loading(
                              height: 60,
                            ),
                          );
                        }
                        return const SizedBox(
                          height: 70,
                        );
                      })
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      floatingActionButton: ValueListenableBuilder<Box<CartItem>>(
          valueListenable: HiveProvider.cartItems,
          builder: (context, box, widget) {
            var total = HiveProvider.cartItemsTotal;
            var count = HiveProvider.cartItemsCount;
            if (total > 0) {
              return Material(
                color: Colors.transparent,
                shadowColor: Colors.transparent,
                child: CustomFilledButton(
                    padding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Badge(
                          position: BadgePosition.custom(top: 0, start: 0),
                          badgeStyle: BadgeStyle(
                            padding: const EdgeInsets.all(6),
                            badgeColor: AppColors.redColor,
                          ),
                          badgeContent: Center(
                            child: Text(
                              count.toString(),
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          child: SvgPicture.asset(
                            Assets.logoIcon,
                            height: 25,
                            // width: 50,
                            colorFilter:
                                ColorFilter.mode(Colors.white, BlendMode.srcIn),
                            // size: 50,
                          ),
                        ),
                        const SizedBox(width: 5),
                        Text(
                          '${total.toStringValue()}  ${HomeController.instance.currencyCode}',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                    onPressed: () {
                      Get.toNamed(Routes.CART_PAGE);
                    }),
              );
            }
            return FloatingActionButton(
                heroTag: "cart",
                onPressed: () {
                  Get.toNamed(Routes.CART_PAGE);
                },
                backgroundColor: Get.theme.primaryColor,
                child: SvgPicture.asset(
                  Assets.logoIcon,
                  height: 30,
                  // width: 50,
                  colorFilter: ColorFilter.mode(Colors.white, BlendMode.srcIn),
                  // size: 50,
                ));
          }),
    );
  }
}
