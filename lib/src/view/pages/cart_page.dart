import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:matjari/src/controllers/cart_controller.dart';
import 'package:matjari/src/controllers/home_controller.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/utils/extenstions/double_extenstion.dart';
import 'package:matjari/src/core/utils/validator.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/data/models/hive/cart_item.dart';
import 'package:matjari/src/view/components/buttons/custom_filled_button.dart';
import 'package:matjari/src/view/components/errors/no_data.dart';
import 'package:matjari/src/view/components/master_page.dart';

import '../../core/utils/common_functions.dart';
import '../../data/providers/local/hive_provider.dart';
import '../../data/services/auth_services.dart';
import '../components/form_fields/select_address_form_field.dart';
import '../components/list_views/cart_list_view.dart';

class CartPage extends StatelessWidget {
  const CartPage({super.key, this.hideAppBar = false});
  final bool hideAppBar;
  @override
  Widget build(BuildContext context) {
    if (!Get.isRegistered<CartController>()) {
      Get.put(CartController());
    }
    // form key
    final formKey = GlobalKey<FormState>();
    return MasterPage(
      title: 'cart'.tr,
      hideAppBar: hideAppBar,
      body: ValueListenableBuilder<Box<CartItem>>(
          valueListenable: HiveProvider.cartItems,
          builder: (context, box, widget) {
            var cartItems = box.values.toList();
            if (cartItems.isEmpty) {
              return NoData(
                message: 'emptyCart'.tr,
                svgIcon: Assets.emptyCart,
                actionLabel: "continueShopping".tr,
                onAction: () {
                  HomeController.instance.pageViewController.jumpToPage(0);
                  Get.back();
                },
              );
            }
            return Column(
              children: [
                // const WorkhoursMarguee(),
                Expanded(
                  child: SingleChildScrollView(
                    child: Form(
                      key: formKey,
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      child: Column(
                        children: [
                          SelectAddressFormField(validator: (value) {
                            return Validator.validate(
                              rules: ["required"],
                              value: value,
                              fieldName: 'deliveryAddress'.tr,
                            );
                          }, onSaved: (value) {
                            CartController.instance.selectedAddress.value =
                                value;
                          }, onChange: (value) {
                            CartController.instance
                                .getPaymentMethods(value?.addressId);
                          }),
                          CartListView(
                            cartItems: cartItems,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Container(
                  width: double.maxFinite,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.3),
                        blurRadius: 5,
                        offset: const Offset(0, -1),
                      ),
                    ],
                    color: Colors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'cartTotal'.tr,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            ValueListenableBuilder<Box<CartItem>>(
                                valueListenable: HiveProvider.cartItems,
                                builder: (context, box, widget) {
                                  var total = HiveProvider.cartItemsTotal;
                                  return Text(
                                    "${total.toStringValue()} ${HomeController.instance.currencyCode}",
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Get.theme.primaryColor,
                                    ),
                                  );
                                }),
                          ],
                        ),
                      ),
                      CustomFilledButton(
                        fullWidth: true,
                        margin: const EdgeInsets.only(
                          top: 16,
                        ),
                        child: Text("Place order".tr),
                        onPressed: () {
                          if (AuthService.instance.isLoggedIn) {
                            if (formKey.currentState!.validate()) {
                              formKey.currentState!.save();
                              Get.toNamed(Routes.CHECKOUT_PAGE);
                            }
                          } else {
                            CommonFunctions.showLoginConfirmBottomSheet();
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ],
            );
          }),
    );
  }
}
