import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/about_app_controller.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/core/values/app_styles.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/data/services/auth_services.dart';
import 'package:matjari/src/data/vms/phone_number_vm.dart';
import 'package:matjari/src/view/components/buttons/custom_filled_button.dart';

import '../components/form_fields/phone_number_form_field.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  @override
  void initState() {
    if (!Get.isRegistered<AboutAppController>()) {
      Get.put(AboutAppController());
    } else {
      //get app configration
      AboutAppController.instance.getAboutInfo();
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // form key
    final formKey = GlobalKey<FormState>();
    // phone number
    PhoneNumberVM phoneNumber = PhoneNumberVM(countryCode: '', phoneNumber: '');
    // country code
    String countryCode = "";
    AuthService.instance.getLoginCountries();

    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarIconBrightness: Brightness.dark),
        leading: IconButton(
          icon: const Icon(
            MatjariIcons.back,
            size: 20,
          ),
          onPressed: () => Get.back(),
        ),
        backgroundColor: Colors.transparent,
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: AppStyles.authPagesPadding,
          child: Form(
            key: formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  margin: const EdgeInsets.only(bottom: 50),
                  child: SvgPicture.asset(
                    Assets.logo,
                    width: 130,
                  ),
                ),
                Obx(() => PhoneNumberTextFormField(
                      initialCountry: "YE",
                      favoriteCountries: const ["YE", "SA", "EG"],
                      filteredCountries: AuthService
                          .instance.loginCountries.value
                          .map((e) => e.countryDialCode.toString())
                          .toList(),
                      keyboardType: TextInputType.phone,
                      textInputAction: TextInputAction.done,
                      onSave: (value) {
                        phoneNumber = value;
                      },
                      onChanged: (value) {
                        // print(value);
                      },
                      hintText: 'رقم المحمول',
                    )),
                CustomFilledButton(
                  onPressed: () {
                    CommonFunctions.unfocusNodes();
                    if (formKey.currentState?.validate() ?? false) {
                      formKey.currentState?.save();

                      if (phoneNumber.phoneNumber.isNotEmpty) {
                        if (phoneNumber.phoneNumber.length < 7) {
                          CommonFunctions.showErrorMessage(
                              "رقم المحمول غير صالح");
                        } else {
                          AuthService.instance.login(phoneNumber);
                        }
                      }
                    }
                  },
                  fullWidth: true,
                  margin: const EdgeInsets.only(top: 20, bottom: 32),
                  child: const Text('دخول'),
                ),
                Obx(() => Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // facebook
                        if (AboutAppController.instance.configurations.value
                                ?.appSignInWith?.facebookLogin ??
                            false)
                          SocialLoginButton(
                            backgroundColor: Colors.blue,
                            icon: Assets.facebook,
                            onPressed: () {
                              // AuthService.instance.signInWithFacebook();
                            },
                          ),
                        // google
                        if (AboutAppController.instance.configurations.value
                                ?.appSignInWith?.googleLogin ??
                            false)
                          SocialLoginButton(
                            backgroundColor: Colors.white,
                            icon: Assets.google,
                            onPressed: () {
                              AuthService.instance.signInWithGoogle();
                            },
                          ),

                        // apple
                        if (AboutAppController.instance.configurations.value
                                ?.appSignInWith?.appleLogin ??
                            false)
                          SocialLoginButton(
                            backgroundColor: Colors.black,
                            icon: Assets.apple,
                            onPressed: () {
                              AuthService.instance.signInWithApple();
                            },
                          ),
                      ],
                    ))
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class SocialLoginButton extends StatelessWidget {
  final String icon;
  final VoidCallback onPressed;
  final Color backgroundColor;
  const SocialLoginButton({
    Key? key,
    required this.icon,
    required this.onPressed,
    required this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Material(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        elevation: 1.5,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(
              10.0,
            ),
            child: SvgPicture.asset(
              icon,
              width: 30,
              height: 30,
            ),
          ),
        ),
      ),
    );
  }
}
