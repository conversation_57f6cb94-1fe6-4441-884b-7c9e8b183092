import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/order_controller.dart';
import 'package:matjari/src/view/components/master_page.dart';
import '../../data/enums/page_status.dart';
import '../components/errors/no_data.dart';
import '../components/errors/no_internet.dart';
import '../components/errors/server_error.dart';
import '../components/list_views/order_list_view.dart';
import '../components/loading/loading.dart';

class OrdersPage extends GetView<OrderController> {
  OrdersPage({Key? key}) : super(key: key);
  final scrollController = Rx<ScrollController>(ScrollController());
  var atEnd = Rx<bool>(false);

  @override
  Widget build(BuildContext context) {
    scrollController.value.addListener(() {
      // Do something when the scroll position changes.

      if (scrollController.value.position.atEdge && atEnd.value == false) {
        if ((controller.currentPage != controller.maxPage) &&
            controller.nextPageStatus.value != LoadingStatus.loading) {
          controller.getOrders(controller.currentPage + 1).then((value) {
            atEnd.value = false;
          });
        }
      }

      atEnd.value = scrollController.value.position.atEdge;
    });

    return MasterPage(
      title: 'myOrders'.tr,
      body: Obx(() {
        if (controller.firtPageStatus.value == LoadingStatus.loading) {
          return const Loading();
        } else if (controller.firtPageStatus.value == LoadingStatus.error) {
          return ServerError(
            onRetry: () {
              controller.getOrders();
            },
          );
        } else if (controller.firtPageStatus.value ==
            LoadingStatus.networkError) {
          return NoInternet(onRetry: () {
            controller.getOrders();
          });
        }
        return controller.orders.value.isEmpty
            ? NoData(
                message: "noOrderMessage".tr,
              )
            : RefreshIndicator(
                onRefresh: () async {
                  controller.getOrders();
                },
                child: SingleChildScrollView(
                  controller: scrollController.value,
                  physics: const AlwaysScrollableScrollPhysics(),

                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Obx(() => OrdersListView(
                            orders: controller.orders.value,
                          )),
                      //pagination
                      Obx(() {
                        if (controller.nextPageStatus.value ==
                            LoadingStatus.loading) {
                          return Container(
                            height: 60,
                            margin: const EdgeInsets.only(bottom: 30),
                            child: const Loading(
                              height: 60,
                            ),
                          );
                        }
                        return controller.nextPageStatus.value ==
                                LoadingStatus.loaded
                            ? const SizedBox.shrink()
                            : const SizedBox(
                                height: 80,
                              );
                      })
                    ],
                  ),
                  // ),
                ));
      }),
    );
  }
}
