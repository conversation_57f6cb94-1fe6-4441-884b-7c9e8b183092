import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:matjari/src/controllers/manage_address_controller.dart';
import 'package:matjari/src/core/utils/validator.dart';
import 'package:matjari/src/core/values/colors.dart';
import 'package:matjari/src/view/components/bottom_sheets/address_form_bottom_sheet.dart';
import 'package:matjari/src/view/components/buttons/custom_filled_button.dart';
import 'package:matjari/src/view/components/form_fields/google_map_form_field.dart';
import 'package:matjari/src/view/components/master_page.dart';

import '../components/others/dot.dart';

class ManageAddressPage extends GetView<ManageAddressController> {
  const ManageAddressPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // selected position
    // CameraPosition selectedPosition = controller.intialCameraPosition.value;
    // form key
    var formKey = GlobalKey<FormState>();
    // address
    late LatLng currentPosition;
    var editMode = controller.address != null;
    return MasterPage(
      title: !editMode ? 'addAddress'.tr : 'editAddress'.tr,
      body: Form(
        key: formKey,
        child: Stack(
          children: [
            Column(
              children: [
                Expanded(
                  child: GoogleMapFormField(
                      initialPosition: editMode
                          ? CameraPosition(
                              zoom: 13.49445915222168,
                              target: LatLng(
                                controller.address!.latitude,
                                controller.address!.longitude,
                              ),
                            )
                          : null,
                      onSaved: (position) {
                        if (position != null) {
                          currentPosition = LatLng(position.target.latitude,
                              position.target.longitude);
                        }
                      },
                      marker: editMode
                          ? Marker(
                              markerId: const MarkerId('marker'),
                              position: LatLng(controller.address!.latitude,
                                  controller.address!.longitude),
                            )
                          : null,
                      initialCameraPosition: const CameraPosition(
                          target: LatLng(15.354048771779711, 44.18771378695965),
                          zoom: 13.49445915222168),
                      onChanged: (position) {
                        if (kDebugMode) {
                          print(position);
                        }
                      },
                      validator: (position) {
                        return Validator.validate(
                          value: position,
                          rules: ["required"],
                          fieldName: 'mapLocation'.tr,
                        );
                      }),
                ),
                const SizedBox(height: 120),
              ],
            ),
            Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                  color: Colors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                height: 150,
                width: double.maxFinite,
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        dot(
                          size: 10,
                          color: Get.theme.primaryColor,
                        ),
                        dot(
                          size: 10,
                          color: AppColors.greyColor,
                        )
                      ],
                    ),
                    const Spacer(),
                    Text('قم بتحديد موقعك على الخريطة'.tr,
                        style: const TextStyle()),
                    const Spacer(),
                    CustomFilledButton(
                      fullWidth: true,
                      onPressed: () {
                        if (formKey.currentState!.validate()) {
                          formKey.currentState?.save();
                          Get.bottomSheet(
                            AddressFormBottomSheet(
                              lang: currentPosition.longitude,
                              lat: currentPosition.latitude,
                            ),
                            isScrollControlled: true,
                          );
                        }
                      },
                      child: const Text(
                        "التالي >",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
