import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/values/colors.dart';

class AboutGroupLabel extends StatelessWidget {
  final String label;
  const AboutGroupLabel({Key? key, required this.label}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      padding: const EdgeInsets.all(16),
      color: AppColors.greyColor,
      child: Text(
        label,
        textAlign: TextAlign.start,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Get.theme.primaryColor,
        ),
      ),
    );
  }
}
