import 'package:flutter/material.dart';
import 'package:matjari/src/core/values/colors.dart';

class ContactCircle extends StatelessWidget {
  const ContactCircle({super.key, required this.icon, this.onTap});

  final IconData icon;
  final void Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(999),
      child: Container(
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.primaryColor,
          // border: Border.all(color: Colors.grey.shade400),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 25,
        ),
      ),
    );
  }
}
