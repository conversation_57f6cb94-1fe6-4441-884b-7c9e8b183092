import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../core/values/assets.dart';

class AboutHeader extends StatelessWidget {
  final String appDescription;
  final String version;
  const AboutHeader(
      {Key? key, required this.appDescription, required this.version})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      padding: const EdgeInsets.only(
        top: 16.0,
        right: 16,
        left: 16,
        bottom: 16,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Column(
            children: [
              SvgPicture.asset(
                Assets.logo,
                width: 40,
              ),
              const SizedBox(
                height: 8,
              ),
              Text(
                '${'version'.tr} $version',
                style: const TextStyle(
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          const SizedBox(
            width: 16,
          ),
          Expanded(
            child: Text(
              appDescription,
              // textAlign: TextAlign.justify,
              style: const TextStyle(height: 1.5, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }
}
