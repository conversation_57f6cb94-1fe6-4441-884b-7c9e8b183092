import 'package:flutter/material.dart';
import 'package:matjari/src/data/models/section.dart';

import '../grid_tiles/category_grid_tile.dart';

class HomeCategoriesGridView extends StatelessWidget {
  final List<Section> categories;
  const HomeCategoriesGridView({
    super.key,
    required this.categories,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1 / 1.1,
        mainAxisSpacing: 16,
        crossAxisSpacing: 16,
      ),
      padding: const EdgeInsets.only(
        top: 16,
        right: 16,
        left: 16,
        bottom: 16,
      ),
      itemCount: categories.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (BuildContext context, int index) {
        return CategoryGridTile(category: categories[index]);
      },
    );
  }
}
