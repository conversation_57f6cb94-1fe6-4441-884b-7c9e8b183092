import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../core/utils/matjari_icons.dart';
import '../../../core/values/app_styles.dart';
import '../../../core/values/country_codes.dart';
import '../../../data/vms/phone_number_vm.dart';
import '../bottom_sheets/select_country_bottom_sheet.dart';

class PhoneNumberTextFormField extends StatefulWidget {
  final TextStyle? style;
  final void Function(PhoneNumberVM value)? onSave;
  final String? Function(PhoneNumberVM? value)? validator;
  final void Function(String? value)? onChanged;
  final int? maxLine;
  final Color? borderColor;
  final Widget? lable;
  final double? borderWidth;
  final Widget? prefixIcon;
  final Widget? prefix;
  final Widget? suffix;
  final EdgeInsetsGeometry? margin;
  final String? hintText;
  final String? titleText;
  final bool obscureText;
  final TextInputType keyboardType;
  final int? maxLength;
  final int? minLines;
  final String? initialValue;
  final Widget? suffixIcon;
  final TextEditingController? controller;
  final bool readOnly;
  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final TextAlign? textAlign;
  final Color? fillColor;
  final bool? isDense;
  final EdgeInsetsGeometry? contentPadding;
  final void Function(String?)? onFieldSubmitted;
  final List<TextInputFormatter> inputFormatters;
  final String initialCountry;
  final List<String> favoriteCountries;
  final List<String>? filteredCountries;
  const PhoneNumberTextFormField({
    Key? key,
    this.onSave,
    this.validator,
    this.onChanged,
    this.maxLine,
    this.lable,
    this.prefixIcon,
    this.hintText,
    this.titleText,
    this.obscureText = false,
    this.keyboardType = TextInputType.phone,
    this.maxLength,
    this.minLines,
    this.initialValue,
    this.suffixIcon,
    this.controller,
    this.readOnly = false,
    this.margin,
    this.focusNode,
    this.onFieldSubmitted,
    this.textInputAction,
    this.inputFormatters = const [],
    this.borderWidth,
    this.borderColor,
    this.prefix,
    this.suffix,
    this.textAlign,
    this.style,
    this.fillColor,
    this.isDense,
    this.contentPadding,
    required this.initialCountry,
    this.favoriteCountries = const [],
    this.filteredCountries,
  }) : super(key: key);

  @override
  State<PhoneNumberTextFormField> createState() =>
      _PhoneNumberTextFormFieldState();
}

class _PhoneNumberTextFormFieldState extends State<PhoneNumberTextFormField> {
  late String _countryCode;
  @override
  void initState() {
    _countryCode = widget.initialCountry;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var cCode = countryCodes.firstWhere(
      (element) => element["code"] == _countryCode,
      orElse: () => countryCodes.first,
    );
    return Container(
      margin: widget.margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.titleText != null)
            Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
              child: Text(
                widget.titleText!,
                style: TextStyle(
                  color: Get.theme.primaryColor,
                ),
              ),
            ),
          Container(
            // padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: TextFormField(
                    onFieldSubmitted: widget.onFieldSubmitted,
                    textInputAction:
                        widget.textInputAction ?? TextInputAction.next,
                    focusNode: widget.focusNode,
                    readOnly: widget.readOnly,
                    maxLines: widget.maxLine ?? 1,
                    controller: widget.controller,
                    onSaved: (value) {
                      if (widget.onSave != null) {
                        if (value != null) {
                          widget.onSave!(PhoneNumberVM(
                              countryCode: (cCode["dial_code"] ?? ""),
                              phoneNumber: value));
                        }
                      }
                    },
                    onChanged: (value) {
                      if (widget.onChanged != null) {
                        widget.onChanged!((cCode["dial_code"] ?? "") + value);
                      }
                    },
                    inputFormatters: widget.inputFormatters,
                    initialValue: widget.initialValue,
                    validator: (value) {
                      if (widget.validator != null) {
                        return widget.validator!(PhoneNumberVM(
                            countryCode: (cCode["dial_code"] ?? ""),
                            phoneNumber: value ?? ""));
                      }
                      return null;
                    },
                    obscureText: widget.obscureText,
                    keyboardType: widget.keyboardType,
                    maxLength: widget.maxLength,
                    minLines: widget.minLines,
                    textAlign: widget.textAlign ?? TextAlign.start,
                    style: widget.style ??
                        const TextStyle(
                          fontSize: 14,
                        ),
                    cursorColor: Get.theme.primaryColor,
                    decoration: InputDecoration(
                      contentPadding: widget.contentPadding ??
                          const EdgeInsets.symmetric(vertical: 16),
                      fillColor: widget.fillColor,
                      isDense: true,
                      hintStyle: Get.theme.inputDecorationTheme.hintStyle,
                      border: InputBorder.none,
                      errorBorder:
                          Get.theme.inputDecorationTheme.errorBorder!.copyWith(
                        borderSide: BorderSide(
                          color: Get.theme.inputDecorationTheme.errorBorder!
                              .borderSide.color,
                          width: Get.theme.inputDecorationTheme.errorBorder!
                              .borderSide.width,
                        ),
                      ),

                      focusedBorder: OutlineInputBorder(
                          borderRadius: AppStyles.cardBorderRadius,
                          borderSide: BorderSide.none),
                      enabledBorder: OutlineInputBorder(
                          borderRadius: AppStyles.cardBorderRadius,
                          borderSide: BorderSide.none),
                      focusedErrorBorder:
                          Get.theme.inputDecorationTheme.errorBorder!.copyWith(
                        borderSide: BorderSide(
                          color: Get.theme.inputDecorationTheme.errorBorder!
                              .borderSide.color,
                          width: Get.theme.inputDecorationTheme.errorBorder!
                              .borderSide.width,
                        ),
                      ),
                      hintText: widget.hintText,
                      label: widget.lable,
                      prefixIconColor: Get.theme.primaryColor,
                      suffixIconColor: Get.theme.primaryColor,
                      // prefixIcon: widget.prefixIcon,
                      suffixIcon: widget.suffixIcon,
                      prefixStyle: const TextStyle(color: Colors.transparent),

                      // prefixText: "",
                      prefixIcon: Container(
                        // color: Get.theme.inputDecorationTheme.fillColor,
                        padding: const EdgeInsetsDirectional.only(start: 8),
                        child: InkWell(
                          onTap: () {
                            if (widget.readOnly) {
                              return;
                            }
                            Get.bottomSheet(
                              SelectCountryBottomSheet(
                                onSelect: (countryCode) {
                                  setState(() {
                                    _countryCode = countryCode!;
                                  });
                                },
                                favoriteCountries: widget.favoriteCountries,
                                initialCountry: _countryCode,
                                filteredCountries: widget.filteredCountries,
                              ),
                              isScrollControlled: true,
                            );
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                MatjariIcons.down_arrow,
                                size: 11,
                                color: Get.theme.inputDecorationTheme.hintStyle
                                    ?.color,
                              ),
                              const SizedBox(
                                width: 4,
                              ),
                              Text(
                                cCode["dial_code"] ?? "",
                                style: AppStyles.formFieldHintTextStyle,
                              ),
                              Container(
                                margin: const EdgeInsetsDirectional.only(
                                    start: 10, end: 15),
                                width: 1,
                                color: Colors.grey.withOpacity(0.5),
                                height: 20,
                              ),
                            ],
                          ),
                        ),
                      ),
                      suffix: widget.suffix,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
