import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/utils/validator.dart';
import 'package:matjari/src/data/models/payment_method.dart';
import 'package:matjari/src/view/components/form_fields/payment_method_drop_down_form_field.dart';

import 'custom_text_form_field.dart';

class PaymentFormFields extends StatelessWidget {
  const PaymentFormFields(
      {super.key,
      this.onSaved,
      this.changeOrderPayment = false,
      this.margin,
      this.showLables = true,
      required this.paymentMethods});
  final void Function(Map<String, dynamic>?)? onSaved;
  final bool changeOrderPayment;
  final bool showLables;
  final EdgeInsetsGeometry? margin;
  final List<PaymentMethod> paymentMethods;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(horizontal: 16),
      child: FormField<Map<String, dynamic>>(
          onSaved: onSaved,
          builder: (state) {
            return Column(
              children: [
                // payment method
                PaymentMethodDropDownFormField(
                  paymentMethods: paymentMethods,
                  onChanage: (value) {
                    if (kDebugMode) {
                      print("payment id ${value?.paymentId}");
                    }
                    var _value = state.value ?? {};
                    _value['payment'] = value;
                    state.didChange(_value);
                  },
                ),
                if (state.value?['payment'] != null &&
                    state.value?['payment'].paymentType == 4 &&
                    state.value?['payment'].paymentId != 6)
                  //haseb
                  if (state.value?['payment']?.paymentId == 13)
                    CustomTextFormField(
                      margin: const EdgeInsets.only(bottom: 16),
                      obscureText: true,
                      onChanged: (value) {
                        var _value = state.value ?? {};
                        _value['payment_account_id'] = value;
                        state.didChange(_value);
                      },
                      titleText: showLables ? 'pin'.tr : null,
                      hintText: 'pin_hint'.tr,
                      validator: (value) {
                        return Validator.validate(
                          rules: ["required"],
                          value: value,
                          fieldName: 'pin'.tr,
                        );
                      },
                    )
                  //
                  else if (state.value?['payment']?.paymentId == 9 ||
                      state.value?['payment']?.paymentId == 14)
                    Column(
                      children: [
                        CustomTextFormField(
                          margin: const EdgeInsets.only(bottom: 16),
                          onChanged: (value) {
                            var _value = state.value ?? {};
                            _value['payment_account_id'] = value;
                            state.didChange(_value);
                          },
                          prefixIcon: Container(
                            margin: const EdgeInsetsDirectional.only(
                                end: 5, top: 10, bottom: 10),
                            width: 10,
                            decoration: const BoxDecoration(
                                border: BorderDirectional(
                                    end: BorderSide(color: Colors.grey))),
                            child: const Center(
                              child: Text(
                                "+967",
                                style:
                                    TextStyle(fontSize: 13, color: Colors.grey),
                              ),
                            ),
                          ),
                          titleText:
                              showLables ? 'phone_mobile_account'.tr : null,
                          hintText: 'phone_mobile_account_hint'.trParams(
                              {"name": state.value?["payment"].paymentName}),
                          keyboardType: TextInputType.phone,
                          validator: (value) {
                            return Validator.validate(
                              rules: ["required", "length:9"],
                              value: value,
                              fieldName: 'phone_mobile_account'.tr,
                            );
                          },
                        ),
                        CustomTextFormField(
                          margin: const EdgeInsets.only(bottom: 16),
                          onChanged: (value) {
                            var _value = state.value ?? {};
                            _value['voucher'] = value;
                            state.didChange(_value);
                          },
                          titleText: showLables ? 'voucher_code'.tr : null,
                          hintText: 'voucher_code'.tr,
                          // keyboardType: TextInputType.phone,
                          validator: (value) {
                            return Validator.validate(
                              rules: ["required"],
                              value: value,
                              fieldName: 'voucher_code'.tr,
                            );
                          },
                        ),
                      ],
                    )
                  // else if (showMoreDetails &&
                  //     (state.value?['payment'].paymentId == 6 ||
                  //         state.value?['payment'].paymentId == 10))
                  //   CustomTextFormField(
                  //     margin: const EdgeInsets.only(bottom: 16),
                  //     onChanged: (value) {
                  //       var _value = state.value ?? {};
                  //       _value['voucher'] = value;
                  //       state.didChange(_value);
                  //     },
                  //     titleText: showLables ? 'voucher_code'.tr : null,
                  //     hintText: 'voucher_code'.tr,
                  //     keyboardType: TextInputType.phone,
                  //     validator: (value) {
                  //       return Validator.validate(
                  //         rules: ["required"],
                  //         value: value,
                  //         fieldName: 'voucher_code'.tr,
                  //       );
                  //     },
                  //   )
                  else
                    CustomTextFormField(
                      margin: const EdgeInsets.only(bottom: 16),
                      onChanged: (value) {
                        var _value = state.value ?? {};
                        _value['payment_account_id'] = value;
                        state.didChange(_value);
                      },
                      prefixIcon: Container(
                        margin: const EdgeInsetsDirectional.only(
                            end: 5, top: 10, bottom: 10),
                        width: 10,
                        decoration: const BoxDecoration(
                            border: BorderDirectional(
                                end: BorderSide(color: Colors.grey))),
                        child: const Center(
                          child: Text(
                            "+967",
                            style: TextStyle(fontSize: 13, color: Colors.grey),
                          ),
                        ),
                      ),
                      titleText: showLables ? 'phone_mobile_account'.tr : null,
                      hintText: 'phone_mobile_account_hint'.trParams(
                          {"name": state.value?["payment"].paymentName}),
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        return Validator.validate(
                          rules: ["required", "length:9"],
                          value: value,
                          fieldName: 'phone_mobile_account'.tr,
                        );
                      },
                    )
                else
                  const SizedBox(
                    height: 8,
                  ),
                //
                if (state.value?['payment'] != null &&
                    state.value?['payment'].paymentType == 2)
                  CustomTextFormField(
                    // margin: const EdgeInsets.only(top: 16),
                    hintText: "اسم المودع / رقم العملية",
                    onChanged: (value) {
                      var _value = state.value ?? {};
                      _value['transferNumber'] = value;
                      state.didChange(_value);
                    },
                    validator: (value) {
                      if (changeOrderPayment) {
                        return Validator.validate(
                          value: value,
                          rules: ["required"],
                          fieldName: 'transferNumber'.tr,
                        );
                      }
                    },
                  )
                else if (state.value?['payment'] != null &&
                    state.value?['payment'].paymentType == 3)
                  Column(
                    children: [
                      CustomTextFormField(
                        margin: const EdgeInsets.only(bottom: 16),
                        hintText: "اسم شركة الصرافة",
                        onChanged: (value) {
                          var _value = state.value ?? {};
                          _value['transferCompany'] = value;
                          state.didChange(_value);
                        },
                        validator: (value) {
                          if (changeOrderPayment) {
                            return Validator.validate(
                              value: value,
                              rules: ["required"],
                              fieldName: 'transferCompany'.tr,
                            );
                          }
                        },
                      ),
                      CustomTextFormField(
                        hintText: "رقم الحوالة",
                        onChanged: (value) {
                          var _value = state.value ?? {};
                          _value['transferNumber'] = value;
                          state.didChange(_value);
                        },
                        validator: (value) {
                          if (changeOrderPayment) {
                            return Validator.validate(
                              value: value,
                              rules: ["required"],
                              fieldName: 'transferNumber'.tr,
                            );
                          }
                        },
                      ),
                    ],
                  )
              ],
            );
          }),
    );
  }
}
