import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/order_details_controller.dart';

import 'package:matjari/src/data/models/payment_method.dart';
import 'package:matjari/src/data/vms/add_payment_info_vm.dart';
import 'package:matjari/src/view/components/bottom_sheets/payment_confirm_bottom_sheet.dart';
import 'package:matjari/src/view/components/bottom_sheets/payment_method_bottom_sheet.dart';
import 'package:matjari/src/view/components/buttons/custom_filled_button.dart';

import '../../../core/utils/matjari_icons.dart';
import '../../../core/values/app_styles.dart';
import '../../../core/values/colors.dart';
import '../../../data/models/order.dart';

class PaymentMethodFormField extends StatelessWidget {
  final List<PaymentMethod> paymentMethods;
  final EdgeInsetsGeometry? margin;
  final String? titleText;
  final String? hintText;
  final PaymentMethod? initialValue;
  final Order order;
  final controller;

  final Function(AddPaymentInfoVm) onPaymentMethodChange;

  final Widget? icon;
  const PaymentMethodFormField(
      {Key? key,
      this.margin,
      this.titleText,
      this.hintText,
      required this.paymentMethods,
      this.initialValue,
      required this.order,
      this.icon,
      required this.onPaymentMethodChange,
      this.controller})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
// print("fherwjgrtejwrgtjerghter");
// print(order.orderPaymentStatus < 3 && order.orderStatus <= 1);

    return Container(
      key: UniqueKey(),
      margin: margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (titleText != null)
            Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
              child: Text(
                titleText!,
                style: TextStyle(
                  color: Get.theme.primaryColor,
                ),
              ),
            ),
          FormField<PaymentMethod>(
            initialValue: initialValue,
            builder: (field) {
              if (field.value != null) {
                return Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              icon ?? const SizedBox.shrink(),
                              Expanded(
                                child: Text(
                                  field.value?.paymentName ?? "",
                                  softWrap: true,
                                  style: const TextStyle(
                                    height: 1.5,
                                    fontSize: 13,
                                  ),
                                ),
                              ),
                              if ((order.orderPaymentStatus < 3 &&
                                      order.orderStatus <= 1 &&
                                      order.orderPaymentType == 1) ||
                                  ((order.orderPaymentType == 2 ||
                                          order.orderPaymentType == 3) &&
                                      order.orderPaymentTransferNumber != ""))
                                CustomFilledButton(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 16),
                                  backgroundColor: AppColors.greyColor,
                                  onPressed: () {
                                    Get.bottomSheet(
                                      PaymentMethodBottomSheet(
                                        paymentMethods: paymentMethods,
                                        orderId: order.orderId,
                                        onSubmit: onPaymentMethodChange,
                                      ),
                                      isScrollControlled: true,
                                      isDismissible: false,
                                    );
                                  },
                                  child: Text(
                                    "change".tr,
                                    style: TextStyle(
                                        color: Get.theme.primaryColor),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    if ((order.orderPaymentStatus < 3 &&
                            order.orderStatus <= 1 &&
                            order.orderPaymentType != 1) ^
                        ((order.orderPaymentType == 2 ||
                                order.orderPaymentType == 3) &&
                            order.orderPaymentTransferNumber != ""))
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Row(
                          children: [
                            Expanded(
                              child: CustomFilledButton(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 15, horizontal: 16),
                                backgroundColor: AppColors.greyColor,
                                onPressed: () {
                                  Get.bottomSheet(
                                    PaymentMethodBottomSheet(
                                      paymentMethods: paymentMethods,
                                      orderId: order.orderId,
                                      onSubmit: onPaymentMethodChange,
                                    ),
                                    isScrollControlled: true,
                                    isDismissible: false,
                                  );
                                },
                                child: Text(
                                  "change".tr,
                                  style:
                                      TextStyle(color: Get.theme.primaryColor),
                                ),
                              ),
                            ),
                            const SizedBox(
                              width: 9,
                            ),
                            Expanded(
                              child: CustomFilledButton(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 15, horizontal: 16),
                                onPressed: () {
                                  if (order.orderPaymentType == 4) {
                                    Get.back();
                                    OrderDetailsController.instance
                                        .confirmPaymentBottomSheet();
                                  } else {
                                    Get.bottomSheet(
                                        PaymentConfirmBottomSheet(
                                          order: order,
                                          onConfirm: (code, transferNumber,
                                              transferCompany) {
                                            AddPaymentInfoVm addPaymentInfoVm =
                                                AddPaymentInfoVm();

                                            addPaymentInfoVm.orderId =
                                                order.orderId;
                                            addPaymentInfoVm.paymentId =
                                                order.orderPaymentId;
                                            addPaymentInfoVm.transferNumber =
                                                transferNumber;
                                            addPaymentInfoVm.transferCompany =
                                                transferCompany;

                                            controller.addPaymentInfo(
                                                addPaymentInfoVm);
                                          },
                                        ),
                                        isDismissible: false);
                                  }
                                },
                                child: Text(
                                  "confirm".tr,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                  ],
                );
              }
              return Material(
                color: AppColors.greyColor,
                borderRadius: AppStyles.cardBorderRadius,
                child: InkWell(
                  borderRadius: AppStyles.cardBorderRadius,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 16.0,
                      horizontal: 16.0,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            hintText ?? '',
                            style: TextStyle(
                              color: AppColors.hintTextColor,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        Icon(
                          MatjariIcons.left_arrow,
                          color: AppColors.hintTextColor,
                          size: 15,
                        ),
                      ],
                    ),
                  ),
                  onTap: () async {
                    Get.bottomSheet(
                      PaymentMethodBottomSheet(
                        paymentMethods: paymentMethods,
                        orderId: order.orderId,
                        onSubmit: onPaymentMethodChange,
                      ),
                      isScrollControlled: true,
                      isDismissible: false,
                    );
                  },
                ),
              );
            },
          ),
          if (order.orderPaymentType == 2 &&
              order.orderPaymentTransferNumber != "")
            Container(
              margin: icon == null
                  ? null
                  : const EdgeInsetsDirectional.only(start: 32),
              padding: order.orderPaymentStatus < 3 && order.orderStatus < 3
                  ? null
                  : const EdgeInsetsDirectional.only(top: 16),
              child: Text(
                "اسم المودع/رقم العملية: ${order.orderPaymentTransferNumber}",
                style: const TextStyle(color: Colors.grey),
              ),
            ),
          if (order.orderPaymentType == 3 &&
              order.orderPaymentTransferNumber != "")
            Container(
                margin: icon == null
                    ? null
                    : const EdgeInsetsDirectional.only(start: 32),
                padding: order.orderPaymentStatus < 3 && order.orderStatus < 3
                    ? null
                    : const EdgeInsetsDirectional.only(top: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "اسم شركة الصرافة: ${order.orderPaymentTransferCompany}",
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Text(
                      "رقم الحوالة: ${order.orderPaymentTransferNumber}",
                      style: const TextStyle(color: Colors.grey),
                    ),
                  ],
                ))
        ],
      ),
    );
  }
}
