import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class CustomTextFormField extends StatelessWidget {
  final TextStyle? style;
  final void Function(String? value)? onSave;
  final String? Function(String? value)? validator;
  final void Function(String? value)? onChanged;
  final int? maxLine;
  final Color? borderColor;
  final Widget? lable;
  final double? borderWidth;
  final Widget? prefixIcon;
  final Widget? prefix;
  final Widget? suffix;
  final EdgeInsetsGeometry? margin;
  final String? hintText;
  final String? titleText;
  final bool obscureText;
  final TextInputType keyboardType;
  final int? maxLength;
  final int? minLines;
  final String? initialValue;
  final Widget? suffixIcon;
  final TextEditingController? controller;
  final bool readOnly;
  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final TextAlign? textAlign;
  final Color? fillColor;
  final bool? isDense;
  final EdgeInsetsGeometry? contentPadding;
  final void Function(String?)? onFieldSubmitted;
  final List<TextInputFormatter> inputFormatters;
  final void Function(PointerDownEvent)? onTapOutside;

  final Widget? titleWidget;
  const CustomTextFormField({
    Key? key,
    this.onSave,
    this.validator,
    this.onChanged,
    this.maxLine,
    this.lable,
    this.prefixIcon,
    this.hintText,
    this.titleText,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.maxLength,
    this.minLines,
    this.initialValue,
    this.suffixIcon,
    this.controller,
    this.readOnly = false,
    this.margin,
    this.focusNode,
    this.onFieldSubmitted,
    this.textInputAction,
    this.inputFormatters = const [],
    this.borderWidth,
    this.borderColor,
    this.prefix,
    this.suffix,
    this.textAlign,
    this.style,
    this.fillColor,
    this.isDense,
    this.contentPadding,
    this.onTapOutside,
    this.titleWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (titleText != null || titleWidget != null)
            Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
              child: titleWidget ??
                  Text(
                    titleText!,
                    style: TextStyle(
                        color: Get.theme.primaryColor,
                        fontSize: 12,
                        fontWeight: FontWeight.bold),
                  ),
            ),
          TextFormField(
            onFieldSubmitted: onFieldSubmitted,
            onTapOutside: onTapOutside,
            textInputAction: textInputAction ?? TextInputAction.next,
            focusNode: focusNode,
            readOnly: readOnly,
            maxLines: maxLine ?? 1,
            controller: controller,
            onSaved: onSave,
            onChanged: onChanged,
            inputFormatters: inputFormatters,
            initialValue: initialValue,
            validator: validator,
            obscureText: obscureText,
            keyboardType: keyboardType,
            maxLength: maxLength,
            minLines: minLines,
            textAlign: textAlign ?? TextAlign.start,
            style: style ??
                const TextStyle(
                  fontSize: 14,
                ),
            cursorColor: Get.theme.primaryColor,
            decoration: InputDecoration(
              contentPadding: contentPadding,
              fillColor: fillColor,
              isDense: isDense,
              hintStyle: Get.theme.inputDecorationTheme.hintStyle
                  ?.copyWith(height: 1.4),
              border: Get.theme.inputDecorationTheme.border!.copyWith(
                borderSide: BorderSide(
                  color:
                      Get.theme.inputDecorationTheme.border!.borderSide.color,
                  width: borderWidth ??
                      Get.theme.inputDecorationTheme.border!.borderSide.width,
                ),
              ),
              errorBorder: Get.theme.inputDecorationTheme.errorBorder!.copyWith(
                borderSide: BorderSide(
                  color: Get
                      .theme.inputDecorationTheme.errorBorder!.borderSide.color,
                  width: borderWidth ??
                      Get.theme.inputDecorationTheme.errorBorder!.borderSide
                          .width,
                ),
              ),
              focusedBorder:
                  Get.theme.inputDecorationTheme.focusedBorder!.copyWith(
                borderSide: BorderSide(
                  color: Get.theme.inputDecorationTheme.focusedBorder!
                      .borderSide.color,
                  width: borderWidth ??
                      Get.theme.inputDecorationTheme.focusedBorder!.borderSide
                          .width,
                ),
              ),
              enabledBorder:
                  Get.theme.inputDecorationTheme.enabledBorder!.copyWith(
                borderSide: BorderSide(
                  color: borderColor ??
                      Get.theme.inputDecorationTheme.enabledBorder!.borderSide
                          .color,
                  width: borderWidth ??
                      (Get.theme.inputDecorationTheme.enabledBorder?.borderSide
                              .width ??
                          1),
                ),
              ),
              focusedErrorBorder:
                  Get.theme.inputDecorationTheme.focusedErrorBorder?.copyWith(
                borderSide: BorderSide(
                  color: Get.theme.inputDecorationTheme.focusedErrorBorder!
                      .borderSide.color,
                  width: borderWidth ??
                      Get.theme.inputDecorationTheme.focusedErrorBorder!
                          .borderSide.width,
                ),
              ),
              hintText: hintText,
              label: lable,
              prefixIconColor: Get.theme.primaryColor,
              suffixIconColor: Get.theme.primaryColor,
              prefixIcon: prefixIcon,
              suffixIcon: suffixIcon,
              prefix: prefix,
              suffix: suffix,
            ),
          ),
        ],
      ),
    );
  }
}
