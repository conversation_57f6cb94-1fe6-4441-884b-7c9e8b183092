import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/values/assets.dart';

class CheckboxField<T> extends StatelessWidget {
  const CheckboxField(
      {super.key,
      required this.text,
      this.onChanged,
      this.initialValue,
      this.onSave,
      this.helperText});
  final String text;
  final void Function(bool?)? onChanged;
  final void Function(bool?)? onSave;
  final bool? initialValue;
  final String? helperText;

  @override
  Widget build(BuildContext context) {
    // return FormField<bool>(
    //   initialValue: initialValue ?? false,
    //   onSaved: onSave,
    //   builder: (state) {
    return Column(
      children: [
        CheckboxListTile(
          title: Text(
            text,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
          ),
          controlAffinity: ListTileControlAffinity.leading,
          value: initialValue,
          checkboxShape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
          onChanged: (value) {
            // state.didChange(value ?? false);
            if (onChanged != null) {
              onChanged!(value);
            }
          },
          tristate: true,
          activeColor: Get.theme.primaryColor,
        ),
        if (helperText != null && (initialValue ?? false))
          Container(
            margin: const EdgeInsets.only(left: 26, right: 26, bottom: 10),
            child: Row(
              children: [
                SvgPicture.asset(
                  Assets.customLight,
                  colorFilter:
                      const ColorFilter.mode(Colors.black54, BlendMode.srcIn),
                ),
                const SizedBox(
                  width: 20,
                ),
                Expanded(
                    child: Text(
                  helperText ?? "",
                  softWrap: true,
                  style: const TextStyle(color: Colors.black54, height: 1.5),
                ))
              ],
            ),
          )
      ],
    );
    //   },
    // );
  }
}
