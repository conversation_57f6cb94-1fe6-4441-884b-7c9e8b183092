import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/cart_controller.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/core/utils/validator.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/core/values/colors.dart';
import 'package:matjari/src/data/services/init_service.dart';
import 'package:matjari/src/data/vms/order_vm.dart';

import '../../../data/models/payment_method.dart';
import '../images/custom_cached_network_image.dart';
import 'custom_dropdwon_form_field.dart';

class PaymentMethodDropDownFormField extends StatefulWidget {
  const PaymentMethodDropDownFormField(
      {super.key,
      this.onSave,
      this.onChanage,
      this.margin,
      required this.paymentMethods});

  final void Function(PaymentMethod?)? onSave;
  final void Function(PaymentMethod?)? onChanage;
  final EdgeInsetsGeometry? margin;
  final List<PaymentMethod> paymentMethods;

  @override
  State<PaymentMethodDropDownFormField> createState() =>
      _PaymentMethodDropDownFormFieldState();
}

class _PaymentMethodDropDownFormFieldState
    extends State<PaymentMethodDropDownFormField> {
  PaymentMethod? selectedPayment;
  @override
  Widget build(BuildContext context) {
    print("payment .. ${widget.paymentMethods.length}");
    return Column(
      children: [
        // payment method

        CustomDropdownFormField<PaymentMethod>(
          onChanged: (value) {
            setState(() {
              selectedPayment = value;
            });

            if (widget.onChanage != null) {
              widget.onChanage!(value);
            }
          },
          margin: widget.margin,
          titleText: 'paymentMethod'.tr,
          hintText: 'paymentMethodHint'.tr,
          validator: (value) {
            return Validator.validate(
              rules: ["required"],
              value: value,
              fieldName: 'paymentMethod'.tr,
            );
          },
          onSave: widget.onSave,
          selectedItemBuilder: (context) => widget.paymentMethods.map((e) {
            return SizedBox(
              width: Get.width - 100,
              child: Row(
                children: [
                  Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      // width: 40,
                      child: Center(
                          child: CustomCachedNetworkImage(
                              width: 20,
                              height: 20,
                              fit: BoxFit.scaleDown,
                              imageUrl: e.paymentIcon ?? ""))),
                  Text(
                    e.paymentName,
                    overflow: TextOverflow.visible,
                    maxLines: 1,
                    softWrap: false,
                  ),
                ],
              ),
            );
          }).toList(),
          items: widget.paymentMethods
              .map(
                (e) => DropdownMenuItem(
                  value: e,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          // width: 40,
                          // height: 40,
                          child: Center(
                              child: CustomCachedNetworkImage(
                                  width: 20,
                                  height: 20,
                                  imageUrl: e.paymentIcon ?? ""))),
                      Text(
                        e.paymentName,
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
        ),

        //payment method description

        if ((selectedPayment?.paymentType ?? 0) == 2)
          Container(
            margin: const EdgeInsetsDirectional.only(top: 12, bottom: 8),
            child: Row(
              children: [
                SvgPicture.asset(
                  Assets.lightbulb,
                  color: AppColors.primaryColor,
                ),
                const SizedBox(
                  width: 8,
                ),
                Expanded(
                  child: Text(
                    "${selectedPayment?.paymentDescription ?? ''} ${selectedPayment?.paymentAccountNumber ?? ''}",
                    style: Get.textTheme.labelMedium?.copyWith(
                        height: 1.5,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.black54),
                  ),
                ),
                const SizedBox(
                  width: 4,
                ),
                InkWell(
                  onTap: () {
                    // copy to clipboard
                    Clipboard.setData(
                      ClipboardData(
                        text: selectedPayment?.paymentAccountNumber ?? "",
                      ),
                    );
                    CommonFunctions.showSuccessMessage(
                        "paymentAccountNumberCopiedSuccessfully".tr);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 3),
                    child: Icon(
                      MatjariIcons.copy,
                      size: 20,
                      color: Get.theme.primaryColor,
                    ),
                  ),
                )
              ],
            ),
          )
        else if ((selectedPayment?.paymentType ?? 0) == 3 ||
            (selectedPayment?.paymentType ?? 0) == 4)
          Container(
            margin: const EdgeInsetsDirectional.only(top: 12, bottom: 8),
            child: Row(
              children: [
                SvgPicture.asset(
                  Assets.lightbulb,
                  height: 25,
                  color: AppColors.primaryColor,
                ),
                const SizedBox(
                  width: 8,
                ),
                Expanded(
                  child: Text(
                    selectedPayment?.paymentDescription ?? "",
                    softWrap: true,
                    style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.black54,
                        height: 1.5),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
