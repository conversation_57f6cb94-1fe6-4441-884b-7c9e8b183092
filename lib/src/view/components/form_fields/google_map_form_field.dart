import 'dart:async';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:map_picker/map_picker.dart';
import 'package:matjari/src/core/values/colors.dart';

import '../../../core/values/app_styles.dart';
import '../../../core/values/assets.dart';
import '../map/map_view_toggle.dart';

class GoogleMapFormField extends StatefulWidget {
  final CameraPosition? initialPosition;
  final CameraPosition initialCameraPosition;
  final Marker? marker;
  final Function(CameraPosition?)? onChanged;
  final String? Function(CameraPosition?)? validator;
  final Function(CameraPosition?) onSaved;
  const GoogleMapFormField(
      {Key? key,
      this.initialPosition,
      this.onChanged,
      required this.onSaved,
      required this.initialCameraPosition,
      this.marker,
      this.validator})
      : super(key: key);

  @override
  State<GoogleMapFormField> createState() => _GoogleMapFormFieldState();
}

class _GoogleMapFormFieldState extends State<GoogleMapFormField> {
  late final GoogleMapController googleMapController;
  @override
  void initState() {
    currentPosition = widget.initialPosition;
    marker = widget.marker;
    super.initState();
  }

  @override
  void dispose() {
    googleMapController.dispose();

    super.dispose();
  }

  Future<BitmapDescriptor> _getAssetIcon(BuildContext context) async {
    final Completer<BitmapDescriptor> bitmapIcon =
        Completer<BitmapDescriptor>();
    final ImageConfiguration config = createLocalImageConfiguration(context);

    const AssetImage(Assets.markerPng)
        .resolve(config)
        .addListener(ImageStreamListener((ImageInfo image, bool sync) async {
      final ByteData? bytes =
          await image.image.toByteData(format: ImageByteFormat.png);
      if (bytes == null) {
        bitmapIcon.completeError(Exception('Unable to encode icon'));
        return;
      }
      final BitmapDescriptor bitmap =
          BitmapDescriptor.fromBytes(bytes.buffer.asUint8List());
      bitmapIcon.complete(bitmap);
    }));

    return await bitmapIcon.future;
  }

  MapType mapType = MapType.normal;
  MapPickerController mapPickerController = MapPickerController();
  CameraPosition? currentPosition;
  Marker? marker;
  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (marker != null) {
        // _getAssetIcon(context).then((value) {
        //   if (mounted) {
        //     setState(() {
        //       marker = widget.marker!.copyWith(
        //         iconParam: value,
        //       );
        //     });
        //   }
        // });
      }
    });
    return FormField<CameraPosition>(
      initialValue: widget.initialPosition,
      onSaved: widget.onSaved,
      validator: widget.validator,
      builder: (FormFieldState<CameraPosition> field) {
        return Stack(
          alignment: Alignment.topCenter,
          children: [
            MapPicker(
              iconWidget: currentPosition == null
                  ? null
                  : SvgPicture.asset(
                      Assets.marker,
                      width: 40,
                    ),
              mapPickerController: mapPickerController,
              child: GoogleMap(
                initialCameraPosition:
                    widget.initialPosition ?? widget.initialCameraPosition,
                gestureRecognizers: {
                  Factory<OneSequenceGestureRecognizer>(
                    () => EagerGestureRecognizer(),
                  ),
                },
                scrollGesturesEnabled: true,
                myLocationEnabled: false,
                mapToolbarEnabled: false,
                mapType: mapType,
                myLocationButtonEnabled: false,
                zoomControlsEnabled: false,
                onCameraMove: (position) {
                  currentPosition = position;
                  mapPickerController.mapMoving!();
                },
                markers: widget.marker != null
                    ? <Marker>{
                        marker!,
                      }
                    : <Marker>{},
                onCameraIdle: () {
                  mapPickerController.mapFinishedMoving!();
                  field.didChange(currentPosition);
                  if (widget.onChanged != null) {
                    widget.onChanged!(currentPosition);
                  }
                },
                onMapCreated: (GoogleMapController controller) {
                  googleMapController = controller;
                },
              ),
            ),
            PositionedDirectional(
              start: 8,
              // right: 8,
              bottom: 38,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Material(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                        borderRadius: AppStyles.cardBorderRadius,
                        side: BorderSide(color: Colors.grey.shade200)),

                    // borderRadius: AppStyles.cardBorderRadius,
                    child: InkWell(
                      borderRadius: AppStyles.cardBorderRadius,
                      onTap: () {
                        getCurrentLocation();
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(10),
                        child: Icon(
                          Icons.my_location,
                          size: 25,
                          color: Get.theme.primaryColor,
                        ),
                      ),
                    ),
                  ),
                  if (field.hasError)
                    Container(
                      margin: const EdgeInsets.only(top: 8),
                      padding: const EdgeInsets.all(16),
                      width: Get.width - 16,
                      decoration: BoxDecoration(
                        color: AppColors.redColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        field.errorText ?? "",
                        style: const TextStyle(
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            Positioned(
              top: 16,
              child: MapViewToggle(
                onChanged: (value) {
                  field.setState(() {
                    mapType = value;
                  });
                },
              ),
            ),
          ],
        );
      },
    );
  }

  // location
  final Location location = Location();

// get current location
  Future<void> getCurrentLocation() async {
    try {
      bool serviceEnabled;
      PermissionStatus permissionGranted;
      serviceEnabled = await location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await location.requestService();
        if (!serviceEnabled) {
          return;
        }
      }

      permissionGranted = await location.hasPermission();
      if (permissionGranted == PermissionStatus.denied) {
        permissionGranted = await location.requestPermission();
        if (permissionGranted != PermissionStatus.granted) {
          return;
        }
      }
      var currentLocation = await location.getLocation();
      goToLocation(currentLocation.latitude, currentLocation.longitude);
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  void goToLocation(double? latitude, double? longitude) {
    googleMapController.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: LatLng(latitude ?? 0, longitude ?? 0),
          zoom: 16,
        ),
      ),
    );
  }
}
