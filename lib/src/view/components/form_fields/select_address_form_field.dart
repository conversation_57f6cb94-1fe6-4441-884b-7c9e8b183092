import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/address_controller.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/data/models/address.dart';
import 'package:matjari/src/data/services/auth_services.dart';

import '../../../core/routes/app_pages.dart';
import '../../../core/utils/matjari_icons.dart';
import '../../../core/values/app_styles.dart';
import '../../../core/values/colors.dart';
import '../icons/directionality_icon.dart';

class SelectAddressFormField extends StatelessWidget {
  final void Function(Address? value)? onSaved;
  final void Function(Address? value)? onChange;
  final String? Function(Address? value)? validator;
  const SelectAddressFormField({
    Key? key,
    this.onSaved,
    this.validator,
    this.onChange,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FormField<Address?>(
      onSaved: onSaved,
      validator: validator,
      builder: (FormFieldState<Address?> field) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding:
                    const EdgeInsets.only(right: 16.0, left: 16, bottom: 16),
                child: Text(
                  'deliveryAddress'.tr,
                  style: TextStyle(
                    color: Get.theme.primaryColor,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.greyColor,
                  borderRadius: AppStyles.cardBorderRadius,
                  border: field.hasError
                      ? Border.all(
                          color: Get.theme.inputDecorationTheme.errorBorder
                                  ?.borderSide.color ??
                              AppColors.redColor,
                          width: Get.theme.inputDecorationTheme.errorBorder
                                  ?.borderSide.width ??
                              0.5,
                        )
                      : null,
                ),
                child: InkWell(
                  borderRadius: AppStyles.cardBorderRadius,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 10.0,
                      horizontal: 16.0,
                    ),
                    child: Row(
                      children: [
                        Icon(
                          MatjariIcons.addresses,
                          color: AppColors.hintTextColor,
                        ),
                        const SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: field.value != null
                              ? Text(
                                  field.value?.fullAddress ?? "",
                                  style: TextStyle(
                                    color: AppColors.primaryColor,
                                    fontSize: 12,
                                    height: 1.5,
                                  ),
                                )
                              : Text(
                                  'selectDeliveryAddress'.tr,
                                  style: TextStyle(
                                    color: AppColors.hintTextColor,
                                    fontSize: 12,
                                  ),
                                ),
                        ),
                        DirectionalityIcon(
                          MatjariIcons.left_arrow,
                          color: AppColors.hintTextColor,
                          size: 15,
                        ),
                      ],
                    ),
                  ),
                  onTap: () async {
                    var result;
                    if (AuthService.instance.isLoggedIn) {
                      if (Get.isRegistered<AddressController>()) {
                        Get.find<AddressController>().dispose();

                        await Future.delayed(const Duration(milliseconds: 250));
                        result = await Get.toNamed(Routes.ADDRESSES_PAGE,
                            arguments: true);
                      } else {
                        result = await Get.toNamed(Routes.ADDRESSES_PAGE,
                            arguments: true);
                      }

                      if (result != null) {
                        field.didChange(result as Address);
                        if (onChange != null) {
                          onChange!(result);
                        }
                      }
                    } else {
                      CommonFunctions.showLoginConfirmBottomSheet();
                    }
                  },
                ),
              ),
              if (field.hasError)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0, left: 16, right: 16),
                  child: Text(
                    field.errorText ?? "",
                    style: TextStyle(
                      color: Get.theme.inputDecorationTheme.errorStyle?.color,
                      fontSize: 12,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
