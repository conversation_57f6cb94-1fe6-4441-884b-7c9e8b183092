import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/core/values/colors.dart';
import 'package:matjari/src/view/components/bottom_sheets/confirm_bottom_sheet.dart';

import '../buttons/custom_filled_button.dart';
import 'custom_text_form_field.dart';

enum CouponViewType {
  text,
  formField,
  couponCodeApplied,
}

class CouponFormField extends StatefulWidget {
  const CouponFormField({
    super.key,
    this.onCouponApply,
    this.onSave,
    this.couponController,
    this.onCouponReset,
  });

  final Future<bool?> Function(String)? onCouponApply;

  final void Function()? onCouponReset;

  final void Function(String?)? onSave;
  final TextEditingController? couponController;

  @override
  State<CouponFormField> createState() => _CouponFormFieldState();
}

class _CouponFormFieldState extends State<CouponFormField> {
  CouponViewType _viewType = CouponViewType.text;
  String _couponCode = '';
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (_viewType == CouponViewType.text)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    "couponQuestion".tr,
                    style: TextStyle(fontSize: 14, color: AppColors.thirdColor),
                  ),
                ),
                CustomFilledButton(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 25, vertical: 0),
                  onPressed: () {
                    setState(() {
                      _viewType = CouponViewType.formField;
                    });
                  },
                  child: Text(
                    'add'.tr,
                    style: const TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          )
        else if (_viewType == CouponViewType.couponCodeApplied)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
            child: Row(
              children: [
                Expanded(
                  child: CustomTextFormField(
                    controller: widget.couponController,
                    hintText: 'couponHint'.tr,
                    fillColor: Colors.grey.shade100,
                    isDense: true,
                    onSave: widget.onSave,
                    readOnly: true,
                    onChanged: (value) {
                      _couponCode = value ?? '';
                    },
                    suffixIcon: InkWell(
                      onTap: () {
                      
                              _couponCode = '';
                              widget.couponController?.clear();
                              setState(() {
                                _viewType = CouponViewType.formField;
                              });
                              widget.onCouponReset?.call();
                        
                      },
                      child: Icon(
                        MatjariIcons.canceled,
                        color: Colors.red,
                      ),
                    ),
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 9),
                  ),
                ),
                const SizedBox(
                  width: 16,
                ),
                Container(
                  width: 60,
                  height: 60,
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Get.theme.primaryColor.withOpacity(
                      0.3,
                    ),
                  ),
                  child: Center(
                    child: Container(
                      width: 35,
                      height: 35,
                      padding: EdgeInsets.all(6),
                      decoration: BoxDecoration(
                          color: Get.theme.primaryColor,
                          shape: BoxShape.circle),
                      child: SvgPicture.asset(
                        Assets.check,
                      ),
                    ),
                  ),
                )
              ],
            ),
          )
        else
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
            child: Row(
              children: [
                Expanded(
                  child: CustomTextFormField(
                    controller: widget.couponController,
                    hintText: 'couponHint'.tr,
                    fillColor: Colors.white,
                    isDense: true,
                    onSave: widget.onSave,
                    onChanged: (value) {
                      _couponCode = value ?? '';
                    },
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 9),
                  ),
                ),
                const SizedBox(
                  width: 16,
                ),
                CustomFilledButton(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 25, vertical: 0),
                  onPressed: () async {
                    if (_couponCode.isNotEmpty) {
                      var result =
                          await widget.onCouponApply?.call(_couponCode);
                      if (result == true) {
                        setState(() {
                          _viewType = CouponViewType.couponCodeApplied;
                        });
                      }
                    }
                  },
                  child: Text(
                    'apply'.tr,
                    style: const TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        const Divider(
          height: 1,
          thickness: 1,
          color: Colors.white,
        ),
      ],
    );
  }
}
