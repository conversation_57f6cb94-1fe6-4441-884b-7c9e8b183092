import 'package:badges/badges.dart';
import 'package:flutter/material.dart' hide Badge;
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/data/models/hive/cart_item.dart';

import '../../../core/routes/app_pages.dart';
import '../../../core/utils/matjari_icons.dart';
import '../../../core/values/colors.dart';
import '../../../data/providers/local/hive_provider.dart';

class CartButton extends StatelessWidget {
  const CartButton({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 70,
      width: 70,
      child: FloatingActionButton(
        heroTag: "cart",
        backgroundColor: Get.theme.primaryColor,
        elevation: 0.5,
        onPressed: () {
          Get.toNamed(Routes.CART_PAGE);
        },
        child: ValueListenableBuilder<Box<CartItem>>(
            valueListenable: HiveProvider.cartItems,
            builder: (context, box, widget) {
              var itemsCount = HiveProvider.cartItemsCount;
              return Center(
                child: itemsCount > 0
                    ? Badge(
                        // toAnimate: false,
                        position: BadgePosition.custom(top: -10, end: -4),
                        badgeStyle: BadgeStyle(
                          padding: const EdgeInsets.all(6),
                          badgeColor: AppColors.thirdColor,
                        ),

                        badgeContent: Center(
                          child: Text(
                            itemsCount.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 11,
                              height: 1,
                            ),
                          ),
                        ),
                        child: SvgPicture.asset(
                          Assets.logoIcon,
                          height: 35,
                          // width: 50,
                          colorFilter:
                              ColorFilter.mode(Colors.white, BlendMode.srcIn),
                          // size: 50,
                        ),
                      )
                    : SvgPicture.asset(
                        Assets.logoIcon,
                        height: 35,
                        // width: 50,
                        colorFilter:
                            ColorFilter.mode(Colors.white, BlendMode.srcIn),
                        // size: 50,
                      ),
              );
            }),
      ),
    );
  }
}
