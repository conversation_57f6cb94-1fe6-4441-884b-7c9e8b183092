import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/values/app_styles.dart';

class CustomFilledButton extends StatelessWidget {
  final Widget child;
  final Color? backgroundColor;
  final bool fullWidth;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final VoidCallback? onPressed;
  const CustomFilledButton({
    super.key,
    required this.child,
    this.backgroundColor,
    this.borderRadius,
    this.fullWidth = false,
    this.padding,
    this.margin,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: fullWidth ? double.maxFinite : null,
      child: MaterialButton(
        minWidth: 50,
        elevation: 0,
        height: 35,
        highlightElevation: 0,
        hoverElevation: 0,
        focusElevation: 0,
        padding: padding ?? AppStyles.buttonPadding,
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? AppStyles.buttonBorderRadius,
        ),
        color: backgroundColor ?? Get.theme.primaryColor,
        onPressed: onPressed,
        disabledColor: backgroundColor ?? Get.theme.primaryColor,
        child: DefaultTextStyle(
          style: TextStyle(
            color: Colors.white,
            fontFamily: Get.textTheme.labelLarge?.fontFamily,
            height: 1,
            fontWeight: FontWeight.w600,
          ),
          child: IconTheme(
            data: const IconThemeData(
              color: Colors.white,
              size: 20,
            ),
            child: child,
          ),
        ),
      ),
    );
  }
}
