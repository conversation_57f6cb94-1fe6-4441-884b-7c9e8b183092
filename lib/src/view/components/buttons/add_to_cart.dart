import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/values/app_styles.dart';
import 'package:matjari/src/core/values/colors.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';

import '../../../core/utils/matjari_icons.dart';
import 'custom_filled_button.dart';

class AddToCartButton extends StatelessWidget {
  final int productId;
  final VoidCallback onPressed;
  final int quantity;
  final double? fontSize;
  const AddToCartButton({
    Key? key,
    this.fontSize,
    required this.quantity,
    required this.productId,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (quantity > 0) {
      return Container(
        decoration: BoxDecoration(
          color: AppColors.greyColor,
          borderRadius: AppStyles.cardBorderRadius,
        ),
        child: Row(
          children: [
            CustomIconButton(
              icon: MatjariIcons.plus,
              onTap: () {
                HiveProvider.incrementCartItemQuantity(productId);
              },
            ),
            SizedBox(
              width: 30,
              child: Text(
                '$quantity',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Get.theme.primaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ),
            CustomIconButton(
              icon: MatjariIcons.minus,
              onTap: () {
                HiveProvider.decrementCartItemQuantity(productId);
              },
            ),
          ],
        ),
      );
    }
    return CustomFilledButton(
      margin: EdgeInsets.zero,
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 0,
      ),
      onPressed: onPressed,
      child: Row(
        children: [
          const Icon(
            MatjariIcons.plus,
          ),
          const SizedBox(width: 8),
          Text(
            'addToCart'.tr,
            style: TextStyle(
              fontSize: fontSize ?? 11,
            ),
          ),
        ],
      ),
    );
  }
}

class CustomIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  const CustomIconButton({
    Key? key,
    required this.icon,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: AppStyles.cardBorderRadius,
      child: InkWell(
        borderRadius: AppStyles.cardBorderRadius,
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
          child: Icon(
            icon,
            size: 18,
            color: Get.theme.primaryColor,
          ),
        ),
      ),
    );
  }
}
