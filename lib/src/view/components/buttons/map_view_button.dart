import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/values/app_styles.dart';

class MapViewButtom extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;
  const MapViewButtom({
    Key? key,
    required this.label,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: isSelected ? Colors.white : Colors.transparent,
      borderRadius: AppStyles.cardBorderRadius,
      child: InkWell(
        borderRadius: AppStyles.cardBorderRadius,
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 8,
            horizontal: 16,
          ),
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              height: 1,
              color: isSelected ? Get.theme.primaryColor : Colors.black,
            ),
          ),
        ),
      ),
    );
  }
}
