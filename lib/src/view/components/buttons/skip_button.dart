import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SkipButton extends StatelessWidget {
  final void Function() onPressed;
  const SkipButton({
    Key? key,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      alignment: AlignmentDirectional.centerEnd,
      child: InkWell(
        onTap: onPressed,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            'تخطي >>',
            style: TextStyle(
              color: Get.theme.primaryColor,
            ),
          ),
        ),
      ),
    );
  }
}
