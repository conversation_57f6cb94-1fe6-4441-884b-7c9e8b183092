import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/view/components/images/custom_cached_network_image.dart';

import '../../../core/values/app_styles.dart';

class ProductSlider extends StatelessWidget {
  final List<String> slides;
  ProductSlider({Key? key, required this.slides}) : super(key: key);
  final ValueNotifier<int> _currentIndex = ValueNotifier(0);
  @override
  Widget build(BuildContext context) {
    return slides.isEmpty
        ? Container(
            padding: const EdgeInsets.only(top: 16.0),
          )
        : Container(
            // padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: AppStyles.sliderBorderRadius,
                        border: Border.all(color: Colors.grey.shade200)),
                    child: ClipRRect(
                      borderRadius: AppStyles.sliderBorderRadius,
                      child: CarouselSlider.builder(
                        itemBuilder:
                            (BuildContext context, int index, int realIndex) {
                          return Stack(
                            children: [
                              SizedBox(
                                width: double.maxFinite,
                                child: CustomCachedNetworkImage(
                                  imageUrl: slides[index],
                                  fit: BoxFit.contain,
                                  // defaultImage: Assets.defaultLandscapImage,
                                ),
                              ),
                              Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  borderRadius: AppStyles.sliderBorderRadius,
                                  onTap: () {
                                    // Get.toNamed(Routes.IMAGE_VIEWER,
                                    //     arguments: slides[index]);
                                  },
                                ),
                              )
                            ],
                          );
                        },
                        itemCount: slides.length,
                        options: CarouselOptions(
                          // aspectRatio: 975 / 321,
                          viewportFraction: 1,
                          enlargeCenterPage: true,
                          autoPlay: true,
                          autoPlayInterval: const Duration(seconds: 8),
                          autoPlayAnimationDuration:
                              const Duration(milliseconds: 500),
                          autoPlayCurve: Curves.fastOutSlowIn,
                          onPageChanged: (index, reason) {
                            _currentIndex.value = index;
                          },
                          scrollDirection: Axis.vertical,
                        ),
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsetsDirectional.only(start: 8.0),
                  child: ValueListenableBuilder(
                    valueListenable: _currentIndex,
                    builder:
                        (BuildContext context, dynamic value, Widget? child) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          slides.length,
                          (index) => Container(
                            margin: const EdgeInsets.symmetric(vertical: 3),
                            width: 10,
                            height: 10,
                            decoration: BoxDecoration(
                              color: index == _currentIndex.value
                                  ? Get.theme.primaryColor
                                  : Get.theme.primaryColor.withOpacity(0.3),
                              borderRadius: AppStyles.cardBorderRadius,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
  }
}
