// import 'dart:convert';

// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_database/firebase_database.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:get/get.dart';

// import 'package:http/http.dart' as http;
// import 'package:matjari/src/core/values/app_config.dart';
// import 'package:matjari/src/core/values/assets.dart';

// import 'package:tuple/tuple.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter_polyline_points/flutter_polyline_points.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart' as latlng;

// import 'dart:math' show asin, atan, cos, sqrt;

// import 'dart:ui' as ui;
// import 'package:vector_math/vector_math.dart' hide Colors;

// class RouteViewLive extends StatefulWidget {
//   const RouteViewLive(
//       {super.key,
//       this.width,
//       this.height,
//       // required this.startCoordinate,
//       required this.orderCoordinate,
//       this.lineColor = Colors.black,
//       this.clientName,
//       this.driverName,
//       this.dbRefPath,
//       this.margin

//       // this.startAddress,
//       // this.destinationAddress,
//       });

//   final double? height;
//   final double? width;
//   // final LatLng startCoordinate;
//   final LatLng orderCoordinate;
//   final Color lineColor;

//   final String? clientName;
//   final String? driverName;
//   final String? dbRefPath;
//   final EdgeInsetsGeometry? margin;

//   // final String? destinationAddress;

//   @override
//   _RouteViewLiveState createState() => _RouteViewLiveState();
// }

// class _RouteViewLiveState extends State<RouteViewLive> {
//   late final CameraPosition _initialLocation;

//   GoogleMapController? mapController;
//   Set<Marker> markers = {};

//   final mapKey = GlobalKey();

//   // // PolylinePoints polylinePoints;
//   Map<PolylineId, Polyline> initialPolylines = {};

//   Uint8List? carMarker;
//   // Method for calculating the distance between two places
//   Future<Map<PolylineId, Polyline>?> _calculateDistance({
//     required double startLatitude,
//     required double startLongitude,
//     required double destinationLatitude,
//     required double destinationLongitude,
//   }) async {
//     try {
//       // Use the retrieved coordinates of the current position,
//       // instead of the address if the start position is user's
//       // current position, as it results in better accuracy.
//       String startCoordinatesString = '($startLatitude, $startLongitude)';
//       String destinationCoordinatesString =
//           '($destinationLatitude, $destinationLongitude)';

//       // Start Location Marker

//       // current Location Marker

//       // Adding the markers to the list

//       debugPrint(
//         'MAP::START COORDINATES: ($startLatitude, $startLongitude)',
//       );
//       debugPrint(
//         'MAP::DESTINATION COORDINATES: ($destinationLatitude, $destinationLongitude)',
//       );

//       // Calculating to check that the position relative
//       // to the frame, and pan & zoom the camera accordingly.
//       double miny = (startLatitude <= destinationLatitude)
//           ? startLatitude
//           : destinationLatitude;
//       double minx = (startLongitude <= destinationLongitude)
//           ? startLongitude
//           : destinationLongitude;
//       double maxy = (startLatitude <= destinationLatitude)
//           ? destinationLatitude
//           : startLatitude;
//       double maxx = (startLongitude <= destinationLongitude)
//           ? destinationLongitude
//           : startLongitude;

//       double southWestLatitude = miny;
//       double southWestLongitude = minx;

//       double northEastLatitude = maxy;
//       double northEastLongitude = maxx;

//       // Accommodate the two locations within the
//       // camera view of the map
//       await mapController?.animateCamera(
//         CameraUpdate.newLatLngBounds(
//           LatLngBounds(
//             northeast: latlng.LatLng(northEastLatitude, northEastLongitude),
//             southwest: latlng.LatLng(southWestLatitude, southWestLongitude),
//           ),
//           60.0,
//         ),
//       );

//       Tuple2<Map<PolylineId, Polyline>, List<LatLng>> result =
//           await _createPolylines(startLatitude, startLongitude,
//               destinationLatitude, destinationLongitude, widget.lineColor);
//       var polylines = result.item1;

//       //to remove the previous markers
//       if (markers.isNotEmpty) markers.clear();

//       carMarker ??= await getImages(Assets.motocycleMarker, 100);

//       if (carMarker != null) {
//         Marker startMarker = Marker(
//           markerId: MarkerId(startCoordinatesString),
//           position: latlng.LatLng(startLatitude, startLongitude),
//           infoWindow: InfoWindow(
//             title: 'Start $startCoordinatesString',
//             snippet: widget.driverName ?? '',
//           ),
//           icon: BitmapDescriptor.fromBytes(carMarker!),
//           rotation: getBearing(result.item2.first, result.item2[1]),
//         );

//         markers.add(startMarker);
//       }
//       // Destination Location Marker

//       Marker destinationMarker;

//       destinationMarker = Marker(
//         markerId: MarkerId(destinationCoordinatesString),
//         position: latlng.LatLng(destinationLatitude, destinationLongitude),
//         infoWindow: InfoWindow(
//           title: 'Destination $destinationCoordinatesString',
//           snippet: widget.clientName ?? '',
//         ),
//         icon: BitmapDescriptor.defaultMarker,
//       );

//       markers.add(destinationMarker);

//       // final polylines = result.item1;
//       // final polylineCoordinates = result.item2;

//       // double totalDistance = 0.0;

//       // Calculating the total distance by adding the distance
//       // between small segments
//       // for (int i = 0; i < polylineCoordinates.length - 1; i++) {
//       //   totalDistance += _coordinateDistance(
//       //     polylineCoordinates[i].latitude,
//       //     polylineCoordinates[i].longitude,
//       //     polylineCoordinates[i + 1].latitude,
//       //     polylineCoordinates[i + 1].longitude,
//       //   );
//       // }

//       // final placeDistance = totalDistance.toStringAsFixed(2);
//       // debugPrint('MAP::DISTANCE: $placeDistance km');
//       // FFAppState().update(() {
//       //   FFAppState().routeDistance = '$placeDistance km';
//       // });

//       // var url = Uri.parse(
//       //   'https://maps.googleapis.com/maps/api/distancematrix/json?destinations=$destinationLatitude,$destinationLongitude&origins=$startLatitude,$startLongitude&key=${AppConfigs.googleMapKey}',
//       // );
//       // var response = await http.get(url);

//       // if (response.statusCode == 200) {
//       //   final jsonResponse = jsonDecode(response.body) as Map<String, dynamic>;

//       //   final String durationText =
//       //       jsonResponse['rows'][0]['elements'][0]['duration']['text'];
//       //   debugPrint('MAP::$durationText');
//       //   // FFAppState().update(() {
//       //   //   FFAppState().routeDuration = '$durationText';
//       //   // });
//       // } else {
//       //   debugPrint('ERROR in distance matrix API');
//       // }

//       return polylines;
//     } catch (e) {
//       debugPrint(e.toString());
//     }
//     return null;
//   }

//   // Formula for calculating distance between two coordinates
//   // https://stackoverflow.com/a/54138876/11910277
//   double _coordinateDistance(lat1, lon1, lat2, lon2) {
//     var p = 0.017453292519943295;
//     var c = cos;
//     var a = 0.5 -
//         c((lat2 - lat1) * p) / 2 +
//         c(lat1 * p) * c(lat2 * p) * (1 - c((lon2 - lon1) * p)) / 2;
//     return 12742 * asin(sqrt(a));
//   }

//   // Create the polylines for showing the route between two places
//   Future<Tuple2<Map<PolylineId, Polyline>, List<latlng.LatLng>>>
//       _createPolylines(double startLatitude, double startLongitude,
//           double destinationLatitude, double destinationLongitude, Color color,
//           {String? polylineId}) async {
//     PolylinePoints polylinePoints = PolylinePoints();
//     PolylineResult result = await polylinePoints.getRouteBetweenCoordinates(
//       AppConfigs.googleMapKey, // Google Maps API Key
//       PointLatLng(startLatitude, startLongitude),
//       PointLatLng(destinationLatitude, destinationLongitude),
//       travelMode: TravelMode.driving,
//     );

//     debugPrint(
//         "getRouteBetweenCoordinates::result  ${result.overviewPolyline}");

//     List<latlng.LatLng> polylineCoordinates = [];

//     if (result.points.isNotEmpty) {
//       for (var point in result.points) {
//         polylineCoordinates.add(latlng.LatLng(point.latitude, point.longitude));
//       }
//     }

//     PolylineId id = PolylineId(polylineId ?? 'poly');

//     Polyline polyline = Polyline(
//       polylineId: id,
//       color: color,
//       points: polylineCoordinates,
//       width: 3,
//     );

//     return Tuple2(
//       {id: polyline},
//       polylineCoordinates,
//     );
//   }

//   // initPolylines() async {
//   //   // double startLatitude = widget.startCoordinate.latitude;
//   //   // double startLongitude = widget.startCoordinate.longitude;

//   //   // double destinationLatitude = widget.endCoordinate.latitude;
//   //   // double destinationLongitude = widget.endCoordinate.longitude;
//   //   // final initPolylines = await _calculateDistance(
//   //   //   startLatitude: startLatitude,
//   //   //   startLongitude: startLongitude,
//   //   //   destinationLatitude: destinationLatitude,
//   //   //   destinationLongitude: destinationLongitude,
//   //   // );

//   //   // if (initPolylines != null) {
//   //   //   setState(() {
//   //   //     initialPolylines = initPolylines;
//   //   //   });
//   //   // }
//   // }

//   @override
//   void initState() {
//     final initialLocation = latlng.LatLng(
//       widget.orderCoordinate.latitude,
//       widget.orderCoordinate.longitude,
//     );
//     _initialLocation = CameraPosition(
//       target: initialLocation,
//       zoom: 14,
//     );

//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return StreamBuilder<DatabaseEvent>(
//       stream: FirebaseDatabase.instance.ref(widget.dbRefPath).onValue,
//       builder: (context, snapshot) {
//         if (!snapshot.hasData) {
//           return Container(
//             height: widget.height,
//             width: widget.width,
//             margin: widget.margin,
//             child: GoogleMap(
//               key: mapKey,
//               markers: Set<Marker>.from(markers),
//               initialCameraPosition: _initialLocation,
//               myLocationEnabled: true,
//               myLocationButtonEnabled: false,
//               mapType: MapType.normal,
//               zoomGesturesEnabled: true,
//               zoomControlsEnabled: false,
//               // polylines: Set<Polyline>.of(initialPolylines.values),
//               onMapCreated: (GoogleMapController controller) {
//                 mapController = controller;
//                 setState(() {});
//                 // setState(() {
//                 //   initPolylines();
//                 // });
//               },
//             ),
//           );
//         }

//         final rideRecord =
//             (snapshot.data?.snapshot.value as Map<Object?, Object?>?)
//                 ?.cast<String?, dynamic>();

//         debugPrint('MAP::UPDATED');

//         return Container(
//           height: widget.height,
//           width: widget.width,
//           margin: widget.margin,
//           child: FutureBuilder<Map<PolylineId, Polyline>?>(
//               future: _calculateDistance(
//                   startLatitude: rideRecord!["current_location"]['latitude'],
//                   startLongitude: rideRecord["current_location"]['longitude'],
//                   destinationLatitude: widget.orderCoordinate.latitude,
//                   destinationLongitude: widget.orderCoordinate.longitude),
//               builder: (context, _snapshot) {
//                 if (!_snapshot.hasData) {
//                   return GoogleMap(
//                     key: mapKey,
//                     markers: Set<Marker>.from(markers),
//                     initialCameraPosition: CameraPosition(
//                         target: widget.orderCoordinate, zoom: 13),
//                     myLocationEnabled: true,
//                     myLocationButtonEnabled: false,
//                     mapType: MapType.normal,
//                     zoomGesturesEnabled: true,
//                     zoomControlsEnabled: false,
//                     // polylines: Set<Polyline>.of(initi
//                     // alPolylines.values),

//                     onMapCreated: (GoogleMapController controller) {
//                       mapController = controller;
//                       setState(() {});
//                     },
//                   );
//                 }

//                 return GoogleMap(
//                   key: mapKey,
//                   markers: Set<Marker>.from(markers),
//                   initialCameraPosition:
//                       CameraPosition(target: widget.orderCoordinate, zoom: 13),
//                   myLocationEnabled: true,
//                   myLocationButtonEnabled: false,
//                   mapType: MapType.normal,
//                   zoomGesturesEnabled: true,
//                   zoomControlsEnabled: false,
//                   polylines: Set<Polyline>.of(_snapshot.data!.values),
//                   rotateGesturesEnabled: false,
//                   onMapCreated: (GoogleMapController controller) {
//                     mapController ??= controller;
//                   },
//                 );
//               }),
//         );
//       },
//     );
//   }

//   // declared method to get Images
//   Future<Uint8List> getImages(String path, int width) async {
//     ByteData data = await rootBundle.load(path);
//     ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
//         targetHeight: width);
//     ui.FrameInfo fi = await codec.getNextFrame();
//     return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
//         .buffer
//         .asUint8List();
//   }

//   double getBearing(LatLng begin, LatLng end) {
//     double lat = (begin.latitude - end.latitude).abs();
//     double lng = (begin.longitude - end.longitude).abs();

//     if (begin.latitude < end.latitude && begin.longitude < end.longitude) {
//       return degrees(atan(lng / lat));
//     } else if (begin.latitude >= end.latitude &&
//         begin.longitude < end.longitude) {
//       return (90 - degrees(atan(lng / lat))) + 90;
//     } else if (begin.latitude >= end.latitude &&
//         begin.longitude >= end.longitude) {
//       return degrees(atan(lng / lat)) + 180;
//     } else if (begin.latitude < end.latitude &&
//         begin.longitude >= end.longitude) {
//       return (90 - degrees(atan(lng / lat))) + 270;
//     }
//     return -1;
//   }
// }
