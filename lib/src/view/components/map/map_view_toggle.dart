import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../core/values/app_styles.dart';
import '../buttons/map_view_button.dart';

class MapViewToggle extends StatefulWidget {
  final Function(MapType mapType) onChanged;
  const MapViewToggle({
    Key? key,
    required this.onChanged,
  }) : super(key: key);

  @override
  State<MapViewToggle> createState() => _MapViewToggleState();
}

class _MapViewToggleState extends State<MapViewToggle> {
  MapType _mapType = MapType.normal;
  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white.withOpacity(0.5),
      borderRadius: AppStyles.cardBorderRadius,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          MapViewButtom(
            label: 'map'.tr,
            isSelected: _mapType == MapType.normal,
            onTap: () {
              setState(() {
                _mapType = MapType.normal;
                widget.onChanged(_mapType);
              });
            },
          ),
          MapViewButtom(
            label: 'satellite'.tr,
            isSelected: _mapType == MapType.satellite,
            onTap: () {
              setState(() {
                _mapType = MapType.satellite;
                widget.onChanged(_mapType);
              });
            },
          ),
        ],
      ),
    );
  }
}
