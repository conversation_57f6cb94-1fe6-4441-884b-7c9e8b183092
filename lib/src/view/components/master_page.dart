import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/view/components/icons/directionality_icon.dart';

import '../../core/utils/matjari_icons.dart';
import '../../core/values/assets.dart';

class MasterPage extends StatelessWidget {
  final String? title;
  final Widget body;
  final List<Widget>? actions;
  final Widget? leading;
  final Widget? bottomNavigationBar;
  final Widget? floatingActionButton;
  final bool resizeToAvoidBottomInset;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final bool? extendBody;
  final bool hideAppBar;
  final Widget? titleWidget;
  const MasterPage(
      {super.key,
      this.title,
      required this.body,
      this.actions,
      this.leading,
      this.bottomNavigationBar,
      this.floatingActionButton,
      this.floatingActionButtonLocation,
      this.resizeToAvoidBottomInset = true,
      this.extendBody,
      this.hideAppBar = false,
      this.titleWidget});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: extendBody ?? true,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      appBar: hideAppBar
          ? null
          : AppBar(
              // centerTitle: false,
              elevation: 0.1,
              title: titleWidget ??
                  (title != null
                      ? Text(
                          title ?? "",
                        )
                      : SvgPicture.asset(
                          Assets.logoHorizontal,
                          fit: BoxFit.contain,
                          height: 30,
                        )),
              leading: leading ??
                  (ModalRoute.of(context)!.canPop
                      ? IconButton(
                          icon: SvgPicture.asset(
                            Assets.back,
                            height: 25,
                          ),
                          // const DirectionalityIcon(
                          //   MatjariIcons.back,
                          //   size: 20,
                          // ),
                          onPressed: () {
                            Get.back();
                          },
                        )
                      : null),
              actions: actions,
            ),
      body: SafeArea(child: body),
      bottomNavigationBar: bottomNavigationBar,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation ??
          FloatingActionButtonLocation.centerDocked,
    );
  }
}
