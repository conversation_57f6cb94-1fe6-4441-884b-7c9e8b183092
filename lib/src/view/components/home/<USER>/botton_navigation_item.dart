import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class BottonNavigationItem extends StatelessWidget {
  final String label;
  final IconData? icon;
  final String? iconSvg;
  final bool isSelected;
  final VoidCallback onTap;
  const BottonNavigationItem({
    super.key,
    required this.label,
    this.icon,
    this.isSelected = false,
    required this.onTap,
    this.iconSvg,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      // margin: EdgeInsets.zero,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white : Colors.transparent,
        borderRadius: BorderRadius.circular(20),
      ),
      child: InkWell(
        customBorder: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(50),
        ),
        onTap: onTap,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null)
              FittedBox(
                fit: BoxFit.contain,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Icon(
                    icon,
                    color: isSelected ? Get.theme.primaryColor : Colors.white,
                    size: 20,
                  ),
                ),
              ),
            if (iconSvg != null)
              FittedBox(
                fit: BoxFit.contain,
                child: Padding(
                  padding: EdgeInsets.all(8),
                  child: SvgPicture.asset(
                    iconSvg!,
                    colorFilter: ColorFilter.mode(
                      isSelected ? Get.theme.primaryColor : Colors.white,
                      BlendMode.srcIn,
                    ),
                    height: 20,
                    width: 20,
                  ),
                ),
              ),
            SizedBox(
              height: 40,
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(
                    opacity: animation,
                    child: SizeTransition(
                      sizeFactor: animation,
                      axis: Axis.horizontal,
                      child: child,
                    ),
                  );
                },
                child: isSelected
                    ? Center(
                        child: Padding(
                          key: ValueKey(label),
                          padding: const EdgeInsetsDirectional.only(start: 0.0),
                          child: Text(
                            label,
                            style: TextStyle(
                              fontSize: 12,
                              color: Get.theme.primaryColor,
                            ),
                          ),
                        ),
                      )
                    : const SizedBox(), // Empty when not selected
              ),
            ),
          ],
        ),
      ),
    );
  }
}
