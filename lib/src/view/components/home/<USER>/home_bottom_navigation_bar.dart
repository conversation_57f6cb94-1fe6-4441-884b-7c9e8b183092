import 'package:badges/badges.dart';
import 'package:flutter/material.dart' hide Badge;
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:matjari/src/controllers/home_controller.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/core/values/colors.dart';
import 'package:matjari/src/data/models/hive/cart_item.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';
import 'package:matjari/src/view/components/home/<USER>/botton_navigation_item.dart';

class HomeBottomNavigationBar extends StatelessWidget {
  const HomeBottomNavigationBar({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Material(
        color: Colors.transparent,
        child: Container(
          color: Colors.transparent,
          margin: const EdgeInsets.only(
              top: 0, bottom: 16, right: 16, left: 16), // Adjust as needed

          child: ClipRRect(
            borderRadius: BorderRadius.circular(30),
            child: BottomAppBar(
              elevation: 0,
              // notchMargin: 6,
              color: AppColors.primaryColor,
              shape: const CircularNotchedRectangle(),
              child: SizedBox(
                height: 56,
                child: Obx(() => Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        BottonNavigationItem(
                          label: "home".tr,
                          iconSvg: Assets.document,
                          // icon: MatjariIcons.home,
                          isSelected:
                              HomeController.instance.currentIndex.value == 0,
                          onTap: () {
                            HomeController.instance.pageViewController
                                .jumpToPage(
                              0,
                            );
                          },
                        ),

                        // const SizedBox(width: 100),
                        BottonNavigationItem(
                          label: "My account".tr,
                          // icon: MatjariIcons.more,
                          iconSvg: Assets.user,
                          isSelected:
                              HomeController.instance.currentIndex.value == 1,
                          onTap: () {
                            HomeController.instance.pageViewController
                                .jumpToPage(
                              1,
                            );
                          },
                        ),

                        BottonNavigationItem(
                          label: "Favorite".tr,
                          icon: MatjariIcons.heart,
                          isSelected:
                              HomeController.instance.currentIndex.value == 2,
                          onTap: () {
                            HomeController.instance.pageViewController
                                .jumpToPage(
                              2,
                            );
                          },
                        ),

                        Builder(builder: (context) {
                          return ValueListenableBuilder<Box<CartItem>>(
                              valueListenable: HiveProvider.cartItems,
                              builder: (context, box, widget) {
                                var itemsCount = HiveProvider.cartItemsCount;
                                return Center(
                                  child: itemsCount > 0 &&
                                          HomeController.instance.currentIndex
                                                  .value !=
                                              3
                                      ? Badge(
                                          // toAnimate: false,
                                          position: BadgePosition.custom(
                                              top: 1, end: 1),
                                          badgeStyle: BadgeStyle(
                                            padding: const EdgeInsets.all(6),
                                            badgeColor: AppColors.redColor,
                                          ),
                                          badgeContent: Center(
                                            child: Text(
                                              itemsCount.toString(),
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 11,
                                                height: 1,
                                              ),
                                            ),
                                          ),
                                          child: BottonNavigationItem(
                                            label: "cart".tr,
                                            // icon: MatjariIc,
                                            iconSvg: Assets.logoIcon,
                                            isSelected: HomeController.instance
                                                    .currentIndex.value ==
                                                3,
                                            onTap: () {
                                              HomeController
                                                  .instance.pageViewController
                                                  .jumpToPage(
                                                3,
                                              );
                                            },
                                          ),
                                        )
                                      : BottonNavigationItem(
                                          label: "cart".tr,
                                          // icon: MatjariIc,
                                          iconSvg: Assets.logoIcon,
                                          isSelected: HomeController.instance
                                                  .currentIndex.value ==
                                              3,
                                          onTap: () {
                                            HomeController
                                                .instance.pageViewController
                                                .jumpToPage(
                                              3,
                                            );
                                          },
                                        ),
                                );
                              });
                        }),
                      ],
                    )),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
