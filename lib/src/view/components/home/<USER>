import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/data/models/ads.dart';
import 'package:matjari/src/view/components/images/custom_cached_network_image.dart';

import '../../../core/values/app_styles.dart';

class HomeSlider extends StatelessWidget {
  final List<Ad> slides;
  HomeSlider({Key? key, required this.slides}) : super(key: key);
  final ValueNotifier<int> _currentIndex = ValueNotifier(0);
  @override
  Widget build(BuildContext context) {
    return slides.isEmpty
        ? Container(
            padding: const EdgeInsets.only(top: 16.0),
          )
        : Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: ClipRRect(
                    borderRadius: AppStyles.sliderBorderRadius,
                    child: CarouselSlider.builder(
                      itemBuilder:
                          (BuildContext context, int index, int realIndex) {
                        return InkWell(
                          borderRadius: AppStyles.sliderBorderRadius,
                          onTap: () {
                            if (slides[index].adUrl != "") {
                              CommonFunctions.openUrl(slides[index].adUrl);
                            } else if (slides[index].adProductId != null &&
                                slides[index].adProductId != 0) {
                              Get.toNamed(Routes.PRODUCT_PAGE, arguments: {
                                "product_id": slides[index].adProductId,
                              });
                            } else if (slides[index].adProductId != null &&
                                slides[index].adProductId != 0) {
                              Get.toNamed(Routes.PRODUCT_PAGE, arguments: {
                                "product_id": slides[index].adProductId,
                              });
                            } 
                            else if (slides[index].adPublisherId != null &&
                                slides[index].adPublisherId != 0) {
                              Get.toNamed(Routes.PUBLISHING_HOUSE_PAGE,
                                  arguments: {
                                    "publisher_id": slides[index].adPublisherId,
                                    "publisher_name":
                                        slides[index].adTitle.toString()
                                  });
                            }
                          },
                          child: Container(
                            decoration: BoxDecoration(
                                borderRadius: AppStyles.sliderBorderRadius,
                                border:
                                    Border.all(color: Colors.grey.shade200)),
                            width: double.maxFinite,
                            child: CustomCachedNetworkImage(
                              imageUrl: slides[index].adImage,
                              fit: BoxFit.cover,
                              borderRadius: AppStyles.sliderBorderRadius,
                            ),
                          ),
                        );
                      },
                      itemCount: slides.length,
                      options: CarouselOptions(
                        aspectRatio: 975 / 321,
                        viewportFraction: 1,
                        enlargeCenterPage: true,
                        autoPlay: true,
                        autoPlayInterval: const Duration(seconds: 8),
                        autoPlayAnimationDuration:
                            const Duration(milliseconds: 500),
                        autoPlayCurve: Curves.fastOutSlowIn,
                        onPageChanged: (index, reason) {
                          _currentIndex.value = index;
                        },
                        scrollDirection: Axis.vertical,
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsetsDirectional.only(start: 8.0),
                  child: ValueListenableBuilder(
                    valueListenable: _currentIndex,
                    builder:
                        (BuildContext context, dynamic value, Widget? child) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          slides.length,
                          (index) => Container(
                            margin: const EdgeInsets.symmetric(vertical: 3),
                            width: 10,
                            height: 10,
                            decoration: BoxDecoration(
                              color: index == _currentIndex.value
                                  ? Get.theme.primaryColor
                                  : Get.theme.primaryColor.withOpacity(0.3),
                              borderRadius: AppStyles.cardBorderRadius,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
  }
}
