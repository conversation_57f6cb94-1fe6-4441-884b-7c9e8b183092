import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/about_app_controller.dart';
import 'package:matjari/src/controllers/home_controller.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/view/components/form_fields/custom_text_form_field.dart';
import 'package:matjari/src/view/components/grid_views/home_categories_list.dart';
import 'package:matjari/src/view/components/icons/directionality_icon.dart';
import 'package:matjari/src/view/components/loading/loading.dart';

import '../../../core/utils/common_functions.dart';
import '../../../data/enums/page_status.dart';
import '../errors/no_internet.dart';
import '../errors/server_error.dart';
import '../home/<USER>';

class HomePageView extends StatelessWidget {
  const HomePageView({super.key});

  @override
  Widget build(BuildContext context) {
    var controller = HomeController.instance;
    return Obx(
      () {
        if (controller.pageStatus.value == LoadingStatus.loading) {
          return const Loading();
        } else if (controller.pageStatus.value == LoadingStatus.error) {
          return ServerError(
            onRetry: () {
              controller.getHomeData();
            },
          );
        } else if (controller.pageStatus.value == LoadingStatus.networkError) {
          return NoInternet(onRetry: () {
            controller.getHomeData();
          });
        }
        return RefreshIndicator(
          onRefresh: () {
            return HomeController.instance.getHomeData(withLoading: false);
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            // primary: false,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                // slider section
                HomeSlider(
                  slides: HomeController.instance.ads.value
                      .where((element) => element.adType == 1)
                      .toList(),
                ),

                // search section
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: CustomTextFormField(
                    onTapOutside: (pointerDownEvent) {
                      CommonFunctions.unfocusNodes();
                    },
                    prefixIcon: const DirectionalityIcon(MatjariIcons.search),
                    hintText: 'searchHere'.tr,
                    textInputAction: TextInputAction.search,
                    onFieldSubmitted: (value) {
                      Get.toNamed(Routes.SEARCH_PAGE, arguments: value);
                    },
                  ),
                ),
                // categories section
                HomeCategoriesGridView(
                  categories: HomeController.instance.sections.value,
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
