import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:matjari/src/controllers/address_controller.dart';
import 'package:matjari/src/controllers/home_controller.dart';
import 'package:matjari/src/controllers/order_controller.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/core/values/boxes.dart';

import 'package:matjari/src/core/values/colors.dart';
import 'package:matjari/src/core/values/hive_values.dart';
import 'package:matjari/src/data/models/currency.dart';
import 'package:matjari/src/data/services/auth_services.dart';
import 'package:matjari/src/view/components/bottom_sheets/custom_bottom_sheet.dart';
import 'package:matjari/src/view/components/buttons/custom_filled_button.dart';
import 'package:matjari/src/view/components/icons/directionality_icon.dart';

import '../../../controllers/about_app_controller.dart';
import '../../../core/routes/app_pages.dart';
import '../bottom_sheets/profile_bottom_sheet.dart';
import '../bottom_sheets/suspend_account_bottom_sheet.dart';
import '../list_titles/icon_list_tile.dart';

class ProfilePageView extends StatelessWidget {
  const ProfilePageView({super.key});

  @override
  Widget build(BuildContext context) {
    if (!Get.isRegistered<AboutAppController>()) {
      Get.put(AboutAppController());
    }

    return ListView(
      children: [
        // user card
        Obx(
          () {
            var user = AuthService.instance.user.value;

            if (user == null) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: Stack(
                    alignment: AlignmentDirectional(0, -0.5),
                    children: [
                      SvgPicture.asset(
                        Assets.accountHeader,
                        // height: 100,
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Image.asset(
                          Assets.accountLogo,
                          height: 105,
                        ),
                      )
                    ],
                  ),
                ),
              );
            }

            return Padding(
              padding: EdgeInsetsGeometry.only(top: 8),
              child: Stack(
                children: [
                  SvgPicture.asset(
                    Assets.accountHeader,
                    // height: 100,
                  ),
                  Padding(
                    padding: const EdgeInsetsDirectional.only(start: 20.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 16.0),
                          child: Image.asset(
                            Assets.accountLogo,
                            height: 100,
                          ),
                        ),
                        SizedBox(
                          width: 8,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 16.0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                user.name,
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              CustomFilledButton(
                                // borderRadius: BorderRadius.circular(8),
                                // margin: const EdgeInsets.only(top: 16),
                                onPressed: () {
                                  Get.bottomSheet(
                                    ProfileBottomSheet(),
                                    isScrollControlled: true,
                                  );
                                },
                                backgroundColor: AppColors.greyColor,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 2,
                                  horizontal: 8,
                                ),
                                child: Text(
                                  'profile'.tr,
                                  style: TextStyle(
                                      color: Get.theme.primaryColor,
                                      height: 1,
                                      fontSize: 10),
                                ),
                              ),
                              // const SizedBox(height: 16),
                              // const Divider(
                              //   height: 1,
                              // ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),

        // my orders
        IconListTile(
          title: 'myOrders'.tr,
          icon: MatjariIcons.orders,
          onTap: () async {
            if (AuthService.instance.isLoggedIn) {
              if (Get.isRegistered<OrderController>()) {
                Get.find<OrderController>().dispose();

                await Future.delayed(const Duration(milliseconds: 250));
                Get.toNamed(Routes.ORDERS_PAGE);
              } else {
                Get.toNamed(Routes.ORDERS_PAGE);
              }
            } else {
              CommonFunctions.showLoginConfirmBottomSheet();
            }
          },
        ),
        // my addresses
        IconListTile(
          title: 'myAddresses'.tr,
          icon: MatjariIcons.addresses,
          onTap: () async {
            if (AuthService.instance.isLoggedIn) {
              if (Get.isRegistered<AddressController>()) {
                Get.find<AddressController>().dispose();

                await Future.delayed(const Duration(milliseconds: 250));
                Get.toNamed(Routes.ADDRESSES_PAGE);
              } else {
                Get.toNamed(Routes.ADDRESSES_PAGE);
              }
            } else {
              CommonFunctions.showLoginConfirmBottomSheet();
            }
          },
        ),

        IconListTile(
            title: 'myFavorite'.tr,
            icon: MatjariIcons.heart,
            onTap: () {
              Get.toNamed(Routes.FAVORITE_PAGE);
            }),

        // ValueListenableBuilder<Box<dynamic>>(
        //     valueListenable: Hive.box(Boxes.appCache).listenable(),
        //     builder: (context, box, child) {
        //       Currency? value;

        //       if (box.get(HiveValues.selectedCurrencyKey) != null) {
        //         value = Currency.fromJson(
        //             json.decode(box.get(HiveValues.selectedCurrencyKey)));
        //       }
        //       return IconListTile(
        //           title: 'currency'.tr,
        //           leading: SvgPicture.asset(
        //             Assets.currency,
        //             color: Get.theme.primaryColor,
        //           ),
        //           trailing: Row(
        //             mainAxisSize: MainAxisSize.min,
        //             children: [
        //               Text(
        //                 value?.currencyName ?? '',
        //                 style: TextStyle(
        //                   color: Get.theme.colorScheme.secondary,
        //                 ),
        //               ),
        //               const SizedBox(
        //                 width: 10,
        //               ),
        //               DirectionalityIcon(
        //                 MatjariIcons.left_arrow,
        //                 color: Get.theme.colorScheme.secondary,
        //                 size: 15,
        //                 textDirection: Get.locale?.languageCode == 'ar'
        //                     ? TextDirection.rtl
        //                     : TextDirection.ltr,
        //               ),
        //             ],
        //           ),
        //           onTap: () {
        //             Get.toNamed(Routes.CURRENCY_PAGE,
        //                 arguments: HomeController.instance.selectedCurrency);
        //           });
        //     }),

        // IconListTile(
        //   title: 'language'.tr,
        //   icon: MatjariIcons.website,
        //   trailing: Row(
        //     mainAxisSize: MainAxisSize.min,
        //     children: [
        //       Text(
        //         Get.locale?.languageCode.tr ?? "",
        //         style: TextStyle(
        //           fontSize: 16,
        //           color: Get.theme.colorScheme.secondary,
        //         ),
        //       ),
        //       const SizedBox(width: 16),
        //       DirectionalityIcon(
        //         MatjariIcons.left_arrow,
        //         color: Get.theme.colorScheme.secondary,
        //         size: 15,
        //       )
        //     ],
        //   ),
        //   onTap: () {
        //     Get.bottomSheet(
        //       const LanguageBottomSheet(),
        //       isScrollControlled: true,
        //     );
        //   },
        // ),

        // suspend account
        Obx(() {
          if (AuthService.instance.user.value == null) {
            return const SizedBox.shrink();
          }
          return IconListTile(
            title: "suspend_account".tr,
            icon: Icons.person_remove_alt_1_outlined,
            onTap: () {
              Get.bottomSheet(
                  CustomBottomSheet(
                    // withExpaned: true,

                    body: SuspendAccountBotoomsheet(),
                    title: "suspend_account".tr,
                  ),

                  // ignoreSafeArea: false,
                  useRootNavigator: false);
            },
            iconMatchTextDirection: true,

            // withDivider: false,
          );
        }),

        // logout
        Obx(() {
          var user = AuthService.instance.user.value;
          return IconListTile(
            title: user == null ? "login".tr : 'logout'.tr,
            icon: MatjariIcons.logout,
            onTap: () {
              if (user == null) {
                Get.toNamed(Routes.LOGIN_PAGE);
              } else {
                CommonFunctions.showLogoutConfirmBottomSheet(() {
                  AuthService.instance.logout();
                });
              }
            },
            iconMatchTextDirection: true,
            withDivider: false,
          );
        }),
      ],
    );
  }
}
