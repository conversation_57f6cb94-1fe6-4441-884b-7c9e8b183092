import 'package:flutter/material.dart';

import '../../../data/models/hive/cart_item.dart';
import '../list_titles/cart_item_list_tile.dart';

class CartListView extends StatelessWidget {
  final List<CartItem> cartItems;
  const CartListView({
    Key? key,
    required this.cartItems,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: cartItems.length,
      itemBuilder: (BuildContext context, int index) {
        return CartItemListTile(
          item: cartItems[index],
          withDivider: index != cartItems.length - 1,
        );
      },
    );
  }
}
