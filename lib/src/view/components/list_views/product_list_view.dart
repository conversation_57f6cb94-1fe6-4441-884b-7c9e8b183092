import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/view/components/errors/no_data.dart';

import '../../../data/models/hive/product.dart';
import '../list_titles/product_list_tile.dart';

class ProductListView extends StatelessWidget {
  final List<Product> products;
  final bool searchResult;
  final bool favorites;
  final ScrollPhysics? physics;
  const ProductListView({
    Key? key,
    required this.products,
    this.searchResult = false,
    this.favorites = false,
    this.physics = const NeverScrollableScrollPhysics(),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return products.isEmpty
        ? (searchResult || favorites)
            ? NoData(
                svgIcon: favorites ? Assets.emptyFavorites : Assets.noResult,
                message:
                    favorites ? 'noFavoriteProducts'.tr : 'noSearchResults'.tr,
              )
            : NoData(
                height: Get.height * 0.6,
                message: "noProducts".tr,
              )
        : ListView.builder(
            shrinkWrap: searchResult ? false : true,
            physics: searchResult ? null : physics,
            itemCount: products.length,
            //  padding:const EdgeInsets.only(bottom: 85),
            itemBuilder: ((context, index) {
              return ProductListTile(
                  favorite: favorites,
                  product: products[index],
                  withDivider: index != products.length - 1);
            }),
          );
  }
}
