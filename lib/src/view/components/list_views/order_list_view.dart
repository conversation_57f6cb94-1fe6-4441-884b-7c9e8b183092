import 'package:flutter/material.dart';

import '../../../data/models/order.dart';
import '../list_titles/order_list_title.dart';

class OrdersListView extends StatelessWidget {
  final List<Order> orders;
  const OrdersListView({
    super.key,
    required this.orders,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: orders.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        return OrderListTile(
          order: orders[index],
          withDivider: index != orders.length - 1,
        );
      },
    );
  }
}
