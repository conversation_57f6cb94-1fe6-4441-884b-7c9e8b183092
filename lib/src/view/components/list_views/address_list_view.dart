import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/core/values/colors.dart';
import 'package:matjari/src/view/components/errors/no_data.dart';

import '../../../core/routes/app_pages.dart';
import '../../../data/models/address.dart';
import '../list_titles/address_list_title.dart';

class AdressListView extends StatelessWidget {
  final List<Address> addresses;
  const AdressListView({
    super.key,
    required this.addresses,
  });

  @override
  Widget build(BuildContext context) {
    return addresses.isEmpty
        ? NoData(
            svgIcon: Assets.noAddress,
            message: "عذراً، لايوجد عناوين مضافة!".tr,
            onAction: () {
              Get.toNamed(Routes.MANAGE_ADDRESS_PAGE);
            },
            actionPrefix: Icon(
              MatjariIcons.plus,
              color: AppColors.primaryColor,
            ),
            actionLabel: "addAddress".tr,
          )
        : ListView.builder(
            itemCount: addresses.length,
            itemBuilder: (BuildContext context, int index) {
              return AddressListTile(
                address: addresses[index],
              );
            },
          );
  }
}
