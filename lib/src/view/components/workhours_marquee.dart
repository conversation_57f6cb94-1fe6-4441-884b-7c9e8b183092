import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:marquee/marquee.dart';

class WorkhoursMarguee extends StatelessWidget {
  const WorkhoursMarguee({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.redAccent,
      width: double.maxFinite,
      alignment: Alignment.center,
      height: 30,
      child: <PERSON><PERSON><PERSON>(
        text: 'outOFWorkHours'.tr,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
        ),
        crossAxisAlignment: CrossAxisAlignment.center,
        startAfter: const Duration(
          seconds: 4,
        ),
        blankSpace: 150,
        velocity: 50.0,
        startPadding: 16.0,
        accelerationCurve: Curves.linear,
      ),
    );
  }
}
