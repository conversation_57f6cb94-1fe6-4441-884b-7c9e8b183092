import 'package:flutter/material.dart';

class dot extends StatelessWidget {
  final double size;
  final Color color;
  const dot({
    Key? key,
    required this.size,
    required this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      margin: EdgeInsets.symmetric(horizontal: size / 2.5),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(1000000),
      ),
    );
  }
}
