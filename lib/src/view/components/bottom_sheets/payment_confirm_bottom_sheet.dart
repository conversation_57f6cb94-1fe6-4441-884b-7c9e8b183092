import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/data/models/order.dart';

import '../../../core/utils/common_functions.dart';
import '../../../core/utils/validator.dart';

import '../buttons/custom_filled_button.dart';
import '../form_fields/custom_text_form_field.dart';
import 'custom_bottom_sheet.dart';

class PaymentConfirmBottomSheet extends StatelessWidget {
  final void Function(
      String code, String transferNumber, String transferCompany) onConfirm;
  final String? titleText;
  final String? hintText;
  final Order? order;
  PaymentConfirmBottomSheet({
    Key? key,
    required this.onConfirm,
    this.titleText,
    this.hintText,
    this.order,
  }) : super(key: key);

  var formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    String code = "";
    String transferNumber = "";
    String transferCompany = "";
    return CustomBottomSheet(
      title: 'paymentConfirm'.tr,
      body: SingleChildScrollView(
        padding: const EdgeInsets.only(top: 8, bottom: 16, left: 16, right: 16),
        child: Form(
          key: formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            children: [
              if (order?.orderPaymentType == 4)
                CustomTextFormField(
                  margin: const EdgeInsets.only(top: 8),
                  titleText: titleText,
                  hintText: hintText,
                  textInputAction: TextInputAction.done,
                  // keyboardType: const TextInputType.numberWithOptions(
                  //     signed: true, decimal: false),
                  onSave: (value) {
                    if (value != null) code = value;
                  },
                  validator: (value) {
                    return Validator.validate(
                      rules: [
                        'required',
                      ],
                      value: value,
                      fieldName: "verificationCode".tr,
                    );
                  },
                ),
              if (order?.orderPaymentType == 2)
                CustomTextFormField(
                  // margin: const EdgeInsets.only(top: 16),
                  hintText: "اسم المودع / رقم العملية",
                  onSave: (value) {
                    if (value != null) transferNumber = value;
                  },
                  validator: (value) {
                    return Validator.validate(
                      value: value,
                      rules: ["required"],
                      fieldName: 'transferNumber'.tr,
                    );
                  },
                ),
              if (order?.orderPaymentType == 3)
                Column(
                  children: [
                    CustomTextFormField(
                      margin: const EdgeInsets.only(bottom: 16),
                      hintText: "اسم شركة الصرافة",
                      onSave: (value) {
                        if (value != null) transferCompany = value;
                      },
                      validator: (value) {
                        return Validator.validate(
                          value: value,
                          rules: ["required"],
                          fieldName: 'transferCompany'.tr,
                        );
                      },
                    ),
                    CustomTextFormField(
                      hintText: "رقم الحوالة",
                      onSave: (value) {
                        if (value != null) transferCompany = value;
                      },
                      validator: (value) {
                        return Validator.validate(
                          value: value,
                          rules: ["required"],
                          fieldName: 'transferNumber'.tr,
                        );
                      },
                    ),
                  ],
                ),
              Padding(
                padding: const EdgeInsets.only(
                  top: 16.0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: CustomFilledButton(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text("confirm".tr),
                            const Icon(
                              Icons.chevron_right,
                            )
                          ],
                        ),
                        onPressed: () {
                          CommonFunctions.unfocusNodes();
                          if (formKey.currentState!.validate()) {
                            formKey.currentState!.save();

                            onConfirm(code, transferNumber, transferCompany);
                          }
                        },
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
