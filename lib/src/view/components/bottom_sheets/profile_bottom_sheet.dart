import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/about_app_controller.dart';
import 'package:matjari/src/data/services/auth_services.dart';
import 'package:matjari/src/data/vms/auth/profile_vm.dart';
import 'package:matjari/src/view/components/form_fields/phone_number_form_field.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/utils/validator.dart';
import '../../../core/values/colors.dart';
import '../buttons/custom_filled_button.dart';
import '../form_fields/custom_text_form_field.dart';
import 'custom_bottom_sheet.dart';

class ProfileBottomSheet extends StatelessWidget {
  ProfileBottomSheet({Key? key, this.required = false}) : super(key: key);
  final bool required;
  var acceptCondationAndTerm = Rx<bool>(true);
  @override
  Widget build(BuildContext context) {
    if (Get.isRegistered<AboutAppController>()) {
      Get.put(AboutAppController());
    }
    // form key
    final formKey = GlobalKey<FormState>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AuthService.instance.getUserProfile();
    });
    // profile vm
    final profileVM = ProfileVM();
    return Obx(() {
      final user = AuthService.instance.user.value;
      AuthService.instance.getLoginCountries();
      return CustomBottomSheet(
        title: 'profile'.tr,
        body: SingleChildScrollView(
          padding:
              const EdgeInsets.only(top: 8, bottom: 16, left: 16, right: 16),
          child: Form(
            key: formKey,
            child: Column(
              children: [
                // full name
                CustomTextFormField(
                  controller: TextEditingController.fromValue(
                    TextEditingValue(
                      text: user?.name ?? "",
                    ),
                  ),
                  titleText: 'name'.tr,
                  hintText: 'nameHint'.tr,
                  validator: (value) {
                    List<String> values =
                        value?.trim().split(RegExp(r'\s+')) ?? [];

                    if (values.length >= 3) {
                      for (var element in values) {
                        if (!RegExp(r"^[a-zA-Z\u0600-\u06FF]+$")
                            .hasMatch(element)) {
                          return "يجب ان تكون الكلمة احرف مقبولة";
                        }

                        if (element.length < 2) {
                          return "minWorldLengthValidationMessage"
                              .trParams({"min": "2"});
                        }
                      }
                    }

                    return Validator.validate(
                      rules: ["required", "minWords:3"],
                      value: value,
                    );
                  },
                  onSave: (value) {
                    profileVM.name = value!.replaceAll(RegExp(r'\s+'), " ");
                  },
                ),
                // phone number
                Obx(
                  () => PhoneNumberTextFormField(
                    readOnly: true,
                    controller: TextEditingController.fromValue(
                      TextEditingValue(
                        text: user?.mobile ?? "",
                      ),
                    ),
                    margin: const EdgeInsets.only(top: 8),
                    titleText: 'mobileNumber'.tr,
                    hintText: 'mobileNumberHint'.tr,
                    validator: (value) {
                      var phoneNumber = value?.phoneNumber;
                      return Validator.validate(
                        rules: ["required"],
                        value: phoneNumber,
                      );
                    },
                    onSave: (value) {
                      profileVM.mobile = value.phoneNumber;
                      profileVM.countryCode = value.countryCode;
                    },
                    initialCountry: 'YE',
                    filteredCountries: AuthService.instance.loginCountries.value
                        .map((e) => e.countryDialCode.toString())
                        .toList(),
                  ),
                ),
                Obx(
                  () => PhoneNumberTextFormField(
                    controller: TextEditingController.fromValue(
                      TextEditingValue(
                        text: user?.whatsapp == ""
                            ? ""
                            : user?.whatsapp.substring(4) ?? "",
                      ),
                    ),
                    margin: const EdgeInsets.only(top: 8),
                    titleText: 'whatsappNumber'.tr,
                    hintText: 'whatsappNumberHint'.tr,
                    validator: (value) {
                      return Validator.validate(
                        rules: ["required"],
                        value: value,
                      );
                    },
                    onSave: (value) {
                      profileVM.whatsapp =
                          value.countryCode + value.phoneNumber;
                    },
                    initialCountry: user?.whatsapp == ""
                        ? "YE"
                        : AuthService.instance.loginCountries.value
                                .firstWhereOrNull((element) =>
                                    element.countryCode ==
                                    user?.whatsapp.substring(0, 3))
                                ?.countryCode ??
                            "YE",
                    filteredCountries: AuthService.instance.loginCountries.value
                        .map((e) => e.countryDialCode.toString())
                        .toList(),
                  ),
                ),
                // whatsapp number
                // CustomTextFormField(
                //   controller: TextEditingController.fromValue(
                //     TextEditingValue(
                //       text: user?.whatsapp ?? "",
                //     ),
                //   ),
                //   margin: const EdgeInsets.only(top: 8),
                //   titleText: 'whatsappNumber'.tr,
                //   hintText: 'whatsappNumberHint'.tr,
                //   keyboardType: TextInputType.phone,
                //   validator: (value) {
                //     return Validator.validate(
                //       rules: ["required"],
                //       value: value,
                //     );
                //   },
                //   onSave: (value) {
                //     profileVM.whatsapp = value!;
                //   },
                // ),
                // email
                CustomTextFormField(
                  controller: TextEditingController.fromValue(
                    TextEditingValue(
                      text: user?.email ?? "",
                    ),
                  ),
                  validator: (value) {
                    return Validator.validate(
                      rules: ["email"],
                      value: value,
                    );
                  },
                  margin: const EdgeInsets.only(top: 8),
                  titleText: 'email'.tr,
                  hintText: 'emailHint'.tr,
                  keyboardType: TextInputType.emailAddress,
                  onSave: (value) {
                    profileVM.email = value!;
                  },
                ),

                Obx(() => CheckboxListTile(
                      activeColor: Get.theme.primaryColor,
                      contentPadding: EdgeInsets.zero,

                      // visualDensity: false,

                      // checkboxShape:const RoundedRectangleBorder(borderRadius: Radius.circular(10)),
                      controlAffinity: ListTileControlAffinity.leading,
                      value: acceptCondationAndTerm.value,
                      checkboxShape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4)),

                      title: Row(
                        children: [
                          const Text(
                            "أوافق على ",
                            style: TextStyle(fontSize: 14),
                          ),
                          InkWell(
                            onTap: () {
                              launchUrl(
                                Uri.parse(AboutAppController.instance
                                        .configurations.value?.privacyUrl ??
                                    ""),
                                mode: LaunchMode.externalApplication,
                              );
                            },
                            child: Text("الشروط وسياسة الخصوصية",
                                style: TextStyle(
                                    fontSize: 14,
                                    color: Get.theme.primaryColor,
                                    decoration: TextDecoration.underline)),
                          )
                        ],
                      ),
                      onChanged: (value) {
                        acceptCondationAndTerm.value = value ?? false;
                      },
                    )),

                Padding(
                  padding: const EdgeInsets.only(
                    top: 16.0,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CustomFilledButton(
                          child: Text(
                            'save'.tr,
                          ),
                          onPressed: () {
                            if (acceptCondationAndTerm.value == false) {
                              Get.showSnackbar(GetSnackBar(
                                  // snackPosition: SnackPosition .,
                                  backgroundColor:
                                      Colors.black.withOpacity(0.6),
                                  borderRadius: 16,
                                  duration: const Duration(seconds: 3),
                                  margin: const EdgeInsets.only(
                                      right: 30, left: 30, bottom: 90),
                                  messageText: const Text(
                                    "يجب الموافقة على سياسة الخصوصية اولا",
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold),
                                  )));

                              return;
                            }

                            if (formKey.currentState!.validate()) {
                              formKey.currentState!.save();
                              Get.back();
                              AuthService.instance.updateUserProfile(
                                profileVM,
                              );
                            }
                          },
                        ),
                      ),
                      const SizedBox(
                        width: 16,
                      ),
                      if (required == false)
                        Expanded(
                          child: CustomFilledButton(
                            backgroundColor: AppColors.greyColor,
                            onPressed: () {
                              Get.back();
                            },
                            child: Text(
                              'cancel'.tr,
                              style: TextStyle(color: Get.theme.primaryColor),
                            ),
                          ),
                        ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      );
    });
  }
}
