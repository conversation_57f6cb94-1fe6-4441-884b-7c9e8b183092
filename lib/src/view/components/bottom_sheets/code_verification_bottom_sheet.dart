import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/core/values/assets.dart';

import '../../../core/utils/common_functions.dart';
import '../../../core/utils/validator.dart';
import '../../../core/values/colors.dart';
import '../buttons/custom_filled_button.dart';
import '../form_fields/custom_text_form_field.dart';
import 'custom_bottom_sheet.dart';

class CodeVerificationBottomSheet extends StatefulWidget {
  final void Function(String code) onConfirm;
  const CodeVerificationBottomSheet({
    super.key,
    required this.onConfirm,
  });

  @override
  State<CodeVerificationBottomSheet> createState() =>
      _CodeVerificationBottomSheetState();
}

class _CodeVerificationBottomSheetState
    extends State<CodeVerificationBottomSheet> {
  late Timer timer;

  DateTime dateTime = DateTime.now();

  var _delaySecond = 60;
  var formKey = GlobalKey<FormState>();

  @override
  void initState() {
    timer = Timer.periodic(const Duration(seconds: 1), (callback) {
      setState(() {
        if (_delaySecond == 0) {
          callback.cancel();
        } else {
          _delaySecond--;
        }
      });
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    String code = "";
    return PopScope(
      canPop: _delaySecond == 0 ? true : false,
      child: CustomBottomSheet(
        title: 'التحقق من رقم المحمول',
        body: SingleChildScrollView(
          padding:
              const EdgeInsets.only(top: 8, bottom: 16, left: 16, right: 16),
          child: Form(
            key: formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              children: [
                // phone number
                CustomTextFormField(
                  margin: const EdgeInsets.only(top: 8),
                  titleWidget: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "verificationCode".tr,
                        style: TextStyle(
                            color: Get.theme.primaryColor,
                            fontSize: 12,
                            fontWeight: FontWeight.bold),
                      ),
                      _delaySecond != 0
                          ? RichText(
                              textDirection: TextDirection.ltr,
                              text: TextSpan(
                                text:
                                    "${(_delaySecond ~/ 60).toString().padLeft(2, '0')} : ${(_delaySecond % 60).toString().length == 1 ? (_delaySecond % 60).toString().padLeft(2, '0') : (_delaySecond % 60).toString()}",
                                style: TextStyle(
                                    color: Get.theme.primaryColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold),
                              ),
                            )
                          : InkWell(
                              onTap: () {
                                Get.back();
                              },
                              child: Row(
                                children: [
                                  SvgPicture.asset(
                                    Assets.refreshIcon,
                                    height: 15,
                                    colorFilter: ColorFilter.mode(
                                        Get.theme.primaryColor,
                                        BlendMode.srcIn),
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  Text(
                                    "resendCode".tr,
                                    style: TextStyle(
                                        color: Get.theme.primaryColor,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                            )
                    ],
                  ),
                  hintText: 'verificationCodeHint'.tr,
                  textInputAction: TextInputAction.done,
                  keyboardType: const TextInputType.numberWithOptions(
                      signed: true, decimal: false),
                  onSave: (value) {
                    if (value != null) code = value;
                  },
                  validator: (value) {
                    return Validator.validate(
                      rules: [
                        'required',
                        'length:6',
                      ],
                      value: value,
                      fieldName: "verificationCode".tr,
                    );
                  },
                ),

                Padding(
                  padding: const EdgeInsets.only(
                    top: 16.0,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CustomFilledButton(
                          child: const Text(
                            'تأكيد',
                          ),
                          onPressed: () {
                            CommonFunctions.unfocusNodes();
                            if (formKey.currentState!.validate()) {
                              formKey.currentState!.save();
                              timer.cancel();
                              Get.back();
                              widget.onConfirm(code);
                            }
                          },
                        ),
                      ),
                      if (_delaySecond == 0)
                        const SizedBox(
                          width: 16,
                        ),
                      if (_delaySecond == 0)
                        Expanded(
                          child: CustomFilledButton(
                            backgroundColor: AppColors.greyColor,
                            onPressed: () {
                              Get.back();
                            },
                            child: Text(
                              'الغاء الأمر',
                              style: TextStyle(color: Get.theme.primaryColor),
                            ),
                          ),
                        ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
