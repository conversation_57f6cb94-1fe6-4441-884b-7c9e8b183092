import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/values/app_config.dart';
import 'package:matjari/src/view/components/bottom_sheets/custom_bottom_sheet.dart';

class LanguageBottomSheet extends StatelessWidget {
  const LanguageBottomSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var supportedLanguages = AppConfigs.supportedLanguages;
    return CustomBottomSheet(
      title: "chooseLanguage".tr,
      body: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          var language = supportedLanguages[index];
          return ListTile(
            title: Text(language.tr),
            onTap: () {
              Get.back();
              Get.updateLocale(
                Locale(language),
              );
            },
            trailing: Get.locale?.languageCode == language
                ? Icon(
                    Icons.check,
                    color: Get.theme.primaryColor,
                  )
                : null,
          );
        },
        itemCount: supportedLanguages.length,
      ),
    );
  }
}
