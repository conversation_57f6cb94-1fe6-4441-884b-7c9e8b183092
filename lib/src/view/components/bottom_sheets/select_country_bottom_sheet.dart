import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/view/components/images/custom_cached_network_image.dart';

import '../../../controllers/country_code_controller.dart';
import '../../../core/utils/matjari_icons.dart';
import '../form_fields/custom_text_form_field.dart';
import 'custom_bottom_sheet.dart';

class SelectCountryBottomSheet extends GetView<CountryCodeController> {
  final String initialCountry;
  final List<String>? favoriteCountries;
  final List<String>? filteredCountries;
  final void Function(String? countryCode) onSelect;
  const SelectCountryBottomSheet({
    required this.initialCountry,
    this.favoriteCountries,
    Key? key,
    this.filteredCountries,
    required this.onSelect,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {});
    Get.put(
      CountryCodeController(
        countryCode: initialCountry,
        favoriteCountries: favoriteCountries,
        filteredCountries: filteredCountries,
      ),
    );
    return CustomBottomSheet(
      withExpaned: false,
      title: 'اختر الدولة',
      body: Container(
        constraints: BoxConstraints(maxHeight: Get.height * 0.7),
        color: Colors.white,
        child: Column(
          children: [
            // search
            CustomTextFormField(
              margin: const EdgeInsets.all(16),
              prefixIcon: const Icon(
                MatjariIcons.search,
              ),
              hintText: 'إبحث عن دولة',
              onChanged: (value) {
                if (value != null) controller.search(value);
              },
            ),
            Expanded(
              child: Obx(
                () {
                  var countries = controller.countries.toList();
                  return ListView.builder(
                    shrinkWrap: true,
                    padding:
                        const EdgeInsets.only(right: 16, left: 16, bottom: 16),
                    itemCount: countries.length,
                    itemBuilder: (context, index) {
                      return Column(
                        children: [
                          ListTile(
                            onTap: () {
                              onSelect(countries[index]['code']);
                              Get.back();
                            },
                            leading: CustomCachedNetworkImage(
                              imageUrl:
                                  "https://flagsapi.com/${countries[index]['code']!.toUpperCase()}/flat/32.png",
                              width: 30,
                            ),
                            title: Text(
                              countries[index]["code"]!.tr,
                              style: const TextStyle(),
                            ),
                            trailing: Text(
                              countries[index]["dial_code"]!,
                              style: TextStyle(
                                color: Get.theme.colorScheme.secondary,
                              ),
                            ),
                          ),
                          const Divider(
                            height: 1,
                          )
                        ],
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
