import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:matjari/src/view/components/bottom_sheets/custom_bottom_sheet.dart';
import 'package:matjari/src/view/components/form_fields/payment_form_fields.dart';
import '../../../core/values/colors.dart';
import '../../../data/models/payment_method.dart';
import '../../../data/vms/add_payment_info_vm.dart';
import '../buttons/custom_filled_button.dart';

class PaymentMethodBottomSheet extends StatefulWidget {
  final List<PaymentMethod> paymentMethods;
  final int? orderId;

  final Function(AddPaymentInfoVm) onSubmit;
  const PaymentMethodBottomSheet(
      {Key? key,
      required this.paymentMethods,
      this.orderId,
      required this.onSubmit})
      : super(key: key);

  @override
  State<PaymentMethodBottomSheet> createState() =>
      _PaymentMethodBottomSheetState();
}

class _PaymentMethodBottomSheetState extends State<PaymentMethodBottomSheet> {
  int selectedPaymentMethodId = -1;
  late AddPaymentInfoVm addPaymentInfoVm = AddPaymentInfoVm();

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    addPaymentInfoVm.orderId = widget.orderId ?? 0;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    PaymentMethod? paymentMethod = selectedPaymentMethodId == -1
        ? null
        : widget.paymentMethods.firstWhere(
            (element) => element.paymentId == selectedPaymentMethodId,
          );
    return CustomBottomSheet(
      title: 'selectPaymentMethod'.tr,
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.only(bottom: 32),
                child: PaymentFormFields(
                  paymentMethods: widget.paymentMethods,
                  changeOrderPayment: true,
                  margin: EdgeInsets.zero,
                  showLables: false,
                  onSaved: (value) {
                    addPaymentInfoVm.paymentId = value?['payment']?.paymentId;
                    addPaymentInfoVm.paymentAccountId =
                        value?['payment_account_id'];
                    addPaymentInfoVm.voucher = value?['voucher'];
                    //
                    addPaymentInfoVm.transferNumber = value?['transferNumber'];
                    addPaymentInfoVm.transferCompany =
                        value?['transferCompany'];
                  },
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: CustomFilledButton(
                      onPressed: () {
                        if (!_formKey.currentState!.validate()) {
                          return;
                        }
                        _formKey.currentState?.save();

                        widget.onSubmit(addPaymentInfoVm);
                      },
                      child: Text(
                        'done'.tr,
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    child: CustomFilledButton(
                      backgroundColor: AppColors.greyColor,
                      onPressed: () {
                        Get.back();
                      },
                      child: Text(
                        'cancel'.tr,
                        style: TextStyle(color: Get.theme.primaryColor),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
