import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/data/services/auth_services.dart';

import '../buttons/custom_filled_button.dart';
import '../form_fields/checkbox_field.dart';
import '../form_fields/custom_text_form_field.dart';

class SuspendAccountBotoomsheet extends StatelessWidget {
  SuspendAccountBotoomsheet({
    super.key,
  });

  var isSuspended = Rx<bool?>(null);
  final _key = GlobalKey<FormState>();
  String? reason;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Form(
        key: _key,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            CheckboxField<bool>(
              text: "ايقاف الحساب",
              initialValue: isSuspended.value ?? false,
              onChanged: (value) {
                isSuspended.value = true;
              },
              helperText:
                  "سيتم إيقاف حسابك ولن تستطيع تسجيل الدخول للتطبيق أو اجراء أي عمليات حتى تتواصل مع خدمة العملاء",
            ),
            CheckboxField<bool>(
              text: "حذف الحساب",
              initialValue:
                  isSuspended.value == null ? false : (!isSuspended.value!),
              helperText:
                  "سيتم حذف الحساب نهائيا ,وسيتم حذف بياناتك الشخصية والشراء وعناوينك بشكل دائم",
              onChanged: (value) {
                isSuspended.value = false;
              },
            ),
            Container(
              margin: const EdgeInsets.only(
                  left: 26, right: 26, bottom: 20, top: 10),
              child: CustomTextFormField(
                borderColor: Get.theme.primaryColor,
                minLines: 6,
                maxLine: 10,
                hintText: "السبب",
                onSave: (value) {
                  reason = value;
                },
                // validator: (value) {
                //   if (value == null || value.trim().isEmpty) {
                //     return "حقل السبب مطلوب";
                //   }
                // },
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 26, right: 26, bottom: 20.0),
              child: CustomFilledButton(
                  fullWidth: true,
                  child: const Text("إرسال"),
                  onPressed: () {
                    //
                    _key.currentState?.save();

                    if (isSuspended.value == null) {
                      Get.showSnackbar(GetSnackBar(
                          backgroundColor: Colors.black54.withOpacity(0.6),
                          borderRadius: 16,
                          duration: const Duration(seconds: 3),
                          margin: const EdgeInsets.only(
                              right: 30, left: 30, bottom: 90),
                          animationDuration: Duration.zero,
                          messageText: const Text("قم بإختيار الاجراء المناسب",
                              // textAlign: TextAlign.center,

                              style: TextStyle(
                                color: Colors.white,
                              ))));
                      return;
                    }
                    //
                    if (reason == null || reason!.trim().isEmpty) {
                      Get.showSnackbar(GetSnackBar(
                          backgroundColor: Colors.black54.withOpacity(0.6),
                          borderRadius: 16,
                          duration: const Duration(seconds: 3),
                          margin: const EdgeInsets.only(
                              right: 30, left: 30, bottom: 90),
                          animationDuration: Duration.zero,
                          messageText: const Text("قم بكتابة السبب",
                              // textAlign: TextAlign.center,

                              style: TextStyle(
                                color: Colors.white,
                              ))));
                      return;
                    }

                    AuthService.instance.accountTermination(
                        isSuspended.value == true ? 1 : 2, reason ?? "");
                  }),
            )
          ],
        ),
      );
    });
  }
}
