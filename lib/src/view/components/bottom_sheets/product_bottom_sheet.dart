import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:matjari/src/view/components/images/custom_cached_network_image.dart';

import '../../../core/utils/matjari_icons.dart';
import '../../../core/values/colors.dart';
import '../../../data/models/hive/cart_item.dart';
import '../../../data/models/hive/product.dart';
import '../../../data/providers/local/hive_provider.dart';
import '../buttons/add_to_cart.dart';
import '../buttons/custom_filled_button.dart';
import 'custom_bottom_sheet.dart';

class ProductBottomSheet extends StatelessWidget {
  final Product product;
  const ProductBottomSheet({Key? key, required this.product}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomBottomSheet(
      body: Padding(
        padding: const EdgeInsets.only(right: 16, left: 16, bottom: 8, top: 16),
        child: Column(children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: CustomCachedNetworkImage(
              imageUrl: product.image,
              width: 200,
              height: 200,
              fit: BoxFit.contain,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  product.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                '${product.price} ${"YR".tr}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Get.theme.primaryColor,
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ValueListenableBuilder<Box<Product>>(
                    valueListenable:
                        HiveProvider.getLisenableFavoriteItem(product.id),
                    builder: (context, box, widget) {
                      var inFavorite = HiveProvider.isInFavorites(product.id);
                      return CustomFilledButton(
                        backgroundColor: AppColors.redColor,
                        padding: const EdgeInsets.all(8),
                        onPressed: () {
                          inFavorite
                              ? HiveProvider.removeFromFavorites(product.id)
                              : HiveProvider.addToFavorites(product);
                        },
                        child: Icon(inFavorite
                            ? MatjariIcons.heart_fill
                            : MatjariIcons.heart),
                      );
                    }),
                ValueListenableBuilder<Box<CartItem>>(
                    valueListenable:
                        HiveProvider.getLisenableCartItem(product.id),
                    builder: (context, box, widget) {
                      Get.log(
                          'ProductListTile: ${product.id} from cart lisenable');
                      final cartItem = box.get(product.id);
                      return AddToCartButton(
                        productId: product.id,
                        quantity: cartItem?.quantity ?? 0,
                        onPressed: () {
                          HiveProvider.addCartItem(
                            CartItem(product: product, quantity: 1),
                          );
                        },
                        fontSize: 14,
                      );
                    }),
              ],
            ),
          ),
        ]),
      ),
    );
  }
}
