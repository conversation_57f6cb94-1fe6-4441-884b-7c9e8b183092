import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/manage_address_controller.dart';
import 'package:matjari/src/core/utils/validator.dart';
import 'package:matjari/src/data/vms/address_vm.dart';

import '../../../core/values/colors.dart';
import '../buttons/custom_filled_button.dart';
import '../form_fields/custom_dropdwon_form_field.dart';
import '../form_fields/custom_text_form_field.dart';
import '../others/dot.dart';

class AddressFormBottomSheet extends GetView<ManageAddressController> {
  final double lang;
  final double lat;
  const AddressFormBottomSheet({
    Key? key,
    required this.lang,
    required this.lat,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // form key
    var formKey = GlobalKey<FormState>();
    // address vm
    var addressVM = AddressVm();
    addressVM.latitude = lat;
    addressVM.longitude = lang;
    // address
    var address = controller.address;
    var editMode = address != null;
    return SafeArea(
      child: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    dot(
                      size: 10,
                      color: AppColors.greyColor,
                    ),
                    dot(
                      size: 10,
                      color: Get.theme.primaryColor,
                    ),
                  ],
                ),
                // country
                // Obx(() => CustomDropdownFormField<int?>(
                //       titleText: 'country'.tr,
                //       hintText: 'countryHint'.tr,
                //       margin: const EdgeInsets.only(top: 8),
                //       value: controller.selectedCountry.value,
                //       onSave: (value) {
                //         if (value != null) addressVM.countryId = value;
                //       },
                //       validator: (value) {
                //         return Validator.validate(
                //           value: value,
                //           rules: ["required"],
                //           fieldName: 'country'.tr,
                //         );
                //       },
                //       onChanged: (value) {
                //         controller.getCountryCities(value);
                //       },
                //       items: controller.countryList.value
                //               ?.map(
                //                 (e) => DropdownMenuItem(
                //                   value: e.countryId,
                //                   child: Text(e.countryName),
                //                 ),
                //               )
                //               .toList() ??
                //           [],
                //     )),
                // city
                Obx(
                  () {
                    List<DropdownMenuItem<int?>> items = [
                      DropdownMenuItem(
                        value: null,
                        child: Text(
                          'cityHint'.tr,
                          style: TextStyle(
                            color: AppColors.hintTextColor,
                          ),
                        ),
                      )
                    ];
                    items.addAll(controller.citiesList.value
                            ?.map(
                              (e) => DropdownMenuItem(
                                value: e.cityId,
                                child: Text(e.cityName),
                              ),
                            )
                            .toList() ??
                        []);
                    return CustomDropdownFormField<int?>(
                      titleText: "city".tr,
                      hintText: "cityHint".tr,
                      margin: const EdgeInsets.only(top: 8),
                      value: controller.selectedCity.value,
                      onSave: (value) {
                        if (value != null) addressVM.cityId = value;
                      },
                      validator: (value) {
                        return Validator.validate(
                          value: value,
                          rules: ["required"],
                        );
                      },
                      onChanged: (value) {
                        controller.getCityAreas(value);
                      },
                      items: controller.selectedCountry.value == null
                          ? [
                              DropdownMenuItem(
                                value: null,
                                child: Text(
                                  'selectCountryFirst'.tr,
                                  style: TextStyle(
                                    color: AppColors.hintTextColor,
                                  ),
                                ),
                              )
                            ]
                          : controller.citiesList.value == null
                              ? [
                                  DropdownMenuItem(
                                    value: null,
                                    child: Text(
                                      'loadingDataMessage'.tr,
                                      style: TextStyle(
                                        color: AppColors.hintTextColor,
                                      ),
                                    ),
                                  )
                                ]
                              : items.length == 1
                                  ? [
                                      DropdownMenuItem(
                                        value: null,
                                        child: Text(
                                          'noCitiesFound'.tr,
                                          style: TextStyle(
                                            color: AppColors.hintTextColor,
                                          ),
                                        ),
                                      )
                                    ]
                                  : items,
                    );
                  },
                ),
                // area
                Obx(
                  () {
                    List<DropdownMenuItem<int?>> items = [
                      DropdownMenuItem(
                        value: null,
                        child: Text(
                          'areaHint'.tr,
                          style: TextStyle(
                            color: AppColors.hintTextColor,
                          ),
                        ),
                      )
                    ];
                    items.addAll(controller.areasList.value
                            ?.map(
                              (e) => DropdownMenuItem(
                                value: e.areaId,
                                child: Text(e.areaName),
                              ),
                            )
                            .toList() ??
                        []);
                    return CustomDropdownFormField<int?>(
                      key: Key(controller.selectedCity.value.toString()),
                      titleText: "area".tr,
                      hintText: "areaHint".tr,
                      margin: const EdgeInsets.only(top: 8),
                      value: controller.selectedArea.value,
                      onSave: (value) {
                        if (value != null) addressVM.areaId = value;
                      },
                      validator: (value) {
                        return Validator.validate(
                          value: value,
                          rules: ["required"],
                          fieldName: 'area'.tr,
                        );
                      },
                      onChanged: (value) {
                        controller.selectedArea.value = value;
                      },
                      items: controller.selectedCity.value == null
                          ? [
                              DropdownMenuItem(
                                value: null,
                                child: Text(
                                  'selectCityFirst'.tr,
                                  style: TextStyle(
                                    color: AppColors.hintTextColor,
                                  ),
                                ),
                              )
                            ]
                          : controller.areasList.value == null
                              ? [
                                  DropdownMenuItem(
                                    value: null,
                                    child: Text(
                                      'loadingDataMessage'.tr,
                                      style: TextStyle(
                                        color: AppColors.hintTextColor,
                                      ),
                                    ),
                                  )
                                ]
                              : items.length == 1
                                  ? [
                                      DropdownMenuItem(
                                        value: null,
                                        child: Text(
                                          'noAreasFound'.tr,
                                          style: TextStyle(
                                            color: AppColors.hintTextColor,
                                          ),
                                        ),
                                      )
                                    ]
                                  : items,
                    );
                  },
                ),
      
                // address description
                CustomTextFormField(
                  titleText: "addressDescription".tr,
                  hintText: "addressDescriptionHint".tr,
                  margin: const EdgeInsets.only(top: 8),
                  controller: editMode
                      ? TextEditingController(text: address.description)
                      : null,
                  onSave: (value) {
                    if (value != null) addressVM.description = value;
                  },
                  validator: (value) {
                    return Validator.validate(
                      value: value,
                      rules: ["required"],
                      fieldName: 'addressDescription'.tr,
                    );
                  },
                  maxLine: 3,
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    top: 16.0,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CustomFilledButton(
                          backgroundColor: AppColors.greyColor,
                          onPressed: () {
                            Get.back();
                          },
                          child: Text(
                            'previous'.tr,
                            style: TextStyle(color: Get.theme.primaryColor),
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 16,
                      ),
                      Expanded(
                        child: CustomFilledButton(
                          child: Text(
                            'save'.tr,
                          ),
                          onPressed: () {
                            if (formKey.currentState!.validate()) {
                              formKey.currentState!.save();
                              controller.manageAddress(addressVM);
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
