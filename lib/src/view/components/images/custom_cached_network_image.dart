import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/values/assets.dart';

class CustomCachedNetworkImage extends StatelessWidget {
  final String imageUrl;
  final double? height;
  final double? width;
  final BoxFit? fit;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final Widget Function(BuildContext, String)? placeholder;
  final Color? color;
  final BlendMode? colorBlendMode;
  final String? errorIcon;

  const CustomCachedNetworkImage({
    super.key,
    required this.imageUrl,
    this.height,
    this.width,
    this.fit,
    this.margin,
    this.borderRadius,
    this.placeholder,
    this.color,
    this.colorBlendMode,
    this.errorIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.circular(0),
        child: CachedNetworkImage(
          imageUrl: imageUrl,
          height: height,
          width: width,
          fit: fit,
          color: color,
          colorBlendMode: colorBlendMode,
          placeholder: placeholder ??
              (context, url) {
                return Stack(
                  alignment: Alignment.center,
                  children: [
                    SvgPicture.asset(
                      Assets.defaultImage,
                      width: width ?? 42,
                      height: height ?? 42,
                    ),
                    SpinKitRipple(
                      color: Get.theme.primaryColor,
                      size: 50,
                    ),
                  ],
                );
              },
          errorWidget: (context, url, error) {
            return Center(
              child: SvgPicture.asset(
                errorIcon ?? Assets.defaultImage,
                width: 42,
                height: 42,
                fit: BoxFit.scaleDown,
              ),
            );
          },
        ),
      ),
    );
  }
}
