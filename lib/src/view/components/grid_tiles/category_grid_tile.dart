import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/values/assets.dart';
import 'package:matjari/src/data/models/section.dart';
import 'package:matjari/src/view/components/images/custom_cached_network_image.dart';

import '../../../controllers/category_controller.dart';
import '../../../core/routes/app_pages.dart';
import '../../../core/values/app_styles.dart';
import '../../../core/values/colors.dart';

class CategoryGridTile extends StatelessWidget {
  const CategoryGridTile({
    super.key,
    required this.category,
  });

  final Section category;

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: "category_${category.sectionId}",
      child: Stack(
        fit: StackFit.loose,
        alignment: Alignment.topCenter,
        children: [
          Container(
            margin: EdgeInsets.only(top: 25),
            padding: EdgeInsets.only(top: 4),
            width: double.maxFinite,
            child: Material(
              borderRadius: AppStyles.cardBorderRadius,
              color: AppColors.greyColor,
              child: InkWell(
                borderRadius: AppStyles.cardBorderRadius,
                onTap: () async {
                  if (Get.isRegistered<CategoryController>(
                      tag: category.sectionId.toString())) {
                    Get.find<CategoryController>(
                            tag: category.sectionId.toString())
                        .dispose();

                    await Future.delayed(const Duration(milliseconds: 250))
                        .then((value) async {
                      await Get.toNamed(
                        Routes.CATEGORY_PAGE,
                        arguments: category.sectionId,
                      );
                    });
                  } else {
                    await Get.toNamed(
                      Routes.CATEGORY_PAGE,
                      arguments: category.sectionId,
                    );
                  }
                },
                child: Column(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 8.0, top: 18),
                        child: CustomCachedNetworkImage(
                          imageUrl: category.sectionIcon,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 12.0, horizontal: 8),
                      child: Text(
                        category.sectionName,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Container(
            // padding: EdgeInsets.all(4),
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              // borderRadius: AppStyles.cardBorderRadius,
              // color: AppColors.backgroundColor,
              // color: Color(0xffF5F4F9)
            ),
            child: Container(
              // padding: EdgeInsets.all(4),
              decoration: BoxDecoration(
                shape: BoxShape.circle,

                // border: Border.all(
                //   color: AppColors.primaryColor,
                // ),
              ),
              child: SvgPicture.asset(Assets.defaultImage,
                  colorFilter:
                      ColorFilter.mode(AppColors.primaryColor, BlendMode.srcIn),
                  fit: BoxFit.scaleDown),
            ),
          )
        ],
      ),
    );
  }
}
