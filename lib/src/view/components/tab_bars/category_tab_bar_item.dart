import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/data/models/section.dart';
import 'package:matjari/src/view/components/images/custom_cached_network_image.dart';

import '../../../controllers/category_controller.dart';
import '../../../core/utils/matjari_icons.dart';
import '../../../core/values/app_styles.dart';
import '../../../core/values/colors.dart';

class CategoryTabBarItem extends StatelessWidget {
  final Section category;
  final EdgeInsetsGeometry? margin;
  final bool isSelected;
  const CategoryTabBarItem({
    Key? key,
    required this.category,
    this.margin,
    this.isSelected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final dataKey = GlobalKey();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (dataKey.currentContext != null) {
        Scrollable.ensureVisible(dataKey.currentContext!,
            alignment: 0.5,
            duration: const Duration(milliseconds: 500),
            curve: Curves.ease);
      }
    });

    return Container(
      margin: margin ?? const EdgeInsetsDirectional.only(end: 16),
      width: 70,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          InkWell(
            key: isSelected ? dataKey : null,
            onTap: () {
              CategoryController.instance.currentId.value = category.sectionId;
            },
            borderRadius: AppStyles.categoryTabBarBorderRadius,
            child: Container(
              height: 90,
              width: 90,
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade200),
                borderRadius: AppStyles.categoryTabBarBorderRadius,
                color:
                    isSelected ? Get.theme.primaryColor : AppColors.greyColor,
              ),
              child: CustomCachedNetworkImage(
                // color: isSelected ? Colors.white : null,
                imageUrl: category.sectionIcon,
                borderRadius: BorderRadius.circular(8),
                fit: BoxFit.contain,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: 12.0,
              right: 0,
              left: 0,
              bottom: 8,
            ),
            child: Text(
              category.sectionName,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: isSelected ? null : Get.theme.colorScheme.secondary,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          if (isSelected)
            SizedBox(
              width: double.maxFinite,
              child: Icon(
                MatjariIcons.indicator,
                color: Get.theme.primaryColor,
                size: 10,
              ),
            ),
        ],
      ),
    );
  }
}
