import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/category_controller.dart';
import 'package:matjari/src/data/models/section.dart';

import 'category_tab_bar_item.dart';

class CategoryTabBar extends StatelessWidget {
  final List<Section> categories;
  const CategoryTabBar({
    Key? key,
    required this.categories,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(() => SingleChildScrollView(
              padding: const EdgeInsetsDirectional.only(top: 16, start: 16),
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: categories.map(
                  (e) {
                    return CategoryTabBarItem(
                      category: e,
                      isSelected: e.sectionId ==
                          CategoryController.instance.currentId.value,
                    );
                  },
                ).toList(),
              ),
            )),
        const Divider(
          height: 1,
        )
      ],
    );
  }
}
