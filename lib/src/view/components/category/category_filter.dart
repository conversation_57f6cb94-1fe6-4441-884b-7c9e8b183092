import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../core/values/app_styles.dart';
import '../../../core/values/assets.dart';

class CategoryFilter extends StatefulWidget {
  const CategoryFilter({
    super.key,
    this.onSelected,
  });
  final void Function(String?)? onSelected;

  @override
  State<CategoryFilter> createState() => _CategoryFilterState();
}

class _CategoryFilterState extends State<CategoryFilter> {
  String? filter;

  Map<String, String> filterText = {
    "most_visited": "most_views".tr,
    "most_orderd": "most_sellings".tr,
    "asc": "lowest_price".tr,
    "desc": "heightest_price".tr
  };

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsetsDirectional.only(end: 10, top: 10, bottom: 5),
      alignment: AlignmentDirectional.centerEnd,
      child: PopupMenuButton<String?>(
        position: PopupMenuPosition.under,
        shape: RoundedRectangleBorder(
          borderRadius: AppStyles.cardBorderRadius,
        ),
        onSelected: (value) {
          if (widget.onSelected != null) {
            widget.onSelected!(value);
          }

          setState(() {
            filter = value;
          });
        },
        itemBuilder: (context) {
          return [
            PopupMenuItem(
              value: "most_visited",
              child: Text("most_views".tr),
            ),
            PopupMenuItem(
              value: "most_orderd",
              child: Text("most_sellings".tr),
            ),
            PopupMenuItem(
              value: "asc",
              child: Text("lowest_price".tr),
            ),
            PopupMenuItem(
              value: "desc",
              child: Text("heightest_price".tr),
            ),
          ];
        },
        child: Container(
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
            decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade200),
                borderRadius: BorderRadius.circular(20)),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  margin: const EdgeInsetsDirectional.only(end: 10),
                  child: SvgPicture.asset(
                    Assets.filter,
                    height: 20,
                    colorFilter: ColorFilter.mode(
                        Get.theme.primaryColor, BlendMode.srcIn),
                  ),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(end: 20),
                  child: Text(
                    filter != null ? filterText[filter!] ?? "" : "sort".tr,
                    style: const TextStyle(color: Colors.grey),
                  ),
                ),
                const Icon(
                  Icons.expand_more,
                  color: Colors.grey,
                ),
              ],
            )),
      ),
    );
  }
}
