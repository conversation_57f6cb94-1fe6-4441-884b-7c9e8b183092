import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/values/app_constants.dart';
import 'package:matjari/src/data/models/order.dart';

import 'another_stepper_package.dart';

/// A beautiful widget that displays the order status as a vertical stepper
/// with animated icons, smooth transitions, and modern UI design.
class OrderStatusStepper extends StatelessWidget {
  const OrderStatusStepper({
    super.key,
    required this.currentIndex,
    required this.orderStatusUpdateDate,
    required this.order,
  });

  final int currentIndex;
  final String orderStatusUpdateDate;
  final Order order;

  // UI Constants for large icons with compact height
  static const double _stepperIconSize = 180.0;
  static const double _stepperBarThickness = 3.0;
  static const double _stepperIconHeight = 42.0;
  static const double _stepperVerticalGap = 15.0;

  @override
  Widget build(BuildContext context) {
    return AnotherStepper(
      stepperList: _buildStepperData(),
      stepperDirection: Axis.vertical,
      barThickness: _stepperBarThickness,
      iconWidth: _stepperIconHeight,
      iconHeight: _stepperIconHeight,
      verticalGap: _stepperVerticalGap,
      activeBarColor: Get.theme.primaryColor,
      inActiveBarColor: Colors.grey.shade300,
      
    );
  }

  /// Builds the stepper data list from order status values
  List<StepperData> _buildStepperData() {
    return AppConstants.orderStatusValuesVm.entries
        .map((entry) => _createStepperItem(entry.key, entry.value))
        .toList();
  }

  /// Creates a single stepper item
  StepperData _createStepperItem(int statusKey, String statusValue) {
    return StepperData(
      title: _buildStepperTitle(statusValue),
      iconWidget: _buildStepperIcon(statusKey),
      subtitleWidget: _buildStepperSubtitle(statusKey),
    );
  }

  /// Builds the title widget for each step with beautiful styling
  StepperText _buildStepperTitle(String statusValue) {
    final isDone = AppConstants.orderStatusValuesVm.keys
        .firstWhere((key) => AppConstants.orderStatusValuesVm[key] == statusValue) <= currentIndex;


 final isCurrentStep = AppConstants.orderStatusValuesVm.keys
        .firstWhere((key) => AppConstants.orderStatusValuesVm[key] == statusValue) == currentIndex;

    return StepperText(
      statusValue,
      textStyle: TextStyle(
        color: isDone ? Get.theme.primaryColor : Colors.grey.shade700,
        fontWeight: isCurrentStep ? FontWeight.w600 : FontWeight.w500,
        fontSize: isCurrentStep ? 16.0 : 14.0,
      ),
    );
  }

  /// Builds the icon widget for each step with enhanced styling
  Widget _buildStepperIcon(int statusKey) {
    final isCurrentStep = statusKey == currentIndex;
    final isCompleted = statusKey < currentIndex;

    if (isCurrentStep) {
      return _buildAnimatedIcon(statusKey);
    }

    return _buildStaticIcon(statusKey, isCompleted);
  }

  /// Builds animated Lottie icon for current step with container
  Widget _buildAnimatedIcon(int statusKey) {
    final lottieAsset = AppConstants.orderStatusLottieValues[statusKey];
    if (lottieAsset == null) return const SizedBox.shrink();

    return Container(
      width: _stepperIconSize,
      height: _stepperIconSize,
      decoration: BoxDecoration(
        color: Get.theme.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(_stepperIconSize / 2),
        border: Border.all(
          color: Get.theme.primaryColor,
          width: 3.0,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(3.0),
        child: Center(
          child: Lottie.asset(
            lottieAsset,
            width: _stepperIconSize * 0.9,
            height: _stepperIconSize * 0.9,
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }

  /// Builds static SVG icon for non-current steps with status styling
  Widget _buildStaticIcon(int statusKey, bool isCompleted) {
    final svgAsset = AppConstants.orderStatusSvgValues[statusKey];
    if (svgAsset == null) return const SizedBox.shrink();

    return Container(
      width: _stepperIconSize,
      height: _stepperIconSize,
      decoration: BoxDecoration(
        color: isCompleted
            ? Get.theme.primaryColor.withValues(alpha: 0.1)
            : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(_stepperIconSize / 2),
        border: Border.all(
          color: isCompleted ? Get.theme.primaryColor : Colors.grey.shade300,
          width: 3,
        ),
      ),
      child: Padding(

        padding: const EdgeInsets.all(6.0),
        child: Center(
          child: isCompleted
              ? Icon(
                  Icons.check,
                  color: Get.theme.primaryColor,
                  size: 25
                )
              : SvgPicture.asset(
                  svgAsset,
                  width: _stepperIconSize * 0.8,
                  height: _stepperIconSize * 0.8,
                  fit: BoxFit.contain,
                  colorFilter: ColorFilter.mode(
                    Colors.grey.shade400,
                    BlendMode.srcIn,
                  ),
                ),
        ),
      ),
    );
  }

  /// Builds the subtitle widget for current step only
  Widget? _buildStepperSubtitle(int statusKey) {
    if (currentIndex != statusKey) return null;

    return _OrderStepSubtitle(
      orderStatusUpdateDate: orderStatusUpdateDate,
      order: order,
      statusKey: statusKey,
    );
  }
}

/// A beautiful widget for handling the subtitle content of each step
class _OrderStepSubtitle extends StatelessWidget {
  const _OrderStepSubtitle({
    required this.orderStatusUpdateDate,
    required this.order,
    required this.statusKey,
  });

  final String orderStatusUpdateDate;
  final Order order;
  final int statusKey;

  static const int _shippedStatusIndex = 4;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<DatabaseEvent>(
      stream: _getDriverLocationStream(),
      builder: (context, snapshot) {
        final hasDriverLocation = _hasDriverLocation(snapshot);

        return Container(
          margin: _getSubtitleMargin(hasDriverLocation),
          padding: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildDateText(),
              if (_shouldShowTrackingButton(hasDriverLocation))
                _buildTrackingButton(),
            ],
          ),
        );
      },
    );
  }

  /// Gets the Firebase stream for driver location
  Stream<DatabaseEvent> _getDriverLocationStream() {
    return FirebaseDatabase.instance
        .ref('drivers/${order.orderDriverId}/${order.orderId}')
        .onValue;
  }

  /// Checks if driver location data exists
  bool _hasDriverLocation(AsyncSnapshot<DatabaseEvent> snapshot) {
    if (!snapshot.hasData) return false;

    final data = (snapshot.data?.snapshot.value as Map<Object?, Object?>?)
        ?.cast<String, dynamic>();

    return data?.containsKey('current_location') ?? false;
  }

  /// Gets appropriate margin based on tracking button visibility
  EdgeInsetsDirectional? _getSubtitleMargin(bool hasDriverLocation) {
    return const EdgeInsetsDirectional.only(top: 2);
  }

  /// Builds the date text widget with enhanced styling
  Widget _buildDateText() {
    return Row(
      children: [
        // Icon(
        //   Icons.access_time_rounded,
        //   size: 16,
        //   color: Get.theme.primaryColor,
        // ),
        // const SizedBox(width: 6),
        Text(
          orderStatusUpdateDate,
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Determines if tracking button should be shown
  bool _shouldShowTrackingButton(bool hasDriverLocation) {
    return statusKey == _shippedStatusIndex && hasDriverLocation;
  }

  /// Builds the compact order tracking button
  Widget _buildTrackingButton() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Get.theme.primaryColor,
            Get.theme.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Get.theme.primaryColor.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: _navigateToTracking,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.location_on_rounded,
                  color: Colors.white,
                  size: 14,
                ),
                const SizedBox(width: 4),
                Text(
                  "orderTracking".tr,
                  style: const TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Navigates to the order tracking page
  void _navigateToTracking() {
    Get.toNamed(Routes.Map_route_page, arguments: order);
  }
}
