import 'dart:math';

import 'package:flutter/material.dart';
import 'package:matjari/src/core/utils/common_functions.dart';

class DirectionalityIcon extends StatelessWidget {
  final IconData icon;
  final Color? color;
  final double? size;
  final TextDirection? textDirection;
  const DirectionalityIcon(this.icon,
      {Key? key, this.color, this.size, this.textDirection})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Transform(
      alignment: Alignment.center,
      transform: CommonFunctions.isDirectionRTL()
          ? Matrix4.rotationY(0)
          : Matrix4.rotationY(pi),
      child: Icon(
        icon,
        color: color,
        size: size,
        textDirection: textDirection,
      ),
    );
  }
}
