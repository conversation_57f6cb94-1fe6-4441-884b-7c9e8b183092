import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../core/values/assets.dart';
import '../buttons/custom_filled_button.dart';

class ServerError extends StatelessWidget {
  final String? message;
  final String? svgIcon;
  final String? actionLabel;
  final IconData? actionIcon;
  final VoidCallback? onRetry;
  const ServerError(
      {Key? key,
      this.onRetry,
      this.actionLabel,
      this.actionIcon,
      this.message,
      this.svgIcon})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(svgIcon ?? Assets.loadingError, width: 150),
          const SizedBox(height: 20),
          Text(message ?? 'unexpectedError'.tr),
          if (onRetry != null) ...[
            const SizedBox(height: 20),
            CustomFilledButton(
              onPressed: onRetry,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    Assets.refreshIcon,
                    height: 17,
                    colorFilter:
                        ColorFilter.mode(Colors.white, BlendMode.srcIn),
                  ),
                  const SizedBox(width: 6),
                  Text('retry'.tr),
                ],
              ),
            )
          ]
        ],
      ),
    );
  }
}
