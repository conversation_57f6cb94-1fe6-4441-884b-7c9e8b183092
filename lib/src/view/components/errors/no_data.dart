import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/values/colors.dart';

import '../../../core/values/assets.dart';
import '../buttons/custom_filled_button.dart';

class NoData extends StatelessWidget {
  final String? message;
  final String? svgIcon;
  final VoidCallback? onAction;
  final String? actionLabel;
  final IconData? actionIcon;
  final double? height;
  final double? width;
  final Widget? actionPrefix;
  const NoData(
      {super.key,
      this.onAction,
      this.actionLabel,
      this.actionIcon,
      this.message,
      this.svgIcon,
      this.height,
      this.width,
      this.actionPrefix});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(svgIcon ?? Assets.noContent, width: 140),
            const SizedBox(height: 20),
            Text(message ?? 'noData'.tr),
            if (onAction != null) ...[
              const SizedBox(height: 20),
              CustomFilledButton(
                onPressed: onAction,
                backgroundColor: AppColors.greyColor,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (actionPrefix != null) ...[
                      actionPrefix!,
                      const SizedBox(width: 5)
                    ],
                    Text(
                      actionLabel ?? 'addNew'.tr,
                      style: TextStyle(
                        color: Get.theme.primaryColor,
                      ),
                    ),
                    if (actionIcon != null) ...[
                      const SizedBox(width: 8),
                      Icon(actionIcon),
                    ],
                  ],
                ),
              )
            ]
          ],
        ),
      ),
    );
  }
}
