import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../core/values/assets.dart';
import '../buttons/custom_filled_button.dart';

class NoInternet extends StatelessWidget {
  final VoidCallback onRetry;
  const NoInternet({Key? key, required this.onRetry}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(Assets.noConnection, width: 150),
          const SizedBox(height: 20),
          Text('noInternetConnection'.tr),
          const SizedBox(height: 20),
          CustomFilledButton(
            onPressed: onRetry,
            child: Text('retry'.tr),
          )
        ],
      ),
    );
  }
}
