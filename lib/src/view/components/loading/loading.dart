import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

class Loading extends StatelessWidget {
  final double? height;
  final double? width;
  const Loading({Key? key, this.height, this.width}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width,
      child: Center(
        // TODO: Replace with a better loading indicator.
        child: SpinKitThreeBounce(
          size: 30,
          color: Get.theme.primaryColor.withValues(alpha: 0.5),
        ),
      ),
    );
  }
}
