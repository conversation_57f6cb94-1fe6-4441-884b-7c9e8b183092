import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/view/components/icons/directionality_icon.dart';

class IconListTile extends StatelessWidget {
  final IconData? icon;
  final String title;
  final VoidCallback? onTap;
  final bool iconMatchTextDirection;
  final bool withDivider;
  final Widget? trailing;
  final Widget? leading;
  const IconListTile({
    super.key,
    this.icon,
    required this.title,
    this.onTap,
    this.iconMatchTextDirection = false,
    this.withDivider = true,
    this.trailing,
    this.leading,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
          dense: true,
          minVerticalPadding: 0,
          leading: leading ??
              Icon(
                icon,
                size: 25,
                color: Get.theme.primaryColor,
              ),
          title: Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
          ),
          onTap: onTap,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 4, horizontal: 20),
          trailing: trailing ??
              DirectionalityIcon(
                MatjariIcons.left_arrow,
                color: Get.theme.colorScheme.secondary,
                size: 15,
                textDirection: Get.locale?.languageCode == 'ar'
                    ? TextDirection.rtl
                    : TextDirection.ltr,
              ),
        ),
        if (withDivider)
          const Divider(
            height: 1,
          ),
      ],
    );
  }
}
