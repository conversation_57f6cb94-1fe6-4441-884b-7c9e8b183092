import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import '../../../controllers/product_details_controller.dart';
import '../../../core/routes/app_pages.dart';
import '../../../core/utils/matjari_icons.dart';
import '../../../core/values/app_styles.dart';
import '../../../core/values/colors.dart';
import '../../../data/models/hive/cart_item.dart';
import '../../../data/models/hive/product.dart';
import '../../../data/providers/local/hive_provider.dart';
import '../buttons/add_to_cart.dart';
import '../buttons/custom_filled_button.dart';
import '../images/custom_cached_network_image.dart';

class ProductListTile extends StatelessWidget {
  final Product product;
  final bool withDivider;
  final bool favorite;

  const ProductListTile({
    super.key,
    required this.product,
    this.withDivider = true,
    this.favorite = false,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Box<CartItem>>(
        valueListenable: HiveProvider.getLisenableCartItem(product.id),
        builder: (context, box, widget) {
          final cartItem = box.get(product.id);
          return ValueListenableBuilder<Box<Product>>(
              valueListenable:
                  HiveProvider.getLisenableFavoriteItem(product.id),
              builder: (context, box, widget) {
                var inFavorite = HiveProvider.isInFavorites(product.id);
                return InkWell(
                  onTap: () async {
                    // Get.bottomSheet(
                    //   ProductBottomSheet(product: product),
                    // );

                    if (Get.isRegistered<ProductDetailsController>(
                        tag: product.id.toString())) {
                      Get.find<ProductDetailsController>(
                              tag: product.id.toString())
                          .dispose();

                      await Future.delayed(const Duration(milliseconds: 250));
                      Get.toNamed(Routes.PRODUCT_PAGE, arguments: product);
                    } else {
                      Get.toNamed(Routes.PRODUCT_PAGE, arguments: product);
                    }
                  },
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.only(
                            top: 8, bottom: 8, left: 16, right: 16),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Container(
                            //   width: 71.60,
                            //   height: 110,
                            //   decoration: BoxDecoration(
                            //       borderRadius: AppStyles.cardBorderRadius,
                            //       color: AppColors.greyColor,
                            //       border:
                            //           Border.all(color: Colors.grey.shade300)),
                            //   // padding: const EdgeInsets.all(10),
                            //   child: CustomCachedNetworkImage(
                            //     imageUrl: product.image,
                            //     borderRadius: AppStyles.cardBorderRadius,
                            //     fit: BoxFit.cover,
                            //   ),
                            // ),

                            Container(
                              width: 100,
                              height: 100,
                              // height: double.m,
                              decoration: BoxDecoration(
                                borderRadius: AppStyles.cardBorderRadius,
                                color: AppColors.greyColor,
                              ),
                              padding: const EdgeInsets.all(10),
                              child: CustomCachedNetworkImage(
                                imageUrl: product.image,
                                fit: BoxFit.contain,
                                height: 100,
                              ),
                            ),

                            const SizedBox(width: 16),
                            Expanded(
                              child: SizedBox(
                                height: 100,
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.only(
                                      end: 8.0),
                                  child: IntrinsicHeight(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Row(
                                          children: [
                                            Expanded(
                                              flex: 3,
                                              child: Text(
                                                product.name,
                                                style: const TextStyle(
                                                  height: 1.5,
                                                  fontSize: 13,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ),
                                            Expanded(
                                                flex: 1,
                                                child: Container(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 1),
                                                    alignment:
                                                        AlignmentDirectional
                                                            .centerEnd,
                                                    child: InkWell(
                                                      onTap: () {
                                                        inFavorite
                                                            ? HiveProvider
                                                                .removeFromFavorites(
                                                                    product.id)
                                                            : HiveProvider
                                                                .addToFavorites(
                                                                    product);
                                                      },
                                                      child: Icon(
                                                        inFavorite
                                                            ? MatjariIcons
                                                                .heart_fill
                                                            : MatjariIcons
                                                                .heart,
                                                        size: 25,
                                                        color:
                                                            Color(0xffba7441),
                                                      ),
                                                    ))),
                                          ],
                                        ),
                                        Spacer(),
                                        Row(
                                          children: [
                                            AddToCartButton(
                                              productId: product.id,
                                              quantity: cartItem?.quantity ?? 0,
                                              onPressed: () {
                                                HiveProvider.addCartItem(
                                                  CartItem(
                                                    product: product,
                                                    quantity: 1,
                                                  ),
                                                );
                                              },
                                            ),
                                            Expanded(
                                              child: Text(
                                                "${product.itemPriceCurrencyVar}",
                                                textAlign: TextAlign.end,
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: Get.theme.primaryColor,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (withDivider)
                        const Divider(
                          height: 1,
                        )
                    ],
                  ),
                );
              });
        });
  }
}
