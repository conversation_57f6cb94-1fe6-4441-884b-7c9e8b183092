import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';
import 'package:matjari/src/view/components/images/custom_cached_network_image.dart';

import '../../../core/utils/matjari_icons.dart';
import '../../../core/values/app_styles.dart';
import '../../../core/values/colors.dart';
import '../../../data/models/hive/cart_item.dart';
import '../buttons/add_to_cart.dart';
import '../buttons/custom_filled_button.dart';

class CartItemListTile extends StatelessWidget {
  final CartItem item;
  final bool withDivider;
  const CartItemListTile({
    Key? key,
    required this.item,
    this.withDivider = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      // onTap: () {
      //   Get.bottomSheet(
      //     ProductBottomSheet(product: product),
      //   );
      // },
      child: Column(
        children: [
          Slidable(
            // startActionPane: ActionPane(
            //   motion: const ScrollMotion(),
            //   extentRatio: 0.25,
            //   children: [
            //     // SlidableAction(
            //     //   onPressed: (BuildContext context) {
            //     //     HiveProvider.removeCartItem(item.product.id);
            //     //   },
            //     //   icon: MatjariIcons.trash,
            //     //   backgroundColor: AppColors.redColor,
            //     // ),
            //   ],
            // ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  // Container(
                  //   width: 67,
                  //   height: 100,
                  //   decoration: BoxDecoration(
                  //       borderRadius: AppStyles.cardBorderRadius,
                  //       color: AppColors.greyColor,
                  //       border: Border.all(color: Colors.grey.shade300)),
                  //   child: CustomCachedNetworkImage(
                  //     imageUrl: item.product.image,
                  //     borderRadius: AppStyles.cardBorderRadius,
                  //     fit: BoxFit.cover,
                  //   ),
                  // ),

                  Container(
                    width: 100,
                    height: 80,
                    decoration: BoxDecoration(
                      borderRadius: AppStyles.cardBorderRadius,
                      color: AppColors.greyColor,
                    ),
                    padding: const EdgeInsets.all(10),
                    child: CustomCachedNetworkImage(
                      imageUrl: item.product.image,
                      fit: BoxFit.contain,
                    ),
                  ),

                  const SizedBox(width: 16),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                flex: 3,
                                child: Text(
                                  item.product.name,
                                  style: const TextStyle(
                                    height: 1.5,
                                    fontSize: 13,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),

                              Expanded(
                                  flex: 1,
                                  child: Container(
                                    alignment: AlignmentDirectional.centerEnd,
                                    child: InkWell(
                                      // padding: const EdgeInsets.all(8),
                                      // backgroundColor: Colors.transparent,
                                      onTap: () {
                                        HiveProvider.removeCartItem(
                                            item.product.id);
                                      },
                                      child: Icon(
                                        MatjariIcons.trash,
                                        color: Color(0xffba7441),
                                        size: 23,
                                      ),
                                    ),
                                  )),
                              // Icon( MatjariIcons.heart,color: AppColors.thirdColor,))
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              AddToCartButton(
                                productId: item.product.id,
                                quantity: item.quantity,
                                onPressed: () {},
                              ),
                              Expanded(
                                child: Text(
                                  "${item.product.itemPriceCurrencyVar}",
                                  textAlign: TextAlign.end,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: AppColors.primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (withDivider)
            const Divider(
              height: 1,
            )
        ],
      ),
    );
  }
}
