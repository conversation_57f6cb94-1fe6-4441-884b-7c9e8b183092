import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class SvgIconListTile extends StatelessWidget {
  final String svgIcon;
  final String title;
  final VoidCallback? onTap;
  final bool iconMatchTextDirection;
  final bool withDivider;
  const SvgIconListTile({
    Key? key,
    required this.svgIcon,
    required this.title,
    this.onTap,
    this.iconMatchTextDirection = false,
    this.withDivider = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
          leading: SvgPicture.asset(
            svgIcon,
            width: 22,
            fit: BoxFit.fitWidth,
            matchTextDirection: iconMatchTextDirection,
          ),
          title: Text(title),
          onTap: onTap,
          trailing: Icon(
            Icons.chevron_right,
            color: Get.theme.primaryColor,
          ),
        ),
        if (withDivider)
          const Divider(
            height: 1,
          ),
      ],
    );
  }
}
