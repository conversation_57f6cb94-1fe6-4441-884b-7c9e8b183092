import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/address_controller.dart';
import 'package:matjari/src/controllers/manage_address_controller.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/utils/matjari_icons.dart';
import 'package:matjari/src/data/models/address.dart';

import '../../../core/values/colors.dart';

class AddressListTile extends StatelessWidget {
  final Address address;

  final bool withDivider;
  const AddressListTile({
    Key? key,
    required this.address,
    this.withDivider = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Slidable(
          // startActionPane: ActionPane(
          //   // motion: const ScrollMotion(),
          //   extentRatio: 0.2,
          //   children: [
          //     // SlidableAction(
          //     //   onPressed: (BuildContext context) {
          //     //     CommonFunctions.showDeleteConfirmBottomSheet(onConfirm: () {
          //     //       AddressController.instance.deleteAddress(
          //     //         address.addressId,
          //     //       );
          //     //     });
          //     //   },
          //     //   icon: MatjariIcons.trash,
          //     //   backgroundColor: AppColors.redColor,
          //     // ),
          //   ],
          // ),
          child: InkWell(
            onTap: () async {
              if (AddressController.instance.forSelecting) {
                Get.back(
                  result: address,
                );
              } else {
                if (Get.isRegistered<ManageAddressController>()) {
                  Get.find<ManageAddressController>().dispose();
                  await Future.delayed(const Duration(milliseconds: 250));
                  Get.toNamed(Routes.MANAGE_ADDRESS_PAGE, arguments: address);
                } else {
                  Get.toNamed(Routes.MANAGE_ADDRESS_PAGE, arguments: address);
                }
              }
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 8),
              padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8),
              child: Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Icon(
                      MatjariIcons.addresses,
                      size: 30,
                      color: Get.theme.primaryColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: Text(
                            "${address.countryName} - ${address.cityName} - ${address.areaName}",
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Get.theme.primaryColor,
                            ),
                          ),
                        ),
                        Text(
                          address.description,
                          style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              height: 1.5),
                        ),
                      ],
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      CommonFunctions.showDeleteConfirmBottomSheet(
                        onConfirm: () {
                          AddressController.instance
                              .deleteAddress(address.addressId);
                        },
                      );
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: Icon(
                        MatjariIcons.trash,
                        color: AppColors.secondaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (withDivider)
          const Divider(
            height: 1,
            indent: 16,
            endIndent: 16,
          ),
      ],
    );
    // return IconListTile(
    //   title: "${address.city} (${address.area})",
    //   subtitle: address.description,
    //   svgIcon: AppConstants.addressTypeIconsValues[address.type].toString(),
    //   topTitle: AppConstants.addressTypeValues[address.type].toString(),
    //   onTap: onTap,
    //   trailing: InkWell(
    //     child: SvgPicture.asset(
    //       Assets.trash,
    //       width: 20,
    //     ),
    //     onTap: onDelete,
    //   ),
    // );
  }
}
