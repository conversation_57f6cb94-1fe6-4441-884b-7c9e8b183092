import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../core/values/app_styles.dart';

class CustomListTile extends StatelessWidget {
  final String title;
  final String subtitle;
  final String svgIcon;
  final VoidCallback? onTap;
  final bool iconMatchTextDirection;
  final String topTitle;
  final Widget? trailing;
  final EdgeInsetsGeometry? margin;

  const CustomListTile(
      {Key? key,
      required this.title,
      required this.subtitle,
      required this.svgIcon,
      this.onTap,
      this.iconMatchTextDirection = false,
      required this.topTitle,
      this.trailing,
      this.margin})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ??
          const EdgeInsets.symmetric(
            vertical: 5,
          ),
      child: Material(
        color: Get.theme.colorScheme.secondary,
        borderRadius: AppStyles.cardBorderRadius,
        child: InkWell(
          borderRadius: AppStyles.cardBorderRadius,
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
            child: Row(
              children: [
                SvgPicture.asset(
                  svgIcon,
                  matchTextDirection: iconMatchTextDirection,
                  width: 20,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        topTitle,
                        style: TextStyle(
                          color: Get.theme.primaryColor,
                        ),
                      ),
                      Text(
                        title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          height: 2,
                        ),
                      ),
                      Text(
                        subtitle,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                if (trailing != null)
                  SizedBox(
                    child: trailing,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
