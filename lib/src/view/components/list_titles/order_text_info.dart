import 'package:flutter/material.dart';
import 'package:get/get.dart';

class OrderTextInfo extends StatelessWidget {
  final String title;
  final String value;
  final bool withDivider;
  final Widget? icon;
  const OrderTextInfo({
    Key? key,
    required this.title,
    required this.value,
    this.withDivider = true,  this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
      
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            
              Text(
                title,
                style: TextStyle(
                  color: Get.theme.primaryColor,
                  fontSize: 12,
                  fontWeight: FontWeight.bold
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                      Padding(
                        padding: const EdgeInsetsDirectional.only(end:8.0),
                        child: icon,
                      ),
                  Expanded(child: Text(value,style:const TextStyle(height: 1.5,fontSize: 13.3),)),
                 
                  ],
                ),
              ),
            ],
          ),
        ),
        if (withDivider)
          const Divider(
            height: 1,
            indent: 16,
            endIndent: 16,
          ),
      ],
    );
  }
}