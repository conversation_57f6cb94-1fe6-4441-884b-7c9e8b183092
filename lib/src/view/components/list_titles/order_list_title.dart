import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:matjari/src/controllers/order_details_controller.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/values/app_styles.dart';
import 'package:matjari/src/core/values/colors.dart';

import '../../../core/values/app_constants.dart';
import '../../../data/models/order.dart';

class OrderListTile extends StatelessWidget {
  final Order order;
  final EdgeInsetsGeometry? margin;
  final bool withDivider;
  const OrderListTile({
    Key? key,
    required this.order,
    this.margin,
    this.withDivider = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: () async {
            final textPainter = TextPainter(
              text: TextSpan(
                  text: 'orderCanceled'.tr,
                  style:
                      TextStyle(fontFamily: GoogleFonts.almarai().fontFamily)),
              textDirection: Get.locale?.languageCode == "ar"
                  ? TextDirection.rtl
                  : TextDirection.ltr,
            );

            // Layout the text to obtain its width
            textPainter.layout();

            final textWidth = textPainter.width;
            print(textWidth);

            if (order.orderStatus == 6) {
              // Scaffold.of(context).s
              var snackBar = GetSnackBar(
                maxWidth: textWidth + 50,
                duration: const Duration(seconds: 2),

                margin: const EdgeInsets.only(bottom: 30),
                animationDuration: const Duration(milliseconds: 500),

                messageText: Text(
                  'orderCanceled'.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontFamily: GoogleFonts.almarai().fontFamily,
                      color: Colors.white),
                ),
                borderRadius: AppStyles.cardBorderRadius.bottomLeft.x,
                // behavior: SnackBarBehavior.floating,

                // duration: const Duration(seconds: 2),
                // elevation: 0,

                // shape: RoundedRectangleBorder(
                //     borderRadius: AppStyles.cardBorderRadius),
                // // margin: EdgeInsets.only(bottom: 30),
                // content: Container(
                //   width: textWidth + 50,
                //   child: Text(
                //     'orderCanceled'.tr,
                //     textAlign: TextAlign.center,
                //     style:
                //         TextStyle(fontFamily: GoogleFonts.almarai().fontFamily),
                //   ),
                // ),
                // ÷ Get.width * 0.3,
              );
              Get.showSnackbar(snackBar);
              //
            } else {
              if (Get.isRegistered<OrderDetailsController>(
                  tag: order.orderId.toString())) {
                Get.find<OrderDetailsController>(tag: order.orderId.toString())
                    .dispose();

                await Future.delayed(const Duration(milliseconds: 250));
                Get.toNamed(
                  Routes.ORDER_DETAILS_PAGE,
                  arguments: order,
                );
              } else {
                Get.toNamed(
                  Routes.ORDER_DETAILS_PAGE,
                  arguments: order,
                );
              }

              // await Future.delayed(Duration(seconds: 1));
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: IntrinsicHeight(
              child: Row(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
                AspectRatio(
                  aspectRatio: 100/80,
                  child: Container(
                    // height: 80,
                    // width: 80,
                    // height: double.maxFinite,
                    // width: 100,
                    decoration: BoxDecoration(
                      color: AppColors.greyColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: SvgPicture.asset(
                            AppConstants.orderStatusSvgValues[order.orderStatus]!,
                            fit: BoxFit.scaleDown,
                            width: 24,
                            height: 24,
                            // color: Get.theme.primaryColor,
                          ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text(
                      order.orderStatusString,
                      style: TextStyle(
                        fontSize: 14,
                        color: Get.theme.primaryColor,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: Text(
                        '${order.orderId}#',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Text(
                      order.orderDate,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                )),
              ]),
            ),
          ),
        ),
        if (withDivider)
          const Divider(
            height: 1,
            indent: 16,
            endIndent: 16,
          ),
      ],
    );
  }
}
