import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:matjari/src/data/models/currency.dart';

class CurrencyListTail extends StatefulWidget {
  const CurrencyListTail({
    super.key,
    required this.currency,
    this.onTap,
    required this.tapedListTailIndex,
    required this.currentIndex,
  });
  final Currency currency;
  final void Function()? onTap;

  final int tapedListTailIndex;
  final int currentIndex;

  @override
  State<CurrencyListTail> createState() => _CurrencyListTailState();
}

class _CurrencyListTailState extends State<CurrencyListTail> {
  var tabed = false;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        tabed = widget.tapedListTailIndex == widget.currentIndex;
      });
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return TweenAnimationBuilder(
        curve: Curves.easeInOut,
        duration: const Duration(milliseconds: 500),
        tween: Tween<double>(begin: 0.0, end: 1.0),
        builder: (context, double value, child) {
          return Container(
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                gradient: LinearGradient(
                    colors: [Colors.grey.shade100, Get.theme.primaryColor],
                    stops: [!tabed ? 1 : 1 - value, 0.0])),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(30),
                onTap: () {
                  if (widget.onTap != null) {
                    widget.onTap!();
                  }
                },
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 18, horizontal: 20),
                  child: Row(
                    children: [
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 500),
                        alignment: Alignment.center,
                        height: 40,
                        width: 40,
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: !tabed
                                ? Get.theme.primaryColor
                                : Colors.grey.shade100),
                        child: AnimatedDefaultTextStyle(
                          duration: const Duration(milliseconds: 500),
                          style: Get.textTheme.bodyMedium!.copyWith(
                              color:
                                  tabed ? Get.theme.primaryColor : Colors.white,
                              fontSize: 13,
                              fontWeight: FontWeight.bold),
                          child: Text(
                            widget.currency.currencyCode ?? "",
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 16,
                      ),
                      AnimatedDefaultTextStyle(
                        duration: const Duration(milliseconds: 500),
                        style: Get.textTheme.bodyMedium!.copyWith(
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                          color: tabed ? Colors.white : Colors.black,
                        ),
                        child: Text(
                          widget.currency.currencyName ?? "",
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }
}
