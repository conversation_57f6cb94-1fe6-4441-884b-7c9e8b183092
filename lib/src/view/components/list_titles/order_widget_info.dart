import 'package:flutter/material.dart';
import 'package:get/get.dart';

class OrderWidgetInfo extends StatelessWidget {
  final String title;
  final Widget widget;
  final bool withDivider;
  const OrderWidgetInfo({
    Key? key,
    required this.title,
    required this.widget,
    this.withDivider = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: Get.theme.primaryColor,
                               fontSize: 12,
                  fontWeight: FontWeight.bold
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(right: 16, left: 16),
          child: widget,
        ),
        if (withDivider)
          const Divider(
            height: 1,
            indent: 16,
            endIndent: 16,
          ),
      ],
    );
  }
}
