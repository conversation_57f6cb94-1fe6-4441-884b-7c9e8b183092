// ar translations
Map<String, String> ar = {
  // common
  'confirm': "تأكيد",
  'cancel': "إلغاء",
  'edit': "تعديل",
  'retry': "إعادة المحاولة",
  'add': "إضافة",
  'addNew': "إضافة جديد",
  'delete': "حذف",
  'save': "حفظ",
  'yes': 'نعم',
  'no': 'لا',
  'close': 'إغلاق',
  'back': 'رجوع',
  'done': 'تم',
  "change": "تغيير",
  'apply': 'تطبيق',

  'deleteConfirmationTitle': 'تأكيد الحذف',
  'deleteConfirmationMessage': 'هل أنت متأكد من حذف هذا العنصر؟',
  'name': 'الاسم',
  'nameHint': 'ادخل إسمك الثلاثي',
  'mobileNumber': 'رقم الجوال',
  'mobileNumberHint': 'ادخل رقم الجوال',
  'whatsappNumber': 'رقم الواتساب',
  'whatsappNumberHint': 'ادخل رقم الواتساب',
  'email': 'البريد الإلكتروني',
  'emailHint': 'ادخل بريدك الإلكتروني',
  //
  'login': "تسجيل الدخول",
  'signup': "تسجيل حساب جديد",
  'home': "القائمة",
  'contactUs': "اتصل بنا على",
  'more': "المزيد",
  'YR': 'ر.ي',
  'welcome': 'مرحبا',
  'myAddresses': 'العناوين',
  'myOrders': 'طلباتي',
  'myFavorite': 'المفضلة',
  'changePassword': 'تغيير كلمة المرور',
  'logout': 'تسجيل الخروج',
  'noInternet': 'لا يوجد اتصال بالإنترنت',
  'noInternetConnection': 'عذراً، لا يوجد اتصال بالإنترنت',
  'noData': 'عذراً، لا يوجد بيانات',
  "noOrderMessage": "عذراً، ليس لديك أي طلبات حالياً",
  "No Addresses Found": "عذراً، ليس لديك أي عناوين",
  'unexpectedError': 'عذراً، حدث خطأ غير متوقع',
  'addNewAddress': 'إضافة عنوان جديد',
  'addAddress': 'إضافة عنوان',
  'noOrders': 'عذراً، ليس لديك طلبات سابقة',
  'noAddresses': 'عذراً، ليس لديك عناوين',
  'aboutUs': 'من نحن',
  'aboutApp': 'عن التطبيق',
  'about_app_description':
      'متجري هو أول متجر ألكتروني متخصص لبيع الجملة ومتخصص لتوزيع المنتجات لأصحاب المحلات',
  'version': 'الإصدار',
  'share': 'مشاركة التطبيق',
  'followUs': 'تابعنا على',
  'messageUs': 'راسلنا',
  'developedBy': 'تصميم وتطوير',
  'init': 'وصول الطلب',
  'waiting': 'قيد الانتظار',
  'preparing': 'جاري تحضير الطلب',
  'shipped': 'في الطريق إليك',
  'delivered': 'تم التوصيل',
  'canceled': 'تم الإلغاء',
  'orderDetails': 'تفاصيل الطلب',
  'orderStatus': 'حالة الطلب',
  'orderDate': 'تاريخ الطلب',
  'address': 'العنوان',
  'cart': 'السلة',
  'total': 'الإجمالي',
  "order total": "إجمالي الطلب",
  'amount': 'المبلغ',
  'items': 'العناصر',
  "phone": "الهاتف",
  'outOFWorkHours':
      'معذرتاً, نحن الآن خارج ساعات العمل سيتم توصيل طلبك في أقرب وقت',
  'checkout': 'إكمال الطلب',
  'placeOrder': 'إرسال الطلب',
  'phoneNumber': "رقم المحمول",
  'phoneNumberConfirmation': "تأكيد رقم المحمول",
  'enterCodeMessage': "لقد تم إرسال رمز التحقق إلى رقم الهاتف المحمول الخاص بك",
  'invalidPhoneNumberMessage': "رقم الهاتف غير صحيح",
  'invalidCodeMessage': "رمز التحقق غير صحيح",
  'loadingMessage': "يرجى الانتظار",
  'map': "خريطة",
  'satellite': "قمر صناعي",
  'emptyAddresses': 'ليس لديك عناوين محفوظة',
  'deleteAddressConfirmation': 'تأكيد حذف العنوان',
  'deleteAddressMessage': 'هل أنت متأكد من حذف العنوان؟',
  'editAddress': 'تعديل العنوان',
  'information': 'معلومات',
  'abuElias': 'Abu Elias',
  'conditions': 'الشروط والأحكام',
  'privacy': 'سياسة الخصوصية',
  // follow us
  'facebook': 'فيسبوك',
  'twitter': 'تويتر',
  'instagram': 'انستجرام',
  'youtube': 'يوتيوب',
  'tiktok': 'تيك توك',
  'addToCart': 'اضف للسلة',
  'searchHere': 'ابحث عن ...',
  'searchHint': 'ابحث عن منتجاتك',
  'rate': 'تقييم التطبيق',
  // payment methods
  'cashOnDelivary': 'الدفع عند الاستلام',
  'bankTransfer': 'إيداع بنكي',
  'moneyTransfer': 'تحويل عبر شبكة صرافة',
  'cashWallet': 'محفظة كاش',
  'loginBottomSheetMessage': 'عذراً، يجب عليك تسجيل الدخول اولاً',
  'loginConfirmText': 'دخول',
  'loginCancelText': 'إلغاء',
  'logoutBottomSheetMessage': 'هل تريد تسجيل الخروج؟',
  'logoutConfirmText': 'تسجيل خروج',
  'logoutCancelText': 'إلغاء',
  'profile': 'الملف الشخصي',
  'noSearchResults': 'لا يوجد نتائج لبحثك',
  'noProducts': 'عذراً لايوجد وجبات في هذا القسم',
  'noFavoriteProducts': 'عذراً، لا يوجد وجبات مفضلة',
  'cartTotal': 'الإجمالي:',
  'emptyCart': 'سلة الطلب فارغة',
  'continueShopping': 'متابعة التسوق',
  'deliveryAddress': 'عنوان التوصيل',
  'selectDeliveryAddress': 'اختر عنوان التوصيل',
  'selectPaymentMethod': 'اختر طريقة الدفع',
  'buyNow': 'اشتري الآن!',
  "language": "اللغة",
  'ar': 'العربية',
  'en': 'الإنجليزية',
  "chooseLanguage": "اختر اللغة",
  "orderItems": "الأصناف المطلوبة",
  'deliveryTime': 'وقت التوصيل',
  'deliveryTimeHint': 'حدد وقت التوصيل',
  'paymentMethod': 'طريقة الدفع',
  'paymentMethodHint': 'حدد طريقة الدفع',
  'notes': 'ملاحظات',
  'notesHint':
      'اترك ملاحظتك للمطعم ليتم اخذها في عين الاعتبار عند اعداد طلبك..',
  'orderNotes': 'ملاحظات الطلب',
  'cancelOrder': 'إلغاء الطلب',
  'orderCanceled': 'تم إلغاء الطلب',
  'couponQuestion': 'هل لديك كوبون خصم؟',
  'couponHint': 'أدخل رمز الكوبون هنا ...',
  'coupon': 'كوبون خصم',
  'couponInvalid': 'رمز الكوبون غير صحيح',
  'couponApplied': 'تم تطبيق الكوبون بنجاح',
  'couponRemoved': 'تم إلغاء الكوبون بنجاح',
  'quantity': 'الكمية',
  'price': 'السعر',
  'discount': 'الخصم',
  'deliveryFees': 'رسوم التوصيل',
  'item': "الصنف",
  'mapLocation': 'الموقع على الخريطة',

  'paymentAccountNumberCopiedSuccessfully': 'تم نسخ رقم الحساب بنجاح',
  'transferNumber': 'رقم العملية',
  'transferCompany': 'اسم الشركة',
  //addresss
  'country': 'الدولة',
  'countryHint': 'حدد الدولة',
  'city': 'المدينة',
  'cityHint': 'حدد المدينة',
  'area': 'المنطقة',
  'areaHint': 'حدد المنطقة',
  'addressDescription': 'وصف العنوان',
  'addressDescriptionHint': 'الشارع،العمارة، الشقة , الطابق ....',
  'selectCountryFirst': 'حدد الدولة أولاً ',
  'selectCityFirst': 'حدد المدينة أولاً ',
  'loadingDataMessage': 'جاري تحميل البيانات ...',
  'previous': '< السابق',
  'next': 'التالي >',
  'verificationCode': 'رمز التحقق',
  "resendCode": "إعادة الارسال",
  'verificationCodeHint': 'أدخل رمز التحقق',

  // bottom sheets
  "deleteFromFavoritesMessage": "هل أنت متأكد من حذف الوجبة من المفضلة؟",

  // validation messages
  'requiredValidationMessage': 'هذا الحقل مطلوب',
  'requiredValidationMessageWithFieldName': 'حقل @fieldName مطلوب',
  'minLengthValidationMessage':
      'يجب أن يكون طول هذا الحقل على الأقل @min حرفاً',
  'minWorldLengthValidationMessage':
      'يجب أن يكون طول  الكلمة على الأقل @min حرفاً',
  'maxLengthValidationMessage':
      'يجب أن يكون طول هذا الحقل على الأكثر @max حرفاً',
  'lengthValidationMessage': 'يجب أن يكون طول هذا الحقل @length ارقام',
  'lengthValidationMessageWithFieldName':
      'حقل @fieldName يجب أن يكون @length ارقام',
  'minValueValidationMessage': 'يجب أن يكون قيمة هذا الحقل على الأقل @min',
  'maxValueValidationMessage': 'يجب أن يكون قيمة هذا الحقل على الأكثر @max',
  'emailValidationMessage':
      'يجب أن يكون قيمة هذا الحقل عنوان بريدك الإلكتروني صحيح',
  'urlValidationMessage': 'يجب أن يكون قيمة هذا الحقل رابط صحيح',
  'phoneValidationMessage': 'يجب أن يكون قيمة هذا الحقل رقم هاتف صحيح',
  'sameValidationMessage': 'يجب أن يكون قيمة هذا الحقل مطابقة لقيمة @same',
  'minWordsValidationMessageSingular':
      'يجب أن يكون قيمة هذا الحقل على الأقل @min كلمة',
  'minWordsValidationMessagePlural':
      'يجب أن يكون قيمة هذا الحقل على الأقل @min كلمات',

  //upgrader

  "upgraderAlertTitle": "هل تريد تحديث التطبيق؟",
  "upgraderAlertbody":
      "نسخة جديدة من تطبيق @app_name متوفرة \n النسخة الحالية @app_version",
  "upgraderAlertaction": "حدث الان",
  "later": "لاحقاً",
  "sort": "ترتيب حسب",
  "suspend_account": "إيقاف الحساب",
  //currency

  'selectCurrency': 'حدد العملة',
  'currency': 'العملة',
  "most_views": "الأكثر مشاهدة",
  "most_sellings": "الأكثر مبيعا",
  "lowest_price": "الأقل سعراً للأعلى",
  "heightest_price": "الأعلى سعراً للأقل",
  "pin": "رقم التعريف الشخصي",
  "pin_hint": "رقم التعريف الشخصي PIN Pass",
  "phone_mobile_account": "رقم حسابك",
  'phone_mobile_account_hint': 'رقم موبايل حسابك في @name',
  'voucher_code': "كود الشراء",
  "code": "كود التحقق",
  "code_hint": "قم بإدخال كود التحقق المرسل من @name",
  'voucher_code_hint': "قم بإدخال كود الشراء من @name",
  'paymentConfirm': "إتمام عملية الدفع",
  "orderTracking": "تتبع الطلب",
  "appName": "مطعم القلعة",
  "My account": "الحساب",
  "Favorite": "المفضلة",
  "Good Morning": "صباح الخير",
  "Good Afternoon": "نهارك سعيد",
  "Good Evening": "مساء الخير",
  "Welcome in": "حياكم في",
  "Place order": "تنفيذ الطلب",
  "notification permission alert":
      "قم بتفعيل الاشعارات الآن لتتبع الطلبات والحصول على العروض الخاصة وجديد المتجر",
};

Map<String, String> en = {
// common
  'confirm': "Confirm",
  'cancel': "Cancel",
  'edit': "Edit",
  'retry': "Retry",
  'add': "Add",
  'addNew': "Add New",
  'delete': "Delete",
  'save': "Save",
  'yes': "Yes",
  'no': "No",
  'close': "Close",
  'back': "Back",
  'done': "Done",
  "change": "Change",
  'apply': "Apply",

  'deleteConfirmationTitle': 'Delete Confirmation',
  'deleteConfirmationMessage': 'Are you sure you want to delete this item?',
  'name': 'Name',
  'nameHint': 'Enter your full name',
  'mobileNumber': 'Mobile Number',
  'mobileNumberHint': 'Enter your mobile number',
  'whatsappNumber': 'WhatsApp Number',
  'whatsappNumberHint': 'Enter your WhatsApp number',
  'email': 'Email',
  'emailHint': 'Enter your email address',
//
  'login': "Login",
  'signup': "Sign Up",
  'home': "Home",
  'contactUs': "Contact Us",
  'more': "More",
  'YR': 'YR',
  'welcome': "Welcome",
  'myAddresses': "My Addresses",
  'myOrders': "My Orders",
  'myFavorite': "My Favorite",
  'changePassword': "Change Password",
  'logout': "Logout",
  'noInternet': 'No Internet Connection',
  'noInternetConnection': 'Sorry, there is no internet connection',
  'noData': 'Sorry, there is no data',
  "noOrderMessage": "Sorry, you don't have any orders yet",
  'unexpectedError': 'Sorry, an unexpected error occurred',
  'addNewAddress': 'Add New Address',
  'addAddress': 'Add Address',
  'noOrders': "Sorry, you don't have any previous orders",
  'noAddresses': "Sorry, you don't have any addresses",
  'aboutUs': 'About Us',
  'aboutApp': 'About the App',
  'about_app_description':
      'My Store is the first online store specialized in wholesale sales and distributing products to shop owners',
  'version': 'Version',
  'share': 'Share App',
  'followUs': 'Follow Us',
  'messageUs': 'Message Us',
  'developedBy': 'Designed and Developed',
  'waiting': 'Pending',
  'preparing': 'Preparing',
  'shipped': 'On its way to you',
  'delivered': 'Delivered',
  'canceled': 'Canceled',
  'orderDetails': 'Order Details',
  'orderStatus': 'Order Status',
  'orderDate': 'Order Date',
  'address': 'Address',
  'cart': 'Cart',
  'total': 'Total',
  'items': 'Items',
  'outOFWorkHours':
      'Sorry, we are currently out of working hours. Your order will be delivered as soon as possible',
  'checkout': 'Checkout',
  'placeOrder': 'Place Order',
  'phoneNumber': "Phone Number",
  'phoneNumberConfirmation': "Confirm Phone Number",
  'enterCodeMessage': "A verification code has been sent to your mobile number",
  'invalidPhoneNumberMessage': "The phone number is invalid",
  'invalidCodeMessage': "The verification code is invalid",
  'loadingMessage': "Please wait",
  'map': 'Map',
  'satellite': 'Satellite',
  'emptyAddresses': 'You do not have any saved addresses',
  'deleteAddressConfirmation': 'Delete Address Confirmation',
  'deleteAddressMessage': 'Are you sure you want to delete this address?',
  'editAddress': 'Edit Address',
  'information': 'Information',
  'abuElias': 'Abu Elias',
  'conditions': 'Terms and Conditions',
  'privacy': 'Privacy Policy',
// follow us
  'facebook': 'Facebook',
  'twitter': 'Twitter',
  'instagram': 'Instagram',
  'youtube': 'YouTube',
  'tiktok': 'TikTok',
  'addToCart': 'Add to Cart',
  'searchHere': 'Search for...',
  'searchHint': 'Search for your products',
  'rate': 'Rate the app',
// payment methods
  'cashOnDelivary': 'Cash on Delivery',
  'bankTransfer': 'Bank Transfer',
  'moneyTransfer': 'Money Transfer',
  'cashWallet': 'Cash Wallet',
  'loginBottomSheetMessage': 'Sorry, you must login first',
  'loginConfirmText': 'Login',
  'loginCancelText': 'Cancel',
  'profile': 'Profile',
  'noSearchResults': 'No results found for your search',
  'noProducts': 'Sorry, there are no products in this category',
  'noFavoriteProducts': 'Sorry, there are no favorite products',
  'cartTotal': 'Total:',
  'emptyCart': 'Your cart is empty',
  'continueShopping': 'Continue Shopping',
  'deliveryAddress': 'Delivery Address',
  'selectDeliveryAddress': 'Select delivery address',
  'buyNow': 'Buy Now!',
  "language": "Language",
  'ar': 'Arabic',
  'en': 'English',
  "chooseLanguage": "Choose Language",
  "orderItems": "Ordered Items",
  'deliveryTime': 'Delivery Time',
  'deliveryTimeHint': 'Select delivery time',
  'paymentMethod': 'Payment Method',
  'paymentMethodHint': 'Select payment method',
  'notes': 'Notes',
  'notesHint':
      'Leave your note for the restaurant so that it can be taken into consideration when preparing your order...',
  'orderNotes': 'Order Notes',
  'couponQuestion': 'Do you have a discount coupon?',
  'couponHint': 'Enter coupon code here...',
  'coupon': 'Discount Coupon',
  'couponInvalid': 'Invalid coupon code',
  'couponApplied': 'Coupon applied successfully',
  'couponRemoved': 'Coupon removed successfully',
  'quantity': 'Quantity',
  'price': 'Price',
  'discount': 'Discount',
  'deliveryFees': 'Delivery Fees',
  'item': "Item",
  'mapLocation': 'Map Location',
//addresss
  'country': 'Country',
  'countryHint': 'Select country',
  'city': 'City',
  'cityHint': 'Select city',
  'area': 'Area',
  'areaHint': 'Select area',
  'addressDescription': 'Address Description',
  'addressDescriptionHint': 'Street, Building, Apartment, Floor....',
  'selectCountryFirst': 'Select country first',
  'selectCityFirst': 'Select city first',
  'loadingDataMessage': 'Loading data...',
  'previous': '<< Previous',
  'next': 'Next >>',
  'verificationCode': 'Verification Code',
  'verificationCodeHint': 'Enter verification code',

// bottom sheets
  "deleteFromFavoritesMessage":
      "Are you sure you want to remove the product from favorites?",

//

// validation messages
  'requiredValidationMessage': 'This field is required',
  'requiredValidationMessageWithFieldName': 'The field @fieldName is required',
  'minLengthValidationMessage':
      'This field must be at least @min characters long',
  'maxLengthValidationMessage':
      'This field must be at most @max characters long',
  'lengthValidationMessage': 'This field must be @length digits long',
  'lengthValidationMessageWithFieldName':
      'The field @fieldName must be @length digits long',
  'minValueValidationMessage': 'This field must be at least @min',
  'maxValueValidationMessage': 'This field must be at most @max',
  'emailValidationMessage': 'This field must be a valid email address',
  'urlValidationMessage': 'This field must be a valid URL',
  'phoneValidationMessage': 'This field must be a valid phone number',
  'sameValidationMessage': 'This field must match the @same field',
  'minWordsValidationMessageSingular':
      'This field must have at least @min word',
  'minWordsValidationMessagePlural': 'This field must have at least @min words',
};
