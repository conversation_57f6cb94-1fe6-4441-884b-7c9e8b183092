extension StringExtension on String {
  String sublistWord(int start, int end) {
    var substring = (this).split(" ");

    if (substring.length > end) {
      return substring.sublist(start, end).join(" ");
    } else {
      return substring.sublist(start).join(" ");
    }
  }

  String decodeUnicode() {
  return this.replaceAllMapped(RegExp(r'u([0-9a-fA-F]{4})'), (match) {
    final code = int.parse(match.group(1)!, radix: 16);
    return String.fromCharCode(code);
  });
}

}
