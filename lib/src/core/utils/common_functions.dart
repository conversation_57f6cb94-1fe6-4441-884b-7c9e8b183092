import 'dart:io';

import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/about_app_controller.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/utils/extenstions/string_extension.dart';
import 'package:matjari/src/data/models/ads.dart';
import 'package:matjari/src/data/services/auth_services.dart';
import 'package:matjari/src/view/components/images/custom_cached_network_image.dart';
import 'package:unique_identifier/unique_identifier.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../data/vms/dialog/dialog_action_item.dart';
import '../../view/components/bottom_sheets/confirm_bottom_sheet.dart';
import 'package:intl/intl.dart' as intl;

import '../../view/components/bottom_sheets/profile_bottom_sheet.dart';
import '../../view/components/buttons/custom_filled_button.dart';

import '../values/assets.dart';
import '../values/colors.dart';

class CommonFunctions {
  static bool isDirectionRTL() {
    return intl.Bidi.isRtlLanguage(Get.locale?.languageCode);
  }

  static void showProfileComplatenessAlertDialog() {
    showDialog(
        radius: 20,
        barrierDismissible: false,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              Assets.alert,
              height: 70,
              width: 2,
              color: AppColors.secondaryColor,
            ),
            const SizedBox(
              height: 8,
            ),
            const Text(
              "إكمال الملف الشخصي",
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(
              height: 16,
            ),
            Text(
              "يجب إكمال ادخال بياناتك الشخصية",
              style: TextStyle(color: Colors.grey.shade400, fontSize: 14),
            ),
            Container(
                margin: const EdgeInsets.only(top: 16),
                width: double.maxFinite,
                child: CustomFilledButton(
                    child: const Text("تم"),
                    onPressed: () {
                      Get.back();
                      Get.bottomSheet(
                        WillPopScope(
                            onWillPop: () async {
                              return false;
                            },
                            child: ProfileBottomSheet(
                              required: true,
                            )),
                        isScrollControlled: true,
                        isDismissible: false,
                        enableDrag: false,
                      );
                    }))
          ],
        ),
        onWillPop: () async {
          return false;
        });
  }

  static void showPopUpAd(Ad ad) {
    // var height = Rx<double>(Get.height * 0.4);

    // Get.dialog(Material(
    //   color: Colors.transparent,

    //   child: FittedBox(

    //     child: InkWell(
    //           hoverColor: Colors.transparent,
    //           focusColor: Colors.transparent,
    //           onTap: () {

    //             if (ad.adUrl != "") {
    //               CommonFunctions.openUrl(ad.adUrl);
    //             } else if (ad.adProductId != null) {
    //               Get.toNamed(Routes.PRODUCT_PAGE, arguments: ad.adProductId);
    //             }
    //           },
    //           child: CustomCachedNetworkImage(
    //             imageUrl: ad.adImage,
    //             fit: BoxFit.fitWidth,

    //             // imageBuilder: (context, imageProvider) {

    //             //   return  Container(
    //             //       constraints: BoxConstraints(
    //             //         minHeight: 300,
    //             //         maxHeight: Get.height * 0.7,
    //             //         minWidth: double.maxFinite,
    //             //       ),
    //             //       decoration: BoxDecoration(
    //             //         image: DecorationImage(
    //             //           image: imageProvider,
    //             //           fit: BoxFit.fitWidth,
    //             //         ),
    //             //       ),
    //             //     );}),
    //           ),
    //         ),

    //   ),
    // ),barrierDismissible: true);
    Get.dialog(
      // radius: 20,

      // contentPadding:const EdgeInsets.all(8),
      // contentInnerPadding:const EdgeInsets.all(8) ,
      // barrierDismissible: true,

      AlertDialog(
        // backgroundColor: Colors.transparent,
        contentPadding: const EdgeInsets.all(16),
        insetPadding: EdgeInsets.zero,

        backgroundColor: Colors.transparent,
        // icon:  Container(
        //   width:40,
        //   child: Material(

        //       child: IconButton(

        //           onPressed: () {
        //             Get.back();
        //           },
        //           icon: const Icon(Icons.close)),
        //     ),
        // ),
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(10))),
        iconPadding: const EdgeInsetsDirectional.only(end: 23),
        icon: Align(
          alignment: AlignmentDirectional.centerEnd,
          child: Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2)),
              child: InkWell(
                onTap: () {
                  Get.back();
                },
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 20,
                ),
              )),
        ),
        content:
            //  Column(
            //   mainAxisSize: MainAxisSize.min,
            //   children: [
            //    Material(
            //   child: IconButton(
            //       onPressed: () {
            //         Get.back();
            //       },
            //       icon: const Icon(Icons.close)),
            // ),
            Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: BoxConstraints(
                maxHeight: Get.height * 0.7,
                minWidth: double.maxFinite,
              ),
              child: InkWell(
                hoverColor: Colors.transparent,
                focusColor: Colors.transparent,
                highlightColor: Colors.transparent,
                borderRadius: BorderRadius.circular(10),
                onTap: () {
                  if (ad.adUrl != "") {
                    CommonFunctions.openUrl(ad.adUrl);
                  } else if (ad.adProductId != null && ad.adProductId != 0) {
                    Get.toNamed(Routes.PRODUCT_PAGE, arguments: ad.adProductId);
                  } else if (ad.adPublisherId != null &&
                      ad.adPublisherId != 0) {
                    Get.toNamed(Routes.PUBLISHING_HOUSE_PAGE, arguments: {
                      "publisher_id": ad.adPublisherId,
                      "publisher_name": ad.adTitle.toString()
                    });
                  }
                },
                child: CustomCachedNetworkImage(
                    imageUrl: ad.adImage,
                    fit: BoxFit.fitWidth,
                    borderRadius: BorderRadius.circular(10),
                    placeholder: (context, url) {
                      return SizedBox(
                        height: Get.height * 0.4,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            SvgPicture.asset(
                              Assets.defaultImage,
                            ),
                            SpinKitRipple(
                              color: Get.theme.primaryColor,
                              size: 50,
                            ),
                          ],
                        ),
                      );
                    }),

                // imageBuilder: (context, imageProvider) {

                //   return  Container(
                //       constraints: BoxConstraints(
                //         minHeight: 300,
                //         maxHeight: Get.height * 0.7,
                //         minWidth: double.maxFinite,
                //       ),
                //       decoration: BoxDecoration(
                //         image: DecorationImage(
                //           image: imageProvider,
                //           fit: BoxFit.fitWidth,
                //         ),
                //       ),
                //     );}),
              ),
            ),
          ],
        ),
      ),
    )
        //   ],
        // ),
        ;
  }

  static Future<T?> showDialog<T>({
    String? title,
    EdgeInsetsGeometry? titlePadding,
    EdgeInsetsGeometry? contentPadding,
    EdgeInsetsGeometry? contentInnerPadding,
    TextStyle? titleStyle,
    Widget? content,
    DialogActionItem? confirm,
    DialogActionItem? cancel,
    Color? backgroundColor,
    Color? titleBackgroundColor,
    bool barrierDismissible = true,
    double radius = 10.0,
    WillPopCallback? onWillPop,
  }) {
    List<Widget> actions = [];
    if (confirm != null) {
      actions.add(
        // Container(),
        CustomFilledButton(
          // borderRadius: BorderRadius.circular(32),
          onPressed: () {
            if (confirm.onClicked != null) {
              confirm.onClicked!();
            }
          },
          backgroundColor: confirm.background ?? Get.theme.primaryColor,
          child: Text(confirm.label!),
        ),
      );
    }
    if (cancel != null) {
      actions.add(
        // Container(),
        CustomFilledButton(
          backgroundColor: cancel.background ?? Colors.redAccent,
          onPressed: () {
            if (cancel.onClicked != null) {
              cancel.onClicked!();
            }
            Get.back();
          },
          child: Text(cancel.label!),
        ),
      );
    }

    Widget baseAlertDialog = FractionallySizedBox(
        widthFactor: 1,
        child: AlertDialog(
          titlePadding: const EdgeInsets.all(0),
          contentPadding: contentPadding ??
              const EdgeInsets.only(top: 10, right: 10, left: 10, bottom: 10),
          backgroundColor: Get.theme.dialogBackgroundColor,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(radius))),
          title: title != null
              ? Container(
                  padding: titlePadding ?? const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: titleBackgroundColor ?? Get.theme.primaryColor,
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(radius)),
                  ),
                  child: Text(
                    title,
                    textAlign: TextAlign.center,
                    style: titleStyle ??
                        const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                  ),
                )
              : Container(),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: contentInnerPadding ?? EdgeInsets.zero,
                  child: content ?? Container(),
                ),
                (confirm != null && cancel != null)
                    ? Row(
                        children: actions
                            .map(
                              (e) => Expanded(
                                  child: Padding(
                                padding: const EdgeInsets.all(4.0),
                                child: e,
                              )),
                            )
                            .toList(),
                      )
                    : ButtonTheme(
                        child: Wrap(
                          alignment: WrapAlignment.center,
                          children: actions,
                        ),
                      )
              ],
            ),
          ),
          buttonPadding: EdgeInsets.zero,
        ));

    if (onWillPop != null) {
      return Get.dialog<T>(
        WillPopScope(
          onWillPop: onWillPop,
          child: baseAlertDialog,
        ),
        barrierDismissible: barrierDismissible,
      );
    }

    return Get.dialog<T>(
      baseAlertDialog,
      barrierDismissible: barrierDismissible,
    );
  }

  static void showErrorMessage(String message) {
    Get.snackbar("خطأ".tr, message,
        colorText: Colors.white,
        backgroundColor: Colors.red,
        margin: const EdgeInsets.only(top: 10, right: 10, left: 10),
        duration: const Duration(seconds: 5));
  }

  static void showSuccessMessage(String message) {
    if (Get.isSnackbarOpen) {
      Get.closeCurrentSnackbar();
    }
    Get.snackbar("نجاح".tr, message,
        colorText: Colors.white,
        backgroundColor: Colors.green,
        margin: const EdgeInsets.all(10),
        duration: const Duration(seconds: 2));
  }

  static void handleError(Object err) {
    if (kDebugMode) {
      print(err.runtimeType);
    }
    if (err.runtimeType == DioException) {
      var error = err as DioException;

      if (error.type == DioExceptionType.badResponse) {
        // print(error.error);
        if (error.type == DioExceptionType.badResponse) {
          if (error.response?.statusCode == 400) {
            if (error.response?.data is String) {
              showErrorMessage(error.response?.data.toString() ?? "");
            } else {
              showErrorMessage(
                  (error.response?.data as Map).values.first[0].toString());
            }
          } else if (error.response?.statusCode == 403) {
            if (kDebugMode) {
              print((error.response?.data as Map).values.first);
            }
            showErrorMessage(
                (error.response?.data as Map).values.first[0].toString());
          } else if (error.response?.statusCode == 404) {
            showErrorMessage("غير قادر على الاتصال بالسرفر".tr);
          } else if (error.response?.statusCode == 500) {
            if (kDebugMode) {
              print(error.response?.data);
            }
            showErrorMessage("عذراً حدث خطأ في السرفر".tr);
          } else if (error.response?.statusCode == 401) {
            if (kDebugMode) {
              print(error.response?.data);
            }
            showErrorMessage("انت غير مسجل دخول".tr);
          } else {
            showErrorMessage(error.response?.data[0]);
            if (kDebugMode) {
              print(error.response?.data);
            }
          }
        } else {
          showErrorMessage("تأكد من اتصالك بالانترنت وعاود المحاولة لاحقاً".tr);
        }
      } else if (error.type == DioExceptionType.unknown) {
        showErrorMessage("حدث خطأ غير متوقع حاول مرة اخرى ".tr);
      } else {
        showErrorMessage("حدث خطاء!   الرجاء إعادة المحاولة");
      }
    } else if (err.runtimeType == FirebaseAuthException) {
      FirebaseAuthException error = err as FirebaseAuthException;
      print(error.code);
      if (error.code == "invalid-verification-code") {
        showErrorMessage("رمز التحقق غير صحيح".tr);
      }
    }
  }

  static void showChangeLocaleBottomSheet() {
    Get.bottomSheet(
      SingleChildScrollView(
        child: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  "اختر اللغة".tr,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Divider(
                indent: 16,
                endIndent: 16,
                height: 1,
              ),
              ListTile(
                title: Text("العربية".tr),
                onTap: () {
                  Get.back();
                  Get.updateLocale(const Locale("ar"));
                },
                trailing: Get.locale?.languageCode == "ar"
                    ? Icon(
                        Icons.check,
                        color: Get.theme.primaryColor,
                      )
                    : null,
              ),
              const Divider(
                height: 1,
              ),
              ListTile(
                title: Text("English".tr),
                onTap: () {
                  Get.back();
                  Get.updateLocale(const Locale("en"));
                },
                trailing: Get.locale?.languageCode == "en"
                    ? Icon(Icons.check, color: Get.theme.primaryColor)
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }

  static void showConfirmBottomSheet({
    required String title,
    required String message,
    String? confirmText,
    String? cancelText,
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
  }) {
    Get.bottomSheet(
      ConfirmBottomSheet(
        title: title,
        message: message,
        confirmText: confirmText,
        cancelText: cancelText,
        onConfirm: onConfirm,
        onCancel: onCancel,
      ),
      isDismissible: false,
    );
  }

  static void openUrl(String url) {
    canLaunchUrl(Uri.parse(url)).then((value) {
      if (value) {
        launchUrl(
          Uri.parse(url),
          mode: LaunchMode.externalApplication,
        );
      }
    });
  }

  static Future<String?> getDeviceId() async {
    // DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    // if (Platform.isAndroid) {
    //   AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    //   return androidInfo.id;
    // } else {
    //   IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
    //   return iosInfo.identifierForVendor;
    // }
    String identifier = "";
    try {
      identifier = await UniqueIdentifier.serial ?? "";
    } catch (_) {}
    return identifier;
  }

  // unfocus nodes
  static void unfocusNodes() {
    Get.focusScope?.unfocus();
  }

  // get onesignal app id from remote config
  static String getOneSignalAppId() {
    FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;
    return remoteConfig.getString("onesignal_app_id");
  }

  // get onesignal app id from remote config
  static void upgradeAlert(int forceUpdate) {
    // Get.put(AboutAppController());

    Get.defaultDialog(
        title: "".tr,
        titlePadding: const EdgeInsets.all(0),
        titleStyle: const TextStyle(fontSize: 16),
        contentPadding: const EdgeInsets.symmetric(horizontal: 8),
        barrierDismissible: false,
        onWillPop: () async {
          return false;
        },

        // textConfirm: "upgraderAlertaction".tr,

        content: Column(
          children: [
            Padding(
              padding:
                  const EdgeInsets.only(bottom: 8, left: 8, right: 8, top: 0),
              child: Text(
                "upgraderAlertbody".trParams({
                  "app_name": AboutAppController
                          .instance.configurations.value?.appName ??
                      "",
                  "app_version": AboutAppController.instance.appVersion.value
                }),
                style: const TextStyle(
                    color: Colors.black, fontSize: 16, height: 1.7),
                textAlign: TextAlign.center,
              ),
            ),
            const Divider(),
            InkWell(
              focusColor: Colors.transparent,
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              onTap: () {
                var appUrl = Platform.isAndroid
                    ? AboutAppController
                            .instance.configurations.value?.shareAndroid ??
                        ""
                    : AboutAppController
                            .instance.configurations.value?.shareIos ??
                        "";
                CommonFunctions.openUrl(appUrl);
              },
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: InkWell(
                    child: Text(
                  "upgraderAlertaction".tr,
                  style: TextStyle(
                      color: Get.theme.primaryColor,
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                )),
              ),
            ),
            if (forceUpdate == 2)
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Divider(),
                  InkWell(
                    focusColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onTap: () {
                      Get.back();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: InkWell(
                          child: Text(
                        "later".tr,
                        style: TextStyle(
                            color: Get.theme.primaryColor,
                            fontSize: 16,
                            fontWeight: FontWeight.bold),
                      )),
                    ),
                  )
                ],
              ),
          ],
        ));
  }

  // show login confirm bottom sheet
  static void showLoginConfirmBottomSheet() {
    Get.bottomSheet(
      ConfirmBottomSheet(
        title: "login".tr,
        message: "loginBottomSheetMessage".tr,
        confirmText: "loginConfirmText".tr,
        cancelText: "loginCancelText".tr,
        onConfirm: () {
          Get.back();
          Get.toNamed(Routes.LOGIN_PAGE);
        },
      ),
      isDismissible: false,
    );
  }

  // show logo out confirm bottom sheet
  static void showLogoutConfirmBottomSheet(Function onConfirm) {
    Get.bottomSheet(
      ConfirmBottomSheet(
        title: "logout".tr,
        message: "logoutBottomSheetMessage".tr,
        confirmText: "logoutConfirmText".tr,
        cancelText: "logoutCancelText".tr,
        onConfirm: () {
          Get.back();
          Get.toNamed(Routes.LOGIN_PAGE);
          onConfirm();
        },
      ),
      isDismissible: false,
    );
  }

  // show delete confirm bottom sheet
  static void showDeleteConfirmBottomSheet({
    String? title,
    String? message,
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
  }) {
    CommonFunctions.showConfirmBottomSheet(
      title: title ?? "deleteConfirmationTitle".tr,
      message: message ?? "deleteConfirmationMessage".tr,
      confirmText: "yes".tr,
      cancelText: "no".tr,
      onConfirm: () {
        Get.back();
        onConfirm();
      },
      onCancel: onCancel,
    );
  }

  static Widget getGreetingWidget() {
    int hour = DateTime.now().hour;

    if (hour > 6 && hour < 11) {
      return Text.rich(
        TextSpan(children: <InlineSpan>[
          TextSpan(
            text: "${"Good Morning".tr}، ",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          TextSpan(
            text:
                "${AuthService.instance.user.value?.name.sublistWord(0,1) ?? ""}  ☕",
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.normal,
              color: AppColors.thirdColor,
            ),
          )
        ]),
      );
    } else if (hour >= 11 && hour < 15) {
      return Text.rich(
        TextSpan(children: <InlineSpan>[
          TextSpan(
            text: "${"Good Afternoon".tr}، ",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          TextSpan(
            text:
                "${AuthService.instance.user.value?.name.sublistWord(0, 1) ?? ""} 🥘",
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.normal,
              color: AppColors.thirdColor,
            ),
          ),
        ]),
      );
    } else if (hour >= 15 && hour < 18) {
      return Text.rich(
        TextSpan(children: <InlineSpan>[
          TextSpan(
            text: "${"Good Evening".tr}، ",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          TextSpan(
            text:
                "${AuthService.instance.user.value?.name.sublistWord(0,1) ?? ""} 🍰",
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.normal,
              color: AppColors.thirdColor,
            ),
          ),
        ]),
      );
    } else if (hour >= 18 && hour < 22) {
      return Text.rich(
        TextSpan(children: <InlineSpan>[
          TextSpan(
            text: "${"Good Evening".tr}، ",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          TextSpan(
            text:
                "${AuthService.instance.user.value?.name.sublistWord(0,1) ?? ""} 🍔🍕",
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.normal,
              color: AppColors.thirdColor,
            ),
          ),
        ]),
      );
    }

AuthService.instance.user.value;
    return Text.rich(
      TextSpan(children: <InlineSpan>[
        TextSpan(
          text: "${"Welcome in".tr} ",
          style: TextStyle(fontSize: 13, fontWeight: FontWeight.bold),
        ),
        TextSpan(
          text: "appName".tr,
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.normal,
            color: AppColors.thirdColor,
          ),
        ),
      ]),
    );
  }
}
