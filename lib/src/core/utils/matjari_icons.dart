/// Flutter icons MatjariIcons
/// Copyright (C) 2022 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  MatjariIcons
///      fonts:
///       - asset: fonts/MatjariIcons.ttf
///
/// 
///
import 'package:flutter/widgets.dart';

class MatjariIcons {
  MatjariIcons._();

  static const _kFontFam = 'MatjariIcons';
  static const String? _kFontPkg = null;

  static const IconData canceled = IconData(0xe801, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData cart_add = IconData(0xe802, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData copy = IconData(0xe803, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData delivered = IconData(0xe804, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData down_arrow = IconData(0xe805, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData email = IconData(0xe806, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData facebook = IconData(0xe807, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData heart = IconData(0xe808, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData heart_fill = IconData(0xe809, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData home = IconData(0xe80a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData waiting = IconData(0xe80b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData instagram = IconData(0xe80c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData left_arrow = IconData(0xe80d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData logo = IconData(0xe80e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData logo_small = IconData(0xe80f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData logout = IconData(0xe810, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData minus = IconData(0xe812, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData mobile = IconData(0xe813, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData more = IconData(0xe814, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData on_the_way = IconData(0xe815, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData orders = IconData(0xe816, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData phone = IconData(0xe817, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData plus = IconData(0xe818, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData preparing = IconData(0xe819, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData privacy = IconData(0xe81a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData search = IconData(0xe81b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData share = IconData(0xe81c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData star = IconData(0xe81d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData terms = IconData(0xe81e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData tiktok = IconData(0xe81f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData trash = IconData(0xe820, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData twitter = IconData(0xe821, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData user = IconData(0xe822, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData website = IconData(0xe824, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData whatsapp = IconData(0xe825, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData youtube = IconData(0xe826, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData about = IconData(0xe827, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData marker = IconData(0xe828, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData approved = IconData(0xe829, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData back = IconData(0xe82a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData addresses = IconData(0xe82b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData cart = IconData(0xe82e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData indicator = IconData(0xe82f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
