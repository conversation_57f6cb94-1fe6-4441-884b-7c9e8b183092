// ignore_for_file: constant_identifier_names

part of './app_pages.dart';

abstract class Routes {
  // login
  static const LOGIN_PAGE = '/login';
  // home
  static const HOME_PAGE = '/home';
  // about
  static const ABOUT_PAGE = '/about';
  // about app
  static const ABOUT_APP_PAGE = '/about-app';
  // cart
  static const CART_PAGE = '/cart';
  // orders
  static const ORDERS_PAGE = '/orders';
  // addresses
  static const ADDRESSES_PAGE = '/addresses';
  // manage address
  static const MANAGE_ADDRESS_PAGE = '/manage-address';
  // order details
  static const ORDER_DETAILS_PAGE = '/order-details';
  // checkout
  static const CHECKOUT_PAGE = '/checkout';
  // category details
  static const CATEGORY_PAGE = '/category-details';
  // favorite
  static const FAVORITE_PAGE = '/favorite';
  // search
  static const SEARCH_PAGE = '/search';

  //
  // product page
  static const PRODUCT_PAGE = '/product';

  // splash page
  static const SPLASH_PAGE = '/splash';

  // publishing house page
  static const PUBLISHING_HOUSE_PAGE = '/publishing-house';

  //currency page
  static const CURRENCY_PAGE = '/currency';

  //map route page
  static const Map_route_page = '/map_route_page';
}
