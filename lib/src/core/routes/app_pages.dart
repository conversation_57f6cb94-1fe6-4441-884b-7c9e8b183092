import 'package:get/get.dart';
import 'package:matjari/src/bindings/order_binding.dart';
import 'package:matjari/src/bindings/order_details_binding.dart';
import 'package:matjari/src/view/pages/currency_page.dart';
import 'package:matjari/src/view/pages/map_route_page.dart';

import '../../bindings/about_app_binding.dart';
import '../../bindings/address_binding.dart';
import '../../bindings/cart_binding.dart';
import '../../bindings/category_binding.dart';
import '../../bindings/home_binding.dart';
import '../../bindings/manage_address_binding.dart';
import '../../bindings/publishing_house_binding.dart';
import '../../bindings/search_binding.dart';
import '../../view/pages/about_app_page.dart';
import '../../view/pages/about_us_page.dart';
import '../../view/pages/category_page.dart';
import '../../view/pages/favorites_page.dart';
import '../../view/pages/login_page.dart';
import '../../view/pages/manage_address_page.dart';
import '../../view/pages/addresses_page.dart';
import '../../view/pages/cart_page.dart';
import '../../view/pages/checkout_page.dart';
import '../../view/pages/home_page.dart';
import '../../view/pages/order_details_page.dart';
import '../../view/pages/orders_page.dart';
import '../../view/pages/product_page.dart';
import '../../view/pages/publishing_house_page.dart';
import '../../view/pages/search_page.dart';
import '../../view/pages/splash_page.dart';
part './app_routes.dart';

abstract class AppPages {
  AppPages._();
  static final pages = [
    // login
    GetPage(
      name: Routes.LOGIN_PAGE,
      page: () => const LoginPage(),
    ),
    // home
    GetPage(
      name: Routes.HOME_PAGE,
      binding: HomeBinding(),
      page: () => const HomePage(),
    ),
    // about
    GetPage(
      name: Routes.ABOUT_PAGE,
      page: () => const AboutUsPage(),
    ),
    // about app
    GetPage(
      name: Routes.ABOUT_APP_PAGE,
      binding: AboutAppBinding(),
      page: () => const AboutAppPage(),
    ),
    // cart
    GetPage(
      name: Routes.CART_PAGE,
      page: () => const CartPage(),
      binding: CartBinding(),
    ),
    // orders
    GetPage(
      name: Routes.ORDERS_PAGE,
      binding: OrderBinding(),
      page: () => OrdersPage(),
    ),
    // addresses
    GetPage(
      name: Routes.ADDRESSES_PAGE,
      binding: AddressBinding(),
      page: () => const AddressesPage(),
    ),
    // add address
    GetPage(
      name: Routes.MANAGE_ADDRESS_PAGE,
      binding: ManageAddressBinding(),
      page: () => const ManageAddressPage(),
    ),
    // order details
    GetPage(
        name: Routes.ORDER_DETAILS_PAGE,
        page: () => OrderDetailsPage(),
        binding: OrderDetailsBinding()),
    // checkout
    GetPage(
      name: Routes.CHECKOUT_PAGE,
      page: () => const CheckoutPage(),
    ),
    // category details
    GetPage(
      name: Routes.CATEGORY_PAGE,
      page: () => CategoryPage(),
      binding: CategoryBinding(),
    ),
    // favorite
    GetPage(
      name: Routes.FAVORITE_PAGE,
      page: () => const FavoritesPage(),
    ),
    // search
    GetPage(
      name: Routes.SEARCH_PAGE,
      page: () => const SearchPage(),
      binding: SearchBinding(),
    ),

    //
    // product page
    GetPage(
      name: Routes.PRODUCT_PAGE,
      page: () => ProductPage(),
    ),
    // splash page
    GetPage(
      name: Routes.SPLASH_PAGE,
      page: () => const SplashPage(),
    ),

    // publishing house page
    GetPage(
      name: Routes.PUBLISHING_HOUSE_PAGE,
      page: () => PublishingHousePage(),
      binding: PublishinghouseBinding(),
    ),

    //currency page
    GetPage(
      name: Routes.CURRENCY_PAGE,
      page: () => const CurrencyPage(),
    ),

    //map route  page
    GetPage(
      name: Routes.Map_route_page,
      page: () => const MapRoutePage(),
    ),
  ];
}
