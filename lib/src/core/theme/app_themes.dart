import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

import '../values/colors.dart';
import 'input_decoration_themes.dart';

final ThemeData appThemeData = ThemeData(
  primaryColor: AppColors.primaryColor,
  useMaterial3: false,
  textTheme: GoogleFonts.almaraiTextTheme(),
  scaffoldBackgroundColor: AppColors.backgroundColor,
  colorScheme: ColorScheme.fromSwatch().copyWith(
    primary: AppColors.primaryColor,
    secondary: AppColors.secondaryColor,
    onError: AppColors.redColor,
  ),
  bottomAppBarTheme: BottomAppBarTheme(color: Colors.transparent, elevation: 0),
  appBarTheme: AppBarTheme(
    systemOverlayStyle: SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.dark,
        statusBarColor: AppColors.appBarBackgroundColor),
    iconTheme: IconThemeData(color: AppColors.appBarIconColor),
    centerTitle: true,
    elevation: 0,
    backgroundColor: AppColors.appBarBackgroundColor,
    titleTextStyle: GoogleFonts.almarai(
      fontSize: 16,
      height: 1,
      fontWeight: FontWeight.w600,
      color: Colors.black,
    ),
  ),
  inputDecorationTheme: primaryInputDecorationTheme,
);
