import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../values/colors.dart';
import '../values/app_styles.dart';

InputDecorationTheme primaryInputDecorationTheme = InputDecorationTheme(
  hintStyle: AppStyles.formFieldHintTextStyle,
  prefixStyle: const TextStyle(
    color: Colors.white,
  ),
  suffixStyle: const TextStyle(
    color: Colors.white,
  ),
  hoverColor: AppColors.primaryColor,
  focusColor: AppColors.primaryColor,
  fillColor: AppColors.formFieldBackgroundColor,
  filled: true,
  errorMaxLines: 2,
  labelStyle: TextStyle(
    color: Get.theme.colorScheme.secondary,
    fontSize: 16,
  ),
  errorStyle: TextStyle(
    color: AppColors.redColor,
    fontSize: 12,
  ),
  prefixIconColor: AppColors.hintTextColor,
  suffixIconColor: AppColors.hintTextColor,
  contentPadding: const EdgeInsets.symmetric(
    vertical: 12,
    horizontal: 16,
  ),
  border: OutlineInputBorder(
    borderSide: BorderSide(
      width: 1,
      color: Colors.grey.shade500,
    ),
    borderRadius: AppStyles.cardBorderRadius,
  ),
  focusedBorder: OutlineInputBorder(
    borderSide: BorderSide(
      width: 1,
      color: AppColors.primaryColor,
    ),
    borderRadius: AppStyles.cardBorderRadius,
  ),
  errorBorder: OutlineInputBorder(
    borderSide: BorderSide(
      width: 0.3,
      color: AppColors.redColor,
    ),
    borderRadius: AppStyles.cardBorderRadius,
  ),
  enabledBorder: OutlineInputBorder(
    borderSide: BorderSide(
      width: 1,
      color: AppColors.formFieldBackgroundColor,
    ),
    borderRadius: AppStyles.cardBorderRadius,
  ),
  alignLabelWithHint: true,
);
InputDecorationTheme secondaryInputDecorationTheme = InputDecorationTheme(
  hintStyle: TextStyle(
    color: Colors.grey.shade500,
    fontSize: 12,
  ),
  hoverColor: AppColors.primaryColor,
  focusColor: AppColors.primaryColor,
  filled: true,
  contentPadding: const EdgeInsets.symmetric(
    vertical: 16,
    horizontal: 16,
  ),
  border: OutlineInputBorder(
    borderSide: BorderSide(
      width: 0.1,
      color: Colors.grey.shade500,
    ),
    borderRadius: AppStyles.cardBorderRadius,
  ),
  focusedBorder: OutlineInputBorder(
    borderSide: BorderSide(
      width: 0.1,
      color: AppColors.primaryColor,
    ),
    borderRadius: AppStyles.cardBorderRadius,
  ),
  errorBorder: OutlineInputBorder(
    borderSide: BorderSide(
      width: 0.5,
      color: AppColors.redColor,
    ),
    borderRadius: AppStyles.cardBorderRadius,
  ),
  enabledBorder: OutlineInputBorder(
    borderSide: BorderSide(
      width: 0.1,
      color: Colors.grey.shade500,
    ),
    borderRadius: AppStyles.cardBorderRadius,
  ),
  alignLabelWithHint: true,
);
