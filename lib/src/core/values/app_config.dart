import 'package:dio/dio.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:get/get.dart' hide FormData;
import 'package:matjari/src/data/providers/api/api_provider.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';
import 'package:matjari/src/data/services/auth_services.dart';

import '../../data/dio_interceptors/custom_retry_interceptors.dart';

class AppConfigs {
  static String get apiUrl =>
      FirebaseRemoteConfig.instance.getString('api_url');
  static String get oneSignalAppId =>
      FirebaseRemoteConfig.instance.getString('onesignal_app_id');
  static String get appKey {
    return FirebaseRemoteConfig.instance.getString('app_key');
  }

  static String get googleMapKey => "AIzaSyA-_OVKoFpOb1LybZ4BoyXZYKWUt3s678E";

  static int? get currencyId {
    return HiveProvider.getSelectedCurrency()?.currencyId;
  }

  static Map<String, dynamic> get queryParameters => {
        'language': Get.locale?.languageCode,
        'token': AuthService.instance.user.value?.token,
        'device_id': AuthService.instance.deviceId.value,
        'app_key': appKey,
        'currency_id': currencyId
      };

  static Dio get dioInstance {
    var dio = Dio(BaseOptions(
      baseUrl: apiUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      queryParameters: queryParameters,
    ));

    dio.interceptors.addAll([
      InterceptorsWrapper(
          //   onError: (response, handler) {
          //   if (response.response?.statusCode == 401) {
          //     // AuthService.instance.logout();
          //   }
          // },
          onRequest: (options, handler) {
        var _queryParameters = queryParameters;
        options.path
            .substring(options.path.indexOf("?") + 1)
            .split("&")
            .forEach((element) {
          var data = element.split("=");
          _queryParameters[data[0]] = data[1];
        });
        _queryParameters.addAll(options.queryParameters);

        if (options.method == 'POST' || options.method == 'PUT') {
          Map<String, dynamic> data = {};
          if (options.data is FormData) {
            data.addEntries((options.data as FormData).fields);
          } else if (options.data is Map) {
            data.addAll(options.data as Map<String, dynamic>);
          }
          options = options.copyWith(
              data: FormData.fromMap(data..addAll(_queryParameters)));

          print(data);
        }

        return handler.next(options); // Continue request
      }),
      CustomRetryInterceptor(
          dio: dio,
          retries: 2,
          retryableExtraStatuses: {401},
          customonError: (err) {
            if (err.response?.statusCode == 401) {
              err.requestOptions.queryParameters = queryParameters
                ..remove('token');

              AuthService.instance.user.value = null;
              HiveProvider.removeUserFromStorage();
            }
            return err;
          }),
    ]);

    return dio;
  }

  static Dio get authDioInstance {
    var dio = Dio(BaseOptions(
      baseUrl: apiUrl,
      connectTimeout: const Duration(seconds: 10),
      queryParameters: {
        'language': Get.locale?.languageCode,
        'device_id': AuthService.instance.deviceId.value,
        'app_key': appKey,
      },
    ));
    dio.interceptors.addAll([
      // Add an interceptor to modify request data
      InterceptorsWrapper(onRequest: (options, handler) {
        var _queryParameters = <String, dynamic>{};
        options.path
            .substring(options.path.indexOf("?") + 1)
            .split("&")
            .forEach((element) {
          var data = element.split("=");
          _queryParameters[data[0]] = data[1];
        });
        _queryParameters.addAll(options.queryParameters);

        if (options.method == 'POST' || options.method == 'PUT') {
          Map<String, dynamic> data = {};
          if (options.data is FormData) {
            data.addEntries((options.data as FormData).fields);
          } else if (options.data is Map) {
            data.addAll(options.data as Map<String, dynamic>);
          }
          options = options.copyWith(
              data: FormData.fromMap(data..addAll(_queryParameters)));

          print(data);
        }

        return handler.next(options);
      }), // Continue request
    ]);

    return dio;
  }

  static ApiProvider get apiProvider => ApiProvider(
        dioInstance,
      );
  static List<String> get supportedLanguages => [
        'ar',
        'en',
      ];
}
