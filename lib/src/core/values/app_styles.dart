import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'colors.dart';

class AppStyles {
  AppStyles._();
  // border Radius
  static BorderRadius sliderBorderRadius = BorderRadius.circular(32);
  static BorderRadius cardBorderRadius = BorderRadius.circular(12);
  static BorderRadius buttonBorderRadius = BorderRadius.circular(32);
  static BorderRadius categoryTabBarBorderRadius = BorderRadius.circular(16);
  // padding
  static EdgeInsets authPagesPadding = const EdgeInsets.all(16);
  static EdgeInsets pagesPadding = const EdgeInsets.all(16);
  static EdgeInsets buttonPadding = const EdgeInsets.symmetric(
    horizontal: 32,
    vertical: 16,
  );
  // decorations
  static BoxDecoration productCardBoxdecoration = BoxDecoration(
    color: Colors.white,
    borderRadius: AppStyles.cardBorderRadius,
    boxShadow: [
      BoxShadow(
        color: Colors.grey.shade200,
        blurRadius: 5,
        spreadRadius: 1,
      ),
    ],
  );
  // font sizes
  static double titleFontSize = 24;
  static double subTitleFontSize = 16;
  static double bodyFontSize = 14;
  static double captionFontSize = 12;
  static double buttonFontSize = 14;
  static double smallButtonFontSize = 12;
  // text styles
  // form field hint text style
  static TextStyle formFieldHintTextStyle = GoogleFonts.almarai(
    color: AppColors.hintTextColor,
    fontSize: 14,
    height: 1,
  );
}
