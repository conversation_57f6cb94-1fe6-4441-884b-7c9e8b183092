class Assets {
  Assets._();
  static const assetBasePath = "assets";
  // image path
  static const imagePath = "$assetBasePath/images";
  // flags path
  static const flagPath = "$imagePath/flags";
  // vector path
  static const vectorsPath = "$assetBasePath/vectors";
  // lottie path
  static const lottiePath = "$assetBasePath/lotties";

  // image assets
  // png marker
  static const markerPng = "$imagePath/marker.png";

  // car marker
  static const carMarker = "$imagePath/car.png";
  //motocycle marker
  static const motocycleMarker = "$imagePath/motocycle.png";
// get flag
  static String getFlag(String countryCode) => "$flagPath/$countryCode.png";
  // svg assets
  // logo
  static const String logo = "$vectorsPath/logo.svg";
  //logo horizontal
  static const String logoHorizontal = "$vectorsPath/logo_hor.svg";
  // platform
  static const String platform = "$vectorsPath/platform.svg";
  // logo icon
  static const String logoIcon = "$vectorsPath/logo_icon.svg";
  // marker
  static const String marker = "$vectorsPath/marker.svg";
  // default
  static const String defaultImage = "$vectorsPath/default.svg";
  // no content
  static const String noContent = "$vectorsPath/no_content.svg";
  // no address
  static const String noAddress = "$vectorsPath/no_address.svg";

  static const String noData = "$vectorsPath/no_data.svg";
  // no connection
  static const String noConnection = "$vectorsPath/no_connection.svg";
  // no results
  static const String noResult = "$vectorsPath/no_results.svg";
  // empty cart
  static const String emptyCart = "$vectorsPath/empty_cart.svg";
  //empty favorites
  static const String emptyFavorites = "$vectorsPath/empty_favorites.svg";
  // add address
  static const String addAddress = "$vectorsPath/add_address.svg";
  // loading error
  static const String loadingError = "$vectorsPath/loading_error.svg";
  // apple
  static const String apple = "$vectorsPath/apple.svg";
  // google
  static const String google = "$vectorsPath/google.svg";
  // facebook
  static const String facebook = "$vectorsPath/facebook.svg";
  // time
  static const String time = "$vectorsPath/time.svg";
  // payment
  static const String payment = "$vectorsPath/payment.svg";
  // notes
  static const String notes = "$vectorsPath/notes.svg";
  // alert
  static const String alert = "$vectorsPath/alert.svg";
  //check
  static const String check = "$vectorsPath/check.svg";

  // lottie assets
  // loading

  // qirtas
  static const String qirtas = "$vectorsPath/qirtas.svg";
  // filter
  static const String filter = "$vectorsPath/filter.svg";

  // custon light
  static const String customLight = "$vectorsPath/custom_light.svg";

  //currency
  static const String currency = "$vectorsPath/currency.svg";
  static const String lightbulb = "$vectorsPath/lightbulb.svg";

  //lotties
  static const String castle = "$lottiePath/castle.json";

  //
  static const String user = "$vectorsPath/user.svg";

// docment
  static const String document = "$vectorsPath/document.svg";

  // refresh
  static const String refreshIcon = "$vectorsPath/refresh.svg";

  // order status
  static const String init = "$vectorsPath/order_status/init.svg";
  static const String preparing = "$vectorsPath/order_status/preparing.svg";
  static const String onTheWay = "$vectorsPath/order_status/on_the_way.svg";
  static const String delivered = "$vectorsPath/order_status/delivered.svg";
  static const String canceled = "$vectorsPath/order_status/canceled.svg";

  //order status lotties
  static const String preparingLottie =
      "$lottiePath/order_status/preparing.json";
  static const String onTheWayLottie =
      "$lottiePath/order_status/on_the_way.json";
  static const String deliveredLottie =
      "$lottiePath/order_status/delivered.json";
  static const String initLottie = "$lottiePath/order_status/init.json";

  //icons

  static const String accountHeader = "$vectorsPath/account-header.svg";

  static const String accountLogo = "$vectorsPath/account-logo.png";

  static const String info = "$vectorsPath/info.svg";

  static const String back = "$vectorsPath/back.svg";
}
