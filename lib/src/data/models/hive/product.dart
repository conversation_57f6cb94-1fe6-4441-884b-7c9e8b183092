import 'package:hive/hive.dart';

part 'product.g.dart';

@HiveType(typeId: 1)
class Product {
  @HiveField(0)
  int id;
  @HiveField(1)
  String name;
  @HiveField(2)
  double price;
  @HiveField(3)
  String image;
  @HiveField(4)
  String imageThumb;
  @HiveField(5)
  int? sectionId;
  @HiveField(6)
  String? sectionName;
  @HiveField(7)
  String? publisherName;
  @HiveField(8)
  int? publisherId;
  @HiveField(9)
  String? description;
  @HiveField(10)
  String? writer;
  @HiveField(11)
  String? itemPriceCurrencyVar;
  @HiveField(12)
  String? itemCurrencyVar;

  // constructor
  Product(
      {required this.id,
      required this.name,
      required this.price,
      required this.image,
      required this.imageThumb,
      this.sectionId,
      this.sectionName,
      this.publisherId,
      this.publisherName,
      this.description,
      this.writer,
      this.itemCurrencyVar,
      this.itemPriceCurrencyVar
      });
  // from json
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['item_id'] as int,
      name: json['item_name'] as String,
      price: double.parse(json['item_price'].toString()),
      image: json['item_image'] as String,
      imageThumb: json['item_image_thumb'] as String,
      sectionId: json['item_section_id'] as int,
      sectionName: json['item_section_name'] as String,
      publisherName: json['item_publisher_name'] as String?,
      publisherId: json['item_publisher_id'] as int?,
      description: json['item_description'] as String?,
      writer: json['item_writer'] as String?,
      itemCurrencyVar: json['item_CurrencyVar'] as String?,
      itemPriceCurrencyVar: json['item_price_CurrencyVar'] as String?
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'item_id': id,
      'item_name': name,
      'item_price': price,
      'item_image': image,
      'item_image_thumb': imageThumb,
      'item_section_id': sectionId,
      'item_section_name': sectionName,
      'item_publisher_name': publisherName,
      'item_publisher_id': publisherId,
      'item_description': description,
      'item_writer': writer,
      'item_price_CurrencyVar': itemPriceCurrencyVar,
      'item_CurrencyVar': itemCurrencyVar,
    };
  }
}
