import 'dart:math';

import 'package:hive/hive.dart';

import 'product.dart';

part 'cart_item.g.dart';

@HiveType(typeId: 0)
class CartItem {
  @HiveField(0)
  Product product;
  @HiveField(1)
  int quantity;

  double? totlePrice;

  
  double get total {
    return ((product.price * quantity) * pow(10, 3)).round() / pow(10, 3);
  }

  // constructor
  CartItem({required this.product, required this.quantity, this.totlePrice});
  // from json
  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      product: Product.fromJson(json['product']),
      quantity: json['quantity'],
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'product': product.toJson(),
      'quantity': quantity,
    };
  }
}
