// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProductAdapter extends TypeAdapter<Product> {
  @override
  final int typeId = 1;

  @override
  Product read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Product(
      id: fields[0] as int,
      name: fields[1] as String,
      price: fields[2] as double,
      image: fields[3] as String,
      imageThumb: fields[4] as String,
      sectionId: fields[5] as int?,
      sectionName: fields[6] as String?,
      publisherId: fields[8] as int?,
      publisherName: fields[7] as String?,
      description: fields[9] as String?,
      writer: fields[10] as String?,
      itemCurrencyVar: fields[12] as String?,
      itemPriceCurrencyVar: fields[11] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Product obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.price)
      ..writeByte(3)
      ..write(obj.image)
      ..writeByte(4)
      ..write(obj.imageThumb)
      ..writeByte(5)
      ..write(obj.sectionId)
      ..writeByte(6)
      ..write(obj.sectionName)
      ..writeByte(7)
      ..write(obj.publisherName)
      ..writeByte(8)
      ..write(obj.publisherId)
      ..writeByte(9)
      ..write(obj.description)
      ..writeByte(10)
      ..write(obj.writer)
      ..writeByte(11)
      ..write(obj.itemPriceCurrencyVar)
      ..writeByte(12)
      ..write(obj.itemCurrencyVar);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
