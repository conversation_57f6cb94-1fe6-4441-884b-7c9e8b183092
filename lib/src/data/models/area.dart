class Area {
  Area({
    required this.areaId,
    required this.areaName,
  });

  int areaId;
  String areaName;
  // from json
  factory Area.fromJson(Map<String, dynamic> json) {
    return Area(
      areaId: json['zones_id'] as int,
      areaName: json['zones_name'] as String,
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'zones_id': areaId,
      'zones_name': areaName,
    };
  }
}
