import 'order_product.dart';

class Order {
  Order({
    required this.orderId,
    required this.orderClientId,
    required this.orderClientName,
    required this.orderClientMobile,
    required this.orderClientWhatsapp,
    required this.orderDriverId,
    required this.orderDriverName,
    required this.orderDriverMobile,
    required this.orderDriverWhatsapp,
    required this.orderCountryId,
    required this.orderCountryName,
    required this.orderCountryCode,
    required this.orderAreaId,
    required this.orderAreaName,
    required this.orderCityId,
    required this.orderCityName,
    required this.orderAddressId,
    required this.orderAddress,
    required this.orderLatitude,
    required this.orderLongitude,
    required this.orderPaymentId,
    required this.orderPaymentName,
    required this.orderPaymentType,
    required this.orderPaymentTypeLabel,
    required this.orderPaymentDescription,
    required this.orderPaymentAccountNumber,
    required this.orderPaymentTransferNumber,
    required this.orderPaymentTransferCompany,
    required this.orderPaymentStatus,
    required this.orderStatus,
    required this.orderStatusString,
    required this.orderStatusUpdateDate,
    required this.orderDeliveryType,
    required this.orderDeliveryTimeId,
    required this.orderDeliveryTime,
    required this.orderDate,
    required this.orderNotes,
    required this.orderTotalPrice,
    required this.orderFullTotalPrice,
    required this.orderDeliveryFees,
    required this.products,
    required this.orderCurrencyVar,
    required this.orderCouponDiscountAmount,
    required this.orderCouponDiscountAmountCurrencyVar,
    required this.orderClientImage,
    required this.orderDriverImage,
    required this.orderMemberAddresse,
    required this.orderAddressTypeId,
    required this.orderAddressTypeName,
    required this.orderPaymentIcon,
    required this.orderStatusUpdateDateTime,
    required this.orderDeliveryTimeLabel,
    required this.orderDeliveryDateDay,
    required this.orderDeliveryFromHourTime,
    required this.orderDeliveryToHourTime,
    required this.orderDeliveryFeesCurrencyVar,
    required this.orderCurrencyId,
    required this.orderDeliveryFeesCurrencyId,
    required this.orderCurrencyName,
    required this.orderDeliveryFeesCurrencyName,
    required this.orderCouponType,
    required this.orderCouponTypeLabel,
    required this.orderCouponDiscountDeliveryFees,
    required this.orderCouponDiscountDeliveryFeesCurrencyVar,
    required this.orderTotalPriceCurrencyVar,
    required this.orderFullTotalPriceCurrencyVar,
  });

  int orderId;
  int orderClientId;
  String orderClientName;
  String orderClientMobile;
  String orderClientWhatsapp;
  int orderDriverId;
  String orderDriverName;
  String orderDriverMobile;
  String orderDriverWhatsapp;
  String orderCountryId;
  String orderCountryName;
  String orderCountryCode;
  String orderAreaId;
  String orderAreaName;
  String orderCityId;
  String orderCityName;
  int orderAddressId;
  String orderAddress;
  double orderLatitude;
  double orderLongitude;
  int orderPaymentId;
  String orderPaymentName;
  int orderPaymentType;
  String orderPaymentTypeLabel;
  String orderPaymentDescription;
  String orderPaymentAccountNumber;
  String orderPaymentTransferNumber;
  String orderPaymentTransferCompany;
  int orderPaymentStatus;
  int orderStatus;
  String orderStatusString;
  String orderStatusUpdateDate;
  int orderDeliveryType;
  int orderDeliveryTimeId;
  String orderDeliveryTime;
  String orderDate;
  String orderNotes;
  double orderTotalPrice;
  double orderFullTotalPrice;
  double orderDeliveryFees;
  List<OrderProduct> products;

  double? orderCouponDiscountAmount;
  String? orderCouponDiscountAmountCurrencyVar;

  String? orderCurrencyVar;

  // Missing fields from JSON
  String orderClientImage;
  String orderDriverImage;
  String orderMemberAddresse;
  int orderAddressTypeId;
  String orderAddressTypeName;
  String orderPaymentIcon;
  String orderStatusUpdateDateTime;
  String orderDeliveryTimeLabel;
  String orderDeliveryDateDay;
  String orderDeliveryFromHourTime;
  String orderDeliveryToHourTime;
  String orderDeliveryFeesCurrencyVar;
  int orderCurrencyId;
  int orderDeliveryFeesCurrencyId;
  String orderCurrencyName;
  String orderDeliveryFeesCurrencyName;
  int orderCouponType;
  String orderCouponTypeLabel;
  double orderCouponDiscountDeliveryFees;
  String orderCouponDiscountDeliveryFeesCurrencyVar;
  String orderTotalPriceCurrencyVar;
  String orderFullTotalPriceCurrencyVar;
  // from json
  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      orderId: json['order_id'] as int,
      orderClientId: json['order_client_id'] as int,
      orderClientName: json['order_client_name'] as String,
      orderClientMobile: json['order_client_mobile'] as String,
      orderClientWhatsapp: json['order_client_whatsapp'] as String,
      orderDriverId: json['order_driver_id'] as int,
      orderDriverName: json['order_driver_name'] as String,
      orderDriverMobile: json['order_driver_mobile'] as String,
      orderDriverWhatsapp: json['order_driver_whatsapp'] as String,
      orderCountryId: json['order_country_id'] as String,
      orderCountryName: json['order_country_name'] as String,
      orderCountryCode: json['order_country_code'] as String,
      orderAreaId: json['order_area_id'] as String,
      orderAreaName: json['order_area_name'] as String,
      orderCityId: json['order_city_id'] as String,
      orderCityName: json['order_city_name'] as String,
      orderAddressId: json['order_address_id'] as int,
      orderAddress: json['order_address'] as String,
      orderLatitude: double.parse(json['order_latitude'].toString()),
      orderLongitude: double.parse(json['order_longitude'].toString()),
      orderPaymentId: json['order_payment_id'] as int,
      orderPaymentName: json['order_payment_name'] as String,
      orderPaymentType: json['order_payment_type'] as int,
      orderPaymentTypeLabel: json['order_payment_type_label'] as String,
      orderPaymentDescription: json['order_payment_description'] as String,
      orderPaymentAccountNumber: json['order_payment_account_number'] as String,
      orderPaymentTransferNumber:
          json['order_payment_transfer_number'] as String,
      orderPaymentTransferCompany:
          json['order_payment_transfer_company'] as String,
      orderPaymentStatus: json['order_payment_status'] as int,
      orderStatus: json['order_status'] as int,
      orderStatusString: json['order_status_string'] as String,
      orderStatusUpdateDate: json['order_status_update_date'] as String,
      orderDeliveryType: json['order_delivery_type'] as int,
      orderDeliveryTimeId: json['order_delivery_time_id'] as int,
      orderDeliveryTime: json['order_delivery_time'] as String,
      orderDate: json['order_date'] as String,
      orderNotes: json['order_notes'] as String,
      orderTotalPrice: double.parse(json['order_total_price'].toString()),
      orderFullTotalPrice:
          double.parse(json['order_full_total_price'].toString()),
      orderDeliveryFees: double.parse(json['order_delivery_fees'].toString()),
      products: (json['order_items'] as List)
          .map((e) => OrderProduct.fromJson(e as Map<String, dynamic>))
          .toList(),
      orderCurrencyVar: json['order_CurrencyVar'] as String?,
      orderCouponDiscountAmount: double.tryParse(
          json['order_coupon_discount_amount']?.toString() ?? ""),
      orderCouponDiscountAmountCurrencyVar:
          json['order_coupon_discount_amount_CurrencyVar']?.toString(),
      orderClientImage: json['order_client_image'] as String,
      orderDriverImage: json['order_driver_image'] as String,
      orderMemberAddresse: json['order_member_addresse'] as String,
      orderAddressTypeId: json['order_address_type_id'] as int,
      orderAddressTypeName: json['order_address_type_name'] as String,
      orderPaymentIcon: json['order_payment_icon'] as String,
      orderStatusUpdateDateTime: json['order_status_update_date_time'] as String,
      orderDeliveryTimeLabel: json['order_delivery_time_label'] as String,
      orderDeliveryDateDay: json['order_delivery_date_day'] as String,
      orderDeliveryFromHourTime: json['order_delivery_from_hour_time'] as String,
      orderDeliveryToHourTime: json['order_delivery_to_hour_time'] as String,
      orderDeliveryFeesCurrencyVar: json['order_delivery_fees_CurrencyVar'] as String,
      orderCurrencyId: json['order_currency_id'] as int,
      orderDeliveryFeesCurrencyId: json['order_DeliveryFeesCurrencyId'] as int,
      orderCurrencyName: json['order_CurrencyName'] as String,
      orderDeliveryFeesCurrencyName: json['order_DeliveryFeesCurrencyName'] as String,
      orderCouponType: json['order_coupon_type'] as int,
      orderCouponTypeLabel: json['order_coupon_type_label'] as String,
      orderCouponDiscountDeliveryFees: double.parse(json['order_coupon_discount_delivery_fees'].toString()),
      orderCouponDiscountDeliveryFeesCurrencyVar: json['order_coupon_discount_delivery_fees_CurrencyVar'] as String,
      orderTotalPriceCurrencyVar: json['order_total_price_CurrencyVar'] as String,
      orderFullTotalPriceCurrencyVar: json['order_full_total_price_CurrencyVar'] as String,
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'order_id': orderId,
      'order_client_id': orderClientId,
      'order_client_name': orderClientName,
      'order_client_mobile': orderClientMobile,
      'order_client_whatsapp': orderClientWhatsapp,
      'order_driver_id': orderDriverId,
      'order_driver_name': orderDriverName,
      'order_driver_mobile': orderDriverMobile,
      'order_driver_whatsapp': orderDriverWhatsapp,
      'order_country_id': orderCountryId,
      'order_country_name': orderCountryName,
      'order_country_code': orderCountryCode,
      'order_area_id': orderAreaId,
      'order_area_name': orderAreaName,
      'order_city_id': orderCityId,
      'order_city_name': orderCityName,
      'order_address_id': orderAddressId,
      'order_address': orderAddress,
      'order_latitude': orderLatitude,
      'order_longitude': orderLongitude,
      'order_payment_id': orderPaymentId,
      'order_payment_name': orderPaymentName,
      'order_payment_type': orderPaymentType,
      'order_payment_type_label': orderPaymentTypeLabel,
      'order_payment_description': orderPaymentDescription,
      'order_payment_account_number': orderPaymentAccountNumber,
      'order_payment_transfer_number': orderPaymentTransferNumber,
      'order_payment_transfer_company': orderPaymentTransferCompany,
      'order_payment_status': orderPaymentStatus,
      'order_status': orderStatus,
      'order_status_string': orderStatusString,
      'order_status_update_date': orderStatusUpdateDate,
      'order_delivery_type': orderDeliveryType,
      'order_delivery_time_id': orderDeliveryTimeId,
      'order_delivery_time': orderDeliveryTime,
      'order_date': orderDate,
      'order_notes': orderNotes,
      'order_total_price': orderTotalPrice,
      'order_full_total_price': orderFullTotalPrice,
      'order_delivery_fees': orderDeliveryFees,
      'order_items': products.map((e) => e.toJson()).toList(),
      'order_CurrencyVar': orderCurrencyVar,
      'order_coupon_discount_amount': orderCouponDiscountAmount,
      'order_coupon_discount_amount_CurrencyVar': orderCouponDiscountAmountCurrencyVar,
      'order_client_image': orderClientImage,
      'order_driver_image': orderDriverImage,
      'order_member_addresse': orderMemberAddresse,
      'order_address_type_id': orderAddressTypeId,
      'order_address_type_name': orderAddressTypeName,
      'order_payment_icon': orderPaymentIcon,
      'order_status_update_date_time': orderStatusUpdateDateTime,
      'order_delivery_time_label': orderDeliveryTimeLabel,
      'order_delivery_date_day': orderDeliveryDateDay,
      'order_delivery_from_hour_time': orderDeliveryFromHourTime,
      'order_delivery_to_hour_time': orderDeliveryToHourTime,
      'order_delivery_fees_CurrencyVar': orderDeliveryFeesCurrencyVar,
      'order_currency_id': orderCurrencyId,
      'order_DeliveryFeesCurrencyId': orderDeliveryFeesCurrencyId,
      'order_CurrencyName': orderCurrencyName,
      'order_DeliveryFeesCurrencyName': orderDeliveryFeesCurrencyName,
      'order_coupon_type': orderCouponType,
      'order_coupon_type_label': orderCouponTypeLabel,
      'order_coupon_discount_delivery_fees': orderCouponDiscountDeliveryFees,
      'order_coupon_discount_delivery_fees_CurrencyVar': orderCouponDiscountDeliveryFeesCurrencyVar,
      'order_total_price_CurrencyVar': orderTotalPriceCurrencyVar,
      'order_full_total_price_CurrencyVar': orderFullTotalPriceCurrencyVar,
    };
  }
}
