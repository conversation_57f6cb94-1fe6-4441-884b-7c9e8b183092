class Ad {
  Ad(
      {required this.adId,
      required this.adTitle,
      required this.adUrl,
      required this.adImage,
      required this.adType,
      this.adProductId,
      this.adPublisherId});

  int adId;
  String adTitle;
  String adUrl;
  String adImage;
  int adType;
  int? adProductId;
  int? adPublisherId;
  // from json
  factory Ad.fromJson(Map<String, dynamic> json) {
    return Ad(
      adId: json['ad_id'] as int,
      adTitle: json['ad_title'] as String,
      adUrl: json['ad_url'] as String,
      adImage: json['ad_image'] as String,
      adType: json['ad_type'] as int,
      adProductId: int.tryParse(json['ad_product_id']?.toString() ?? ""),
      adPublisherId: int.tryParse(json['ad_publisher_id']?.toString() ?? ""),
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'ad_id': adId,
      'ad_title': adTitle,
      'ad_url': adUrl,
      'ad_image': adImage,
    };
  }
}
