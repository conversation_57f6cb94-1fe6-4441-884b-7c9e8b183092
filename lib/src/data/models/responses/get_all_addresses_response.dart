import '../address.dart';

class GetAllAddressesResponse {
  int status;
  List<Address>? addresses;
  String? message;
  // constructor
  GetAllAddressesResponse({
    required this.status,
    this.addresses,
    this.message,
  });
  // from json
  factory GetAllAddressesResponse.fromJson(Map<String, dynamic> json) {
    return GetAllAddressesResponse(
      status: json['status'] as int,
      addresses: json['result'] == null
          ? null
          : (json['result'] as List<dynamic>)
              .map((e) => Address.fromJson(e as Map<String, dynamic>))
              .toList(),
      message: json['message'] == null ? null : json['message'] as String,
    );
  }
}
