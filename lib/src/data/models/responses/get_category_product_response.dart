import '../hive/product.dart';

class GetCategoryProductResponse {
  GetCategoryProductResponse({
    required this.status,
    required this.result,
  });

  int status;
  GetCategoryProductResult result;
  // from json
  factory GetCategoryProductResponse.fromJson(Map<String, dynamic> json) {
    return GetCategoryProductResponse(
        status: json['status'] as int,
        result: GetCategoryProductResult.fromJson(
            json['result'] as Map<String, dynamic>));
  }
}

class GetCategoryProductResult {
  GetCategoryProductResult({
    required this.products,
    required this.currentPage,
    required this.maxPage,
  });

  List<Product> products;
  int currentPage;
  int maxPage;
  // from json
  factory GetCategoryProductResult.fromJson(Map<String, dynamic> json) {
    return GetCategoryProductResult(
      products: (json['items'] as List<dynamic>)
          .map((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
      currentPage: json.containsKey("current_page") ? json['current_page'] : 1,
      maxPage: json.containsKey("max_page") ? json['max_page'] : 1,
    );
  }
}
