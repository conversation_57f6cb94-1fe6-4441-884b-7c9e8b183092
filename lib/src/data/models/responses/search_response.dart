import '../hive/product.dart';

class SearchResponse {
  SearchResponse({
    required this.status,
    required this.searchResult,
  });

  int status;
  SearchResult searchResult;
  // from json
  factory SearchResponse.fromJson(Map<String, dynamic> json) {
    return SearchResponse(
      status: json['status'] as int,
      searchResult: SearchResult.fromJson(json['result']),
    );
  }
}

class SearchResult {
  SearchResult({
    required this.products,
  });

  List<Product> products;
  // from json
  factory SearchResult.fromJson(Map<String, dynamic> json) {
    return SearchResult(
      products: (json['items'] as List<dynamic>)
          .map((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}
