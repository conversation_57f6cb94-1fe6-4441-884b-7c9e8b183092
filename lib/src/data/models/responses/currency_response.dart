import 'package:matjari/src/data/models/currency.dart';

class CurrencyResponse {
  CurrencyResponse({
    required this.status,
    required this.currencies,
  });

  int status;
  List<Currency> currencies;
  // from json
  factory CurrencyResponse.fromJson(Map<String, dynamic> json) {
    return CurrencyResponse(
      status: json['status'] as int,
      currencies: (json['result'] as List)
          .map((e) => Currency.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'result': currencies.map((e) => e.toJson()).toList(),
    };
  }
}
