

// {"status":0,"result":{"full_total_amount":53000,"full_total_amount_CurrencyVar":"53000 ريال","total_amount":53000,"total_amount_CurrencyVar":"53000 ريال","delivery_fees":1000,"delivery_fees_CurrencyVar":"1000 ريال","order_coupon_type":1,"order_coupon_type_label":"توصيل مجاني","order_coupon_discount_amount":0,"order_coupon_discount_amount_CurrencyVar":"0 ريال","order_coupon_discount_delivery_fees":1000,"order_coupon_discount_delivery_fees_CurrencyVar":"1000 ريال","TotalMinOrderPriceAllowUse":10000,"TotalMinOrderPriceAllowUse_CurrencyVar":"10000 ريال","discount":0,"discount_label":"0"}}
class ApplyCouponResponse {
  ApplyCouponResponse({
    required this.status,
    this.result,
    this.message,
  });

  int status;
  ApplyCouponResult? result;
  String? message;

  // from json
  factory ApplyCouponResponse.fromJson(Map<String, dynamic> json) {
    return ApplyCouponResponse(
      status: json['status'] as int,
      message: json['message'] as String?,
      result: json['result'] == null
          ? null
          : ApplyCouponResult.fromJson(json['result']),
    );
  }

  // to json
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'result': result?.toJson(),
    };
  }
}

class ApplyCouponResult {
  ApplyCouponResult({
    required this.fullTotalAmount,
    required this.fullTotalAmountCurrencyVar,
    required this.totalAmount,
    required this.totalAmountCurrencyVar,
    required this.deliveryFees,
    required this.deliveryFeesCurrencyVar,
    required this.orderCouponType,
    required this.orderCouponTypeLabel,
    required this.orderCouponDiscountAmount,
    required this.orderCouponDiscountAmountCurrencyVar,
    required this.orderCouponDiscountDeliveryFees,
    required this.orderCouponDiscountDeliveryFeesCurrencyVar,
    required this.totalMinOrderPriceAllowUse,
    required this.totalMinOrderPriceAllowUseCurrencyVar,
    required this.discount,
    required this.discountLabel,
  });

  double fullTotalAmount;
  String fullTotalAmountCurrencyVar;
  double totalAmount;
  String totalAmountCurrencyVar;
  double deliveryFees;
  String deliveryFeesCurrencyVar;
  int orderCouponType;
  String orderCouponTypeLabel;
  double orderCouponDiscountAmount;
  String orderCouponDiscountAmountCurrencyVar;
  double orderCouponDiscountDeliveryFees;
  String orderCouponDiscountDeliveryFeesCurrencyVar;
  double totalMinOrderPriceAllowUse;
  String totalMinOrderPriceAllowUseCurrencyVar;
  double discount;
  String discountLabel;
  // from json
  factory ApplyCouponResult.fromJson(Map<String, dynamic> json) {
    return ApplyCouponResult(
      fullTotalAmount: double.parse(json['full_total_amount'].toString()),
      fullTotalAmountCurrencyVar: json['full_total_amount_CurrencyVar'].toString(),
      totalAmount: double.parse(json['total_amount'].toString()),
      totalAmountCurrencyVar: json['total_amount_CurrencyVar'].toString(),
      deliveryFees: double.parse(json['delivery_fees'].toString()),
      deliveryFeesCurrencyVar: json['delivery_fees_CurrencyVar'].toString(),
      orderCouponType: int.parse(json['order_coupon_type'].toString()),
      orderCouponTypeLabel: json['order_coupon_type_label'].toString(),
      orderCouponDiscountAmount: double.parse(json['order_coupon_discount_amount'].toString()),
      orderCouponDiscountAmountCurrencyVar: json['order_coupon_discount_amount_CurrencyVar'].toString(),
      orderCouponDiscountDeliveryFees: double.parse(json['order_coupon_discount_delivery_fees'].toString()),
      orderCouponDiscountDeliveryFeesCurrencyVar: json['order_coupon_discount_delivery_fees_CurrencyVar'].toString(),
      totalMinOrderPriceAllowUse: double.parse(json['TotalMinOrderPriceAllowUse'].toString()),
      totalMinOrderPriceAllowUseCurrencyVar: json['TotalMinOrderPriceAllowUse_CurrencyVar'].toString(),
      discount: double.parse(json['discount'].toString()),
      discountLabel: json['discount_label'].toString(),
    );
  }

  // to json
  Map<String, dynamic> toJson() {
    return {
      'full_total_amount': fullTotalAmount,
      'full_total_amount_CurrencyVar': fullTotalAmountCurrencyVar,
      'total_amount': totalAmount,
      'total_amount_CurrencyVar': totalAmountCurrencyVar,
      'delivery_fees': deliveryFees,
      'delivery_fees_CurrencyVar': deliveryFeesCurrencyVar,
      'order_coupon_type': orderCouponType,
      'order_coupon_type_label': orderCouponTypeLabel,
      'order_coupon_discount_amount': orderCouponDiscountAmount,
      'order_coupon_discount_amount_CurrencyVar': orderCouponDiscountAmountCurrencyVar,
      'order_coupon_discount_delivery_fees': orderCouponDiscountDeliveryFees,
      'order_coupon_discount_delivery_fees_CurrencyVar': orderCouponDiscountDeliveryFeesCurrencyVar,
      'TotalMinOrderPriceAllowUse': totalMinOrderPriceAllowUse,
      'TotalMinOrderPriceAllowUse_CurrencyVar': totalMinOrderPriceAllowUseCurrencyVar,
      'discount': discount,
      'discount_label': discountLabel,
    };
  }
}
