import '../page_info.dart';

class AboutResponse {
  AboutResponse({
    required this.status,
    required this.pagesInfo,
  });

  int status;
  List<PageInfo> pagesInfo;
  // from json
  factory AboutResponse.fromJson(Map<String, dynamic> json) {
    return AboutResponse(
        status: json['status'] as int,
        pagesInfo: (json['result'] as List<dynamic>)
            .map((e) => PageInfo.fromJson(e as Map<String, dynamic>))
            .toList());
  }
}
