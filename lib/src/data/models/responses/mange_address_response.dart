
import '../address.dart';

class ManageAddressResponse {
  ManageAddressResponse({
    required this.status,
    required this.message,
    required this.address,
  });

  int status;
  String message;
  Address? address;
  // from json
  factory ManageAddressResponse.fromJson(Map<String, dynamic> json) {
    return ManageAddressResponse(
      status: json['status'] as int,
      message: json['message'] as String,
      address: json['result'] == null
          ? null
          : Address.fromJson(json['result'] as Map<String, dynamic>),
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'result': address?.toJson(),
    };
  }
}
