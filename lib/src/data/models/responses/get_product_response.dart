import '../hive/product.dart';

class GetProductResponse {
  GetProductResponse({
    required this.status,
    required this.result,
  });

  int status;
  GetProductResult result;
  // from json
  factory GetProductResponse.fromJson(Map<String, dynamic> json) {
     print(json);
    return GetProductResponse(
        status: json['status'] as int,
        result: GetProductResult.fromJson(
            json ));
  }
}

class GetProductResult {
  GetProductResult({
    required this.products,
    required this.currentPage,
    required this.maxPage,
  });

  List<Product> products;
  int currentPage;
  int maxPage;
  // from json
  factory GetProductResult.fromJson(Map<String, dynamic> json) {
   
    return GetProductResult(
      products: (json['result'] as List<dynamic>)
          .map((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
      currentPage: json.containsKey("current_page") ? json['current_page'] : 1,
      maxPage: json.containsKey("max_page") ? json['max_page'] : 1,
    );
  }
}
