import '../order.dart';

class OrderResponse {
  OrderResponse({
    required this.status,
    required this.orderResult,
    required this.message,
  });

  int status;
  OrderResult? orderResult;
  String message;
  // from json
  factory OrderResponse.fromJson(Map<String, dynamic> json) {
    return OrderResponse(
      status: json['status'] as int,
      orderResult:
          json['result'] == null ? null : OrderResult.fromJson(json['result']),
      message: json['message'] == null ? "" : json['message'] as String,
    );
  }
  // to json
  Map<String, dynamic> toJson() => {
        'status': status,
        'result': orderResult?.toJson(),
        'message': message,
      };
}

class OrderResult {
  OrderResult({
    required this.orders,
    required this.currentPage,
    required this.maxPage,
  });

  List<Order> orders;
  int currentPage;
  int maxPage;
  // from json
  // from json
  factory OrderResult.fromJson(Map<String, dynamic> json) {
    return OrderResult(
      orders: (json['list'] as List<dynamic>)
          .map((e) => Order.fromJson(e))
          .toList(),
      currentPage: !json.containsKey("current_page") ? 1 : json['current_page'],
      maxPage:!json.containsKey("max_page") ? 1 : json['max_page'],
    );
  }
  // to json
  Map<String, dynamic> toJson() => {
        'list': orders.map((e) => e.toJson()).toList(),
      };
}
