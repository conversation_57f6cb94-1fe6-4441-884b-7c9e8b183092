import '../area.dart';

class GetAllAreasResponse {
  GetAllAreasResponse({
    required this.status,
    this.areas,
    this.message,
  });

  int status;
  List<Area>? areas;
  String? message;
  // from json
  factory GetAllAreasResponse.fromJson(Map<String, dynamic> json) {
    return GetAllAreasResponse(
      status: json['status'] as int,
      message: json['message'] == null ? null : json['message'] as String,
      areas: json['result'] == null
          ? null
          : (json['result'] as List<dynamic>)
              .map((e) => Area.fromJson(e as Map<String, dynamic>))
              .toList(),
    );
  }
}
