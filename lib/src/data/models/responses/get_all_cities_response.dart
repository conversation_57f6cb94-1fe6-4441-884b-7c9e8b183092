import '../city.dart';

class GetAllCitiesResponse {
  GetAllCitiesResponse({
    required this.status,
    required this.cities,
  });

  int status;
  List<City> cities;
  // from json
  factory GetAllCitiesResponse.fromJson(Map<String, dynamic> json) {
    return GetAllCitiesResponse(
      status: json['status'] as int,
      cities: (json['result'] as List<dynamic>)
          .map((e) => City.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}
