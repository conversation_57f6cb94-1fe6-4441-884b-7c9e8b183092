import 'package:matjari/src/data/models/ads.dart';

import '../section.dart';

class HomePageResponse {
  HomePageResponse({
    required this.status,
    required this.result,
    required this.message,
  });

  int status;
  HomePageResponseResult? result;
  String? message;
  // from json
  factory HomePageResponse.fromJson(Map<String, dynamic> json) {
    return HomePageResponse(
      status: json['status'] as int,
      result: json['result'] == null
          ? null
          : HomePageResponseResult.fromJson(
              json['result'] as Map<String, dynamic>),
      message: json['message'] == null ? null : json['message'] as String,
    );
  }

  // to json
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'result': result?.toJson(),
    };
  }
}

class HomePageResponseResult {
  HomePageResponseResult({
    required this.latesUpdate,
    required this.sections,
    required this.ads,
    required this.slider,
    required this.minOrderPrice,
    // required this.currencyCode,
    required this.iosForceUpdae,
    required this.androidForceUpdae,
    required this.androidBuildNumber,
    required this.iosBuildNumber,
    required this.mainCurrencyCode,
  });

  String latesUpdate;
  List<Section> sections;
  List<Ad> ads;
  List<dynamic> slider;
  double minOrderPrice;
  // String currencyCode;
  int androidBuildNumber;
  int iosBuildNumber;
  int androidForceUpdae;
  int iosForceUpdae;
  String mainCurrencyCode;
  // from json
  factory HomePageResponseResult.fromJson(Map<String, dynamic> json) {
    return HomePageResponseResult(
        latesUpdate: json['latest_update'] as String? ?? "",
        sections: (json['sections'] as List<dynamic>)
            .map((e) => Section.fromJson(e as Map<String, dynamic>))
            .toList(),
        ads: (json['ads'] as List<dynamic>)
            .map((e) => Ad.fromJson(e as Map<String, dynamic>))
            .toList(),
        slider: json['slider'] as List<dynamic>,
        // currencyCode: json["currencie_main"]["currency_code"],
        minOrderPrice: double.parse(json["min_order_price"].toString()),
        androidBuildNumber:
            int.tryParse(json['android_version'].toString()) ?? 0,
        iosBuildNumber: int.tryParse(json['ios_version'].toString()) ?? 0,
        androidForceUpdae:
            int.tryParse(json['android_force_update'].toString()) ?? 0,
        iosForceUpdae: int.tryParse(json['ios_force_update'].toString()) ?? 0,
        mainCurrencyCode: json["currencie_main"]["currency_code"] ?? 'ريال');
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'lates_update': latesUpdate,
      'sections': sections.map((e) => e.toJson()).toList(),
      'ads': ads,
      'slider': slider,
    };
  }
}
