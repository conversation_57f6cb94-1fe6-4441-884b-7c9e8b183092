import '../delivery_time.dart';

class DeliveryTimeResponse {
  DeliveryTimeResponse({
    required this.status,
    required this.deliveryTimes,
  });

  int status;
  List<DeliveryTime> deliveryTimes;
  // from json
  factory DeliveryTimeResponse.fromJson(Map<String, dynamic> json) {
    return DeliveryTimeResponse(
      status: json['status'] as int,
      deliveryTimes: (json['result'] as List)
          .map((e) => DeliveryTime.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'result': deliveryTimes.map((e) => e.toJson()).toList(),
    };
  }
}
