import '../configurations.dart';

class ConfigurationResponse {
  ConfigurationResponse({
    required this.status,
    required this.configurations,
  });

  int status;
  Configurations configurations;
  // from json
  factory ConfigurationResponse.fromJson(Map<String, dynamic> json) {
    return ConfigurationResponse(
      status: json['status'] as int,
      configurations:
          Configurations.fromJson(json['result'] as Map<String, dynamic>),
    );
  }
}
