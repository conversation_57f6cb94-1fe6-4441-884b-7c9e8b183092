import '../order.dart';

class AddOrderResponse {
  AddOrderResponse({
    required this.status,
    required this.message,
    required this.order,
  });

  int status;
  String message;
  Order? order;
  // from json
  factory AddOrderResponse.fromJson(Map<String, dynamic> json) {
    return AddOrderResponse(
      status: json['status'] as int,
      message: json['message'] as String,
      order: json['result'] == null ? null : Order.fromJson(json['result']),
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'order': order?.toJson(),
    };
  }
}
