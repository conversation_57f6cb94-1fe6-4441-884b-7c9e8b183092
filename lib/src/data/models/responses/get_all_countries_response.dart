import '../country.dart';

class GetAllCountriesResponse {
  GetAllCountriesResponse({
    required this.status,
    required this.countries,
    this.message,
  });

  int status;
  List<Country> countries;
  String? message;

  // from json
  factory GetAllCountriesResponse.fromJson(Map<String, dynamic> json) {
    return GetAllCountriesResponse(
      status: json['status'] as int,
      countries: (json['result'] as List<dynamic>)
          .map((e) => Country.fromJson(e as Map<String, dynamic>))
          .toList(),
      message: json['message'] == null ? null : json['message'] as String,
    );
  }
}
