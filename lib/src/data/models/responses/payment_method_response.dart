import '../payment_method.dart';

class PaymentMethodResponse {
  PaymentMethodResponse({
    required this.status,
    required this.paymentMethods,
  });

  int status;
  List<PaymentMethod> paymentMethods;
  // from json
  factory PaymentMethodResponse.fromJson(Map<String, dynamic> json) {
    return PaymentMethodResponse(
      status: json['status'] as int,
      paymentMethods: (json['result'] as List)
          .map((e) => PaymentMethod.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'result': paymentMethods.map((e) => e.toJson()).toList(),
    };
  }
}
