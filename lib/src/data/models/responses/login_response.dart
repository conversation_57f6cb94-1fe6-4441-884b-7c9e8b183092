import '../user.dart';

class LoginResponse {
  int status;
  User? user;
  String? message;
  // constructor
  LoginResponse({
    required this.status,
    this.user,
    this.message,
  });
  // toJson
  Map<String, dynamic> toJson() => {
        'status': status,
        'user': user?.toJson(),
        'message': message,
      };

  // fromJson
  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      status: json['status'] as int,
      user: json['result'] == null
          ? null
          : User.fromJson(json['result'] as Map<String, dynamic>),
      message: json['message'] == null ? null : json['message'] as String,
    );
  }
}
