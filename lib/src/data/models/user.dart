class User {
  String image;
  String imageThumb;
  String name;
  String mobile;
  String email;
  // String facebookId;
  // String googleId;
  // String appleId;
  String token;
  String whatsapp;
  int? gender;
  // constructor
  User({
    required this.image,
    required this.imageThumb,
    required this.name,
    required this.mobile,
    required this.email,
    // required this.facebookId,
    // required this.googleId,
    // required this.appleId,
    required this.token,
    required this.whatsapp,
    this.gender,
  });
  // from json
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      image: json['image'] as String,
      imageThumb: json['image_thumb'] as String,
      name: json['name'] as String,
      mobile: json['mobile'] as String,
      email: json['email'] as String,
      // facebookId: json['facebook_id'] as String,
      // googleId: json['google_id'] as String,
      // appleId: json['apple_id'] as String,
      token: json['token'] as String,
      whatsapp: json['whatsapp'] == null ? "" : json['whatsapp'] as String,
      gender:
          json['gender'] == null ? null : int.parse(json['gender'].toString()),
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'image': image,
      'image_thumb': imageThumb,
      'name': name,
      'mobile': mobile,
      'email': email,
      // 'facebook_id': facebookId,
      // 'google_id': googleId,
      // 'apple_id': appleId,
      'token': token,
      'whatsapp': whatsapp,
      'gender': gender,
    };
  }
}
