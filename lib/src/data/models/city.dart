class City {
  City({
    required this.cityId,
    required this.cityName,
  });

  int cityId;
  String cityName;
  // from json
  factory City.fromJson(Map<String, dynamic> json) {
    return City(
      cityId: json['city_id'] as int,
      cityName: json['city_name'] as String,
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'city_id': cityId,
      'city_name': cityName,
    };
  }
}
