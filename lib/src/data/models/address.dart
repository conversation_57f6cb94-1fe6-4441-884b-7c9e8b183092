class Address {
  Address({
    required this.addressId,
    required this.addressMobile,
    required this.countryId,
    required this.countryName,
    required this.countryCode,
    required this.areaId,
    required this.areaName,
    required this.cityId,
    required this.cityName,
    required this.deliveryFees,
    required this.description,
    required this.latitude,
    required this.longitude,
  });

  int addressId;
  String addressMobile;
  int countryId;
  String countryName;
  String countryCode;
  int areaId;
  String areaName;
  int cityId;
  String cityName;
  double deliveryFees;
  String description;
  double latitude;
  double longitude;
  // full address
  String get fullAddress =>
      '$countryName - $cityName - $areaName - $description';
  // from json
  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      addressId: json['address_id'] as int,
      addressMobile: json['address_mobile'] as String,
      countryId: int.parse(json['address_country_id'] as String),
      countryName: json['address_country_name'] as String,
      countryCode: json['address_country_code'] as String,
      areaId: int.parse(json['address_area_id'] as String),
      areaName: json['address_area_name'] as String,
      cityId: int.parse(json['address_city_id'] as String),
      cityName: json['address_city_name'] as String,
      deliveryFees: json['address_delivery_fees'].toDouble(),
      description: json['address_description'] as String,
      latitude: double.parse(json['address_latitude'] as String),
      longitude: double.parse(json['address_longitude'] as String),
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'address_id': addressId,
      'address_mobile': addressMobile,
      'address_country_id': countryId,
      'address_country_name': countryName,
      'address_country_code': countryCode,
      'address_area_id': areaId,
      'address_area_name': areaName,
      'address_city_id': cityId,
      'address_city_name': cityName,
      'address_delivery_fees': deliveryFees,
      'address_description': description,
      'address_latitude': latitude,
      'address_longitude': longitude,
    };
  }
}
