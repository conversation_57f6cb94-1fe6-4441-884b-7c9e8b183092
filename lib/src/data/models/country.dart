class Country {
  Country({
    required this.countryId,
    required this.countryCode,
    required this.countryName,
    required this.countryDialCode,
    required this.countryFlag,
  });

  int countryId;
  String countryCode;
  String countryName;
  String countryDialCode;
  String countryFlag;

  // from json
  factory Country.fromJson(Map<String, dynamic> json) {
    return Country(
      countryId: json['country_id'] as int,
      countryCode: json['country_code'] as String,
      countryName: json['country_name'] as String,
      countryDialCode: json['country_dial_code'] as String,
      countryFlag: json['country_flag'] as String,
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'country_id': countryId,
      'country_code': countryCode,
      'country_name': countryName,
      'country_dial_code': countryDialCode,
      'country_flag': countryFlag,
    };
  }
}
