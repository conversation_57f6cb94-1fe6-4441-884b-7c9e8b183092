class Section {
  Section({
    required this.sectionId,
    required this.sectionName,
    required this.sectionIcon,
  });

  int sectionId;
  String sectionName;
  String sectionIcon;
  // from json
  factory Section.fromJson(Map<String, dynamic> json) {
    return Section(
      sectionId: json['section_id'] as int,
      sectionName: json['section_title'] as String,
      sectionIcon: json['section_image'] as String,
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'section_id': sectionId,
      'section_title': sectionName,
      'section_image': sectionIcon,
    };
  }
}
