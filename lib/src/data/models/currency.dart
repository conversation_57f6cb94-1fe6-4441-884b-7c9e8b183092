class Currency {
  int? currencyId;
  int? currencyIsMain;
  int? currencyAllowPriceRounded;
  String? currencyTitle;
  String? currencyName;
  String? currencyCode;
  int? currencyDayExchangeId;
  int? currencyDayExchange;

  // constructor
  Currency(
      {this.currencyId,
      this.currencyIsMain,
      this.currencyAllowPriceRounded,
      this.currencyTitle,
      this.currencyName,
      this.currencyCode,
      this.currencyDayExchangeId,
      this.currencyDayExchange});
  // from json
  factory Currency.fromJson(Map<String, dynamic> json) {
    return Currency(
      currencyId: int.tryParse(json['currency_id']?.toString() ?? ""),
      currencyIsMain: int.tryParse(json['currency_is_main']?.toString() ?? ""),
      currencyAllowPriceRounded:
          int.tryParse(json['currency_allow_price_rounded']?.toString() ?? ""),
      currencyTitle: json['currency_title'] as String?,
      currencyName: json['currency_name'] as String?,
      currencyCode: json['currency_code'] as String?,
      currencyDayExchangeId:
          int.tryParse(json['currency_day_exchange_id']?.toString() ?? ""),
      currencyDayExchange:
          int.tryParse(json['currency_day_exchange']?.toString() ?? ""),
    );
  }

  // to json
  Map<String, dynamic> toJson() {
    return {
      'currency_id': currencyId,
      'currency_is_main': currencyIsMain,
      'currency_allow_price_rounded': currencyAllowPriceRounded,
      'currency_title': currencyTitle,
      'currency_name': currencyName,
      'currency_code': currencyCode,
      'currency_day_exchange_id': currencyDayExchangeId,
      'currency_day_exchange': currencyDayExchange,
    };
  }
}
