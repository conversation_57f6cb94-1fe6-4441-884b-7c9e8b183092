class PaymentMethod {
  PaymentMethod({
    required this.paymentId,
    required this.paymentName,
    required this.paymentType,
    required this.paymentTypeLabel,
    this.paymentShalDesign,
    required this.paymentDescription,
    required this.paymentAccountNumber,
    this.paymentIcon
  });

  int paymentId;
  String paymentName;
  int paymentType;
  String paymentTypeLabel;
  int? paymentShalDesign;
  String paymentDescription;
  String paymentAccountNumber;
  String? paymentIcon;
  // from json
  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    return PaymentMethod(
      paymentId: json['payment_id'] as int,
      paymentName: json['payment_name'] as String,
      paymentType: json['payment_type'] as int,
      paymentTypeLabel: json['payment_type_label'] as String,
      paymentShalDesign: json['payment_shal_design'] as int?,
      paymentDescription: json['payment_description'] as String,
      paymentAccountNumber: json['payment_account_number'] as String,
      paymentIcon: json['payment_icon'] ,
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'payment_id': paymentId,
      'payment_name': paymentName,
      'payment_type': paymentType,
      'payment_type_label': paymentTypeLabel,
      'payment_shal_design': paymentShalDesign,
      'payment_description': paymentDescription,
      'payment_account_number': paymentAccountNumber,
    };
  }
}
