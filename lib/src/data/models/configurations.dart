class Configurations {
  Configurations({
    required this.address,
    required this.url,
    required this.email,
    required this.website,
    required this.phones,
    required this.mobile,
    this.adminMobile,
    required this.whatsapp,
    required this.facebook,
    required this.twitter,
    required this.instagram,
    required this.linkedin,
    required this.telegram,
    required this.youtube,
    required this.tiktok,
    required this.snapchat,
    required this.shareIos,
    required this.shareAndroid,
    required this.appName,
    required this.appDescription,
    this.appSignInWith,
    this.appSignInHasRegister,
    this.appSignInHasPassword,
    required this.privacyUrl,
    required this.forceUpdate,
    required this.allowCoupon,
    required this.allowCustomerWallet,
    required this.allowLoyaltyPoints,
    required this.allowOrdersRates,
    this.siteChosenProducts,
    this.siteMostOrderedProducts,
    required this.allowDeliveryTimes,
    this.siteCouponsType,
    this.siteLoyaltyPointsConditionsType,
    this.selectCurrency,
  });

  String address;
  String url;
  String email;
  String website;
  List<dynamic> phones;
  String mobile;
  String? adminMobile;
  String whatsapp;
  String facebook;
  String twitter;
  String instagram;
  String linkedin;
  String telegram;
  String youtube;
  String tiktok;
  String snapchat;
  String shareIos;
  String shareAndroid;
  String appName;
  String appDescription;
  AppSignInWith? appSignInWith;
  bool? appSignInHasRegister;
  bool? appSignInHasPassword;
  String privacyUrl;
  int forceUpdate;
  bool allowCoupon;
  bool allowCustomerWallet;
  bool allowLoyaltyPoints;
  bool allowOrdersRates;
  int? siteChosenProducts;
  int? siteMostOrderedProducts;
  bool allowDeliveryTimes;
  Map<String, dynamic>? siteCouponsType;
  Map<String, dynamic>? siteLoyaltyPointsConditionsType;
  int? selectCurrency;

  factory Configurations.defaultValue() => Configurations(
        url: '',
        address: '',
        email: '',
        website: '',
        phones: [],
        mobile: '',
        adminMobile: '',
        whatsapp: '',
        facebook: '',
        twitter: '',
        instagram: '',
        linkedin: '',
        telegram: '',
        youtube: '',
        tiktok: '',
        snapchat: '',
        shareIos: '',
        shareAndroid: '',
        appName: '',
        appDescription: '',
        privacyUrl: '',
        forceUpdate: 3,
        allowCoupon: false,
        allowCustomerWallet: true,
        allowLoyaltyPoints: false,
        appSignInHasRegister: false,
        appSignInHasPassword: false,
        allowOrdersRates: false,
        siteChosenProducts: 0,
        siteMostOrderedProducts: 0,
        allowDeliveryTimes: false,
        siteCouponsType: {},
        siteLoyaltyPointsConditionsType: {},
        selectCurrency: 0,
      );

  // from json
  factory Configurations.fromJson(Map<String, dynamic> json) {
    return Configurations(
      address: json['address'] as String,
      url: json['url'] as String,
      email: json['email'] as String,
      website: json['website'] as String,
      phones: json['phones'] as List<dynamic>,
      mobile: json['mobile'] as String,
      adminMobile: json.containsKey('admin_mobile')
          ? json['admin_mobile'] as String
          : null,
      whatsapp: json['whatsapp'] as String,
      facebook: json['facebook'] as String,
      twitter: json['twitter'] as String,
      instagram: json['instagram'] as String,
      linkedin: json['linkedin'] as String,
      telegram: json['telegram'] as String,
      youtube: json['youtube'] as String,
      tiktok: json['tiktok'] as String,
      snapchat: json['snapchat'] as String,
      shareIos: json['share_ios'] as String,
      shareAndroid: json['share_android'] as String,
      appName: json['app_name'] as String,
      appDescription: json['app_description'] as String,
      privacyUrl: json['privacy_url'] as String,
      forceUpdate: !json.containsKey("site_force_update")
          ? 3
          : json['site_force_update'] as int,
      appSignInWith: json.containsKey('AppSignInWith')
          ? AppSignInWith.fromJson(json['AppSignInWith'])
          : null,
      appSignInHasRegister: json.containsKey('AppSignInHasRegister')
          ? json['AppSignInHasRegister'] as bool
          : false,
      appSignInHasPassword: json.containsKey('AppSignInHasPassword')
          ? json['AppSignInHasPassword'] as bool
          : false,
      allowCoupon: json['site_allow_coupons'] == 1 ? true : false,
      allowCustomerWallet:
          json['site_allow_customer_wallet'] == 1 ? true : false,
      allowLoyaltyPoints: json['site_allow_loyalty_points'] == 1 ? true : false,
      allowOrdersRates: (json['site_orders_rates']) == 1 ? true : false,
      siteChosenProducts: json.containsKey('site_chosen_products')
          ? json['site_chosen_products'] as int
          : null,
      siteMostOrderedProducts: json.containsKey('site_most_ordered_products')
          ? json['site_most_ordered_products'] as int
          : null,
      allowDeliveryTimes: json['site_delivery_times'] == 1 ? true : false,
      siteCouponsType: json.containsKey('site_coupons_type')
          ? json['site_coupons_type'] as Map<String, dynamic>
          : null,
      siteLoyaltyPointsConditionsType: json
              .containsKey('site_loyalty_points_conditions_type')
          ? json['site_loyalty_points_conditions_type'] as Map<String, dynamic>
          : null,
      selectCurrency: json.containsKey('select_curreny')
          ? json['select_curreny'] as int
          : null,
    );
  }
}

class AppSignInWith {
  late bool mobileLogin;
  late bool mobileFirebaseLogin;
  late bool facebookLogin;
  late bool googleLogin;
  late bool appleLogin;

  AppSignInWith(
      {required this.mobileLogin,
      required this.appleLogin,
      required this.facebookLogin,
      required this.googleLogin,
      required this.mobileFirebaseLogin});
  factory AppSignInWith.fromJson(Map<String, dynamic> json) => AppSignInWith(
      appleLogin: json["AppleLogin"],
      facebookLogin: json["FacebookLogin"],
      googleLogin: json["GoogleLogin"],
      mobileFirebaseLogin: json["MobileFirebaseLogin"],
      mobileLogin: json["MobileLogin"]);
}
