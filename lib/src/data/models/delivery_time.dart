class DeliveryTime {
  DeliveryTime({
    required this.timeId,
    required this.timeTitle,
  });

  int timeId;
  String timeTitle;
  // from json
  factory DeliveryTime.fromJson(Map<String, dynamic> json) {
    return DeliveryTime(
      timeId: json['time_id'] as int,
      timeTitle: json['time_title'] as String,
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'time_id': timeId,
      'time_title': timeTitle,
    };
  }
}
