class PageInfo {
  PageInfo({
    required this.pageId,
    required this.pageTitle,
    required this.pageContent,
    required this.pageUrl,
    required this.pageImage,
    required this.pageIcon,
  });

  int pageId;
  String pageTitle;
  String pageContent;
  String pageUrl;
  String pageImage;
  String pageIcon;
  // from json
  factory PageInfo.fromJson(Map<String, dynamic> json) {
    return PageInfo(
      pageId: json['page_id'] as int,
      pageTitle: json['page_title'] as String,
      pageContent: json['page_content'] as String,
      pageUrl: json['page_url'] as String,
      pageImage: json['page_image'] as String,
      pageIcon: json['page_icon'] as String,
    );
  }
}
