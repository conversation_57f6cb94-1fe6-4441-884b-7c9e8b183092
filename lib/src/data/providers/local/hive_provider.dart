import 'dart:convert';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/values/hive_values.dart';
import 'package:matjari/src/data/models/currency.dart';
import 'package:matjari/src/data/models/user.dart';

import '../../../core/values/boxes.dart';
import '../../models/hive/cart_item.dart';
import '../../models/hive/product.dart';

class HiveProvider {
  // add cart item
  static void addCartItem(CartItem cartItem) {
    final box = Hive.box<CartItem>(Boxes.cart);
    box.put(cartItem.product.id, cartItem);
  }

  static void putAllCartItem(List<CartItem> cartItems) {
    final box = Hive.box<CartItem>(Boxes.cart);

    for (var cartItem in cartItems) {
      box.put(cartItem.product.id, cartItem);
    }
  }

  // remove cart item
  static void removeCartItem(int id) {
    final box = Hive.box<CartItem>(Boxes.cart);
    box.delete(id);
  }

  // is in cart
  static bool isInCart(int id) {
    final box = Hive.box<CartItem>(Boxes.cart);
    return box.containsKey(id);
  }

  // empty cart
  static Future<void> emptyCart() async {
    final box = Hive.box<CartItem>(Boxes.cart);
    await box.clear();
  }

  // empty favorite
  static Future<void> emptyFavorite() async {
    final box = Hive.box<Product>(Boxes.favorites);
    await box.clear();
  }

  // get all cart items as listenable
  static ValueListenable<Box<CartItem>> get cartItems =>
      Hive.box<CartItem>(Boxes.cart).listenable();
  // increment cart item quantity
  static void incrementCartItemQuantity(int id) {
    final box = Hive.box<CartItem>(Boxes.cart);
    final item = box.get(id);
    if (item != null) {
      item.quantity++;
      box.put(id, item);
    }
  }

  // get all cart items
  static List<CartItem> get allCartItems {
    final box = Hive.box<CartItem>(Boxes.cart);
    return box.values.toList();
  }

// decrement cart item quantity
  static void decrementCartItemQuantity(int id) {
    final box = Hive.box<CartItem>(Boxes.cart);
    final item = box.get(id);
    if (item != null) {
      item.quantity--;
      if (item.quantity == 0) {
        box.delete(id);
      } else {
        box.put(id, item);
      }
    }
  }

  // get Lisenable cart item by id
  static ValueListenable<Box<CartItem>> getLisenableCartItem(int id) {
    return Hive.box<CartItem>(Boxes.cart).listenable(keys: [id]);
  }

  // get cart items count
  static int get cartItemsCount => Hive.box<CartItem>(Boxes.cart)
      .values
      .fold(0, (previousValue, element) => previousValue + element.quantity);
  // get cart items total
  static double get cartItemsTotal {
    var totle = Hive.box<CartItem>(Boxes.cart)
        .values
        .fold<double>(0, (sum, item) => sum + item.total);

    return ((totle * pow(10, 3)).round()) / pow(10, 3);
  }

  // add to favorites
  static void addToFavorites(Product product) {
    final box = Hive.box<Product>(Boxes.favorites);
    box.put(product.id, product);
  }

  static void putAllToFavorite(List<Product> products) {
    final box = Hive.box<Product>(Boxes.favorites);

    for (var prod in products) {
      box.put(prod.id, prod);
    }
  }

  // remove from favorites
  static void removeFromFavorites(int id) {
    CommonFunctions.showDeleteConfirmBottomSheet(
      message: "deleteFromFavoritesMessage".tr,
      onConfirm: () {
        final box = Hive.box<Product>(Boxes.favorites);
        box.delete(id);
      },
    );
  }

  // get all favorites as listenable
  static ValueListenable<Box<Product>> get favorites =>
      Hive.box<Product>(Boxes.favorites).listenable();

  // get all favorites
  static List<Product> get favoriteProducts =>
      Hive.box<Product>(Boxes.favorites).values.toList();
  // add user data to settings box
  static void addUserData(User user) {
    final box = Hive.box(Boxes.settings);
    box.put('user', user.toJson());
  }

  // get listenable favorite item
  static ValueListenable<Box<Product>> getLisenableFavoriteItem(int id) {
    return Hive.box<Product>(Boxes.favorites).listenable(keys: [id]);
  }

  // is in favorites
  static bool isInFavorites(int id) {
    final box = Hive.box<Product>(Boxes.favorites);
    return box.containsKey(id);
  }

  // add user to  storage
  static Future<void> addUserToStorage(User user) async {
    await Hive.box(Boxes.appCache).put('user', json.encode(user.toJson()));
  }

  // get user from  storage
  static User? getUserFromStorage() {
    var userJson = Hive.box(Boxes.appCache).get('user');

    return userJson == null ? null : User.fromJson(json.decode(userJson));
  }

// remove user from  storage
  static Future<void> removeUserFromStorage() async {
    await Hive.box(Boxes.appCache).delete('user');
  }

  // add selected currency to  storage
  static Future<void> putSelectedCurrency(Currency currency) async {
    Hive.box(Boxes.appCache)
        .put(HiveValues.selectedCurrencyKey, json.encode(currency.toJson()));
  }

  // get selected currency from  storage
  static Currency? getSelectedCurrency() {
    return Currency(
        currencyId: 3, currencyCode: "ر.ي", currencyName: "ريال يمني");
  }
}
