//

import 'package:dio/dio.dart';
import 'package:matjari/src/data/models/responses/add_order_response.dart';
import 'package:matjari/src/data/models/responses/apply_coupon_response.dart';
import 'package:matjari/src/data/models/responses/configuration_response.dart';
import 'package:matjari/src/data/models/responses/currency_response.dart';
import 'package:matjari/src/data/models/responses/delivery_times_response.dart';
import 'package:matjari/src/data/models/responses/general_response.dart';
import 'package:matjari/src/data/models/responses/get_all_addresses_response.dart';
import 'package:matjari/src/data/models/responses/get_all_areas_response.dart';
import 'package:matjari/src/data/models/responses/get_all_cities_response.dart';
import 'package:matjari/src/data/models/responses/get_category_product_response.dart';
import 'package:matjari/src/data/models/responses/get_product_response.dart';
import 'package:matjari/src/data/models/responses/home_page_response.dart';
import 'package:matjari/src/data/models/responses/mange_address_response.dart';
import 'package:matjari/src/data/models/responses/payment_method_response.dart';
import 'package:matjari/src/data/models/responses/search_response.dart';
import 'package:retrofit/http.dart';
import 'package:matjari/src/data/services/auth_services.dart';
import 'package:retrofit/retrofit.dart';
import '../../models/responses/about_response.dart';
import '../../models/responses/get_all_countries_response.dart';
import '../../models/responses/login_response.dart';
import '../../models/responses/order_response.dart';
part 'api_provider.g.dart';

@RestApi()
abstract class ApiProvider {
  factory ApiProvider(Dio dio, {String baseUrl}) = _ApiProvider;

  factory ApiProvider.create() => _instance ??= ApiProvider(Dio());

  static ApiProvider? _instance;

//logout
  @POST('account.php?action=logout')
  Future<HttpResponse> logout();

// mobile login
  @POST('account.php?action=mobile_login')
  Future<HttpResponse> mobileLogin(@Body() FormData body);

// activation mobile login sms
  @POST('account.php?action=check_activation')
  Future<LoginResponse> checkActivation(@Body() FormData body);

  // google login
  @POST('account.php?action=google_login')
  Future<LoginResponse> googleLogin(@Body() FormData body);
  // facebook login
  @POST('account.php?action=facebook_login')
  Future<LoginResponse> facebookLogin(@Body() FormData body);
  // apple login
  @POST('account.php?action=apple_login')
  Future<LoginResponse> appleLogin(@Body() FormData body);
  // edit profile
  @POST('account.php?action=set_profile_info')
  Future<LoginResponse> updateProfile(@Body() FormData body);
  // get profile
  @POST('account.php?action=get_profile')
  Future<LoginResponse> getProfile();
  // get about info
  @POST('about.php?action=get_all')
  Future<AboutResponse> getAboutInfo();


  // get configurations
  @POST('configurations.php?action=get_all')
  Future<ConfigurationResponse> getConfigurations();
  // get login countries
  @POST('places.php?action=get_login_countries')
  Future<GetAllCountriesResponse> getLoginCountries();
  // get all countries
  @POST('places.php?action=get_addresses_countries')
  Future<GetAllCountriesResponse> getAllCountries();
  // get all cities
  @POST('places.php?action=get_cities')
  Future<GetAllCitiesResponse> getAllCities(@Query('country_id') int countryId);
  // get all areas
  @POST('places.php?action=get_areas')
  Future<GetAllAreasResponse> getAllAreas(@Query('city_id') int cityId);
  // add address
  @POST('addresses.php?action=add_address')
  Future<ManageAddressResponse> addAddress(@Body() FormData body);
  // edit address
  @POST('addresses.php?action=edit_address')
  Future<ManageAddressResponse> editAddress(@Body() FormData body);
  // delete address
  @POST('addresses.php?action=remove_address')
  Future<GeneralResponse> deleteAddress(@Query('address_id') int addressId);
  // get all addresses
  @POST('addresses.php?action=get_all')
  Future<GetAllAddressesResponse> getAllAddresses();
  // get home page data
  @POST('main.php?action=get_main')
  Future<HomePageResponse> getHomePageData();
  // get category products
  @POST('products.php?action=get_products')
  Future<GetCategoryProductResponse> getCategoryProducts(
      @Query('section_id') int categoryId,
      {@Query("page") int page = 1,
      @Query("filter") String? filtere});
  // get category products
  @POST('products.php?action=check_availability')
  Future<GetProductResponse> checkProductsAvailability(
    @Query('items') String items,
  );
  // get category products
  @POST('products.php?action=get_info')
  Future<HttpResponse> getProduct(@Query('product_id') int productId);

  // get publisher products
  @POST('products.php?action=get_products')
  Future<GetCategoryProductResponse> getPublisherProducts(
      @Query('publisher_id') int categoryId,
      {@Query("page") int page = 1});

  //visit product
  @POST('products.php?action=update_visits')
  Future<HttpResponse> visitProduct(@Query('product_id') int? productId);

  // get payment methods
  @POST('payment.php?action=get_all')
  Future<PaymentMethodResponse> getPaymentMethods(
      [@Query('address_id') int? addressId]);

// get currencies
  @POST('currencies.php?action=get_all')
  Future<CurrencyResponse> getCurrencies();

  // get delivery times
  @POST('delivery-times.php?action=get_all')
  Future<DeliveryTimeResponse> getDeliveryTimes();
  // add order
  @POST('orders.php?action=add_order')
  Future<AddOrderResponse> addOrder(@Body() FormData formData);

  @POST('orders.php?action=add_payment_info')
  Future<HttpResponse> addPaymentInfo(@Body() FormData formData);

  @POST('orders.php?action=SendConfirmPayment')
  Future<HttpResponse> sendConfirmPayment(@Body() FormData formData);
  // get orders
  @POST('orders.php?action=get_previous_orders')
  Future<OrderResponse> getOrders(@Query("page") int page);

  // get order
  @POST('orders.php?action=get_previous_order_info')
  Future<HttpResponse> getOrder(@Query("order_id") int orderId);
  // cancel order
  @POST('orders.php?action=cancel_order')
  Future<HttpResponse> cancelOrder(@Query("order_id") int orderId);

  @POST('orders.php?action=check_coupon_discount')
  Future<ApplyCouponResponse> checkCouponDiscount(@Body() FormData formData);

  // search
  @POST('products.php?action=get_search')
  Future<SearchResponse> search(@Query('keyword') String keyword);

// account termination
  @POST('account.php?action=account_termination')
  Future<HttpResponse> accountTermination(
      @Query("type") int type, @Query("reason") String reason);
}
