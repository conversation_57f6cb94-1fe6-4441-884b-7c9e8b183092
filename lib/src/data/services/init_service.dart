import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:matjari/src/core/values/boxes.dart';
import 'package:matjari/src/data/enums/page_status.dart';
import 'package:matjari/src/data/models/currency.dart';
import 'package:matjari/src/data/models/hive/cart_item.dart';
import 'package:matjari/src/data/models/hive/product.dart';
import 'package:matjari/src/data/providers/api/api_provider.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';

import 'package:matjari/src/data/services/network_service.dart';

import '../../core/values/app_config.dart';

class InitService extends GetxService {
  static InitService get instance => Get.find();

//currencies
  Rx<List<Currency>> currencies = Rx<List<Currency>>([]);
  //currency page status
  Rx<LoadingStatus> currencyPageStatus = LoadingStatus.initial.obs;

  //Rx<List<PaymentMethod>> paymentMethods = Rx<List<PaymentMethod>>([]);
  Future<InitService> init() async {
    return this;
  }

  @override
  Future<void> onInit() async {
    // getPaymentMethods();
    getCurrencies();
    //  getPaymentMethods();

    super.onInit();
  }

  // Future<void> getPaymentMethods() async {
  //   await NetworkService.instance.checkConnectivity(
  //     () async {
  //       var response = await AppConfigs.apiProvider.getPaymentMethods();
  //       Get.log('response: ${response.toJson()}');
  //       if (response.status == 0) {
  //         paymentMethods.value = response.paymentMethods;
  //       } else if (response.status == 1) {
  //         paymentMethods.value = [];
  //       } else {
  //         // CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب طرق الدفع");
  //       }
  //     },
  //     () {},
  //   );
  // }

  Future<void> getCurrencies() async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          currencyPageStatus.value = LoadingStatus.loading;
          var response = await AppConfigs.apiProvider.getCurrencies();

          if (response.status == 0) {
            currencies.value = response.currencies;
            currencyPageStatus.value = LoadingStatus.loaded;
          }
          // else if (response.status == 1) {
          // }
          else {
            currencyPageStatus.value = LoadingStatus.error;
          }
        } catch (e) {
          currencyPageStatus.value = LoadingStatus.error;
        }
      },
      () {
        currencyPageStatus.value = LoadingStatus.networkError;
      },
    );
  }

  void checkProductsAvailability() async {
    NetworkService.instance.checkConnectivity(() async {
      try {
        ApiProvider apiProvider =
            ApiProvider(AppConfigs.dioInstance, baseUrl: AppConfigs.apiUrl);

        var favorites = Hive.box<Product>(Boxes.favorites).values.toList();

        if (favorites.isNotEmpty) {
          var cfavorites = favorites.map((e) => {"id": e.id}).toList();
          var response = await apiProvider
              .checkProductsAvailability(json.encode(cfavorites));

          if (response.status == 0) {
            await HiveProvider.emptyFavorite();
            HiveProvider.putAllToFavorite(response.result.products);

            Get.log("done sync favorites ");
          }
        }

        //sync cart products
        var cartItems = Hive.box<CartItem>(Boxes.cart).values.toList();
        if (cartItems.isNotEmpty) {
          var xcartItems = cartItems.map((e) => {"id": e.product.id}).toList();

          var cartProductsRes = await apiProvider
              .checkProductsAvailability(json.encode(xcartItems));

          if (cartProductsRes.status == 0) {
            await HiveProvider.emptyCart();
            for (var cartItem in cartItems) {
              var _product = cartProductsRes.result.products
                  .firstWhere((element) => element.id == cartItem.product.id);
              cartItem.product = _product;

              HiveProvider.addCartItem(cartItem);
            }

            Get.log("done sync cart ");
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print(e);
        }
      }
    });
  }
  // Future<void> getPaymentMethods() async {
  //   await NetworkService.instance.checkConnectivity(
  //     () async {
  //       var response = await AppConfigs.apiProvider.getPaymentMethods();
  //       Get.log('response: ${response.toJson()}');
  //       if (response.status == 0) {
  //         paymentMethods.value = response.paymentMethods;
  //       } else if (response.status == 1) {
  //         paymentMethods.value = [];
  //       } else {
  //         // CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب طرق الدفع");
  //       }
  //     },
  //     () {},
  //   );
  // }
}
