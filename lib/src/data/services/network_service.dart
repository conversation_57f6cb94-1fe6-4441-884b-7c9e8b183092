import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../core/utils/common_functions.dart';
import 'loading_service.dart';

class NetworkService extends GetxService {
  static NetworkService instance = Get.find();
  var connected = false.obs;
  Future<NetworkService> init() async {
    return this;
  }

  @override
  Future<void> onInit() async {
    connected.value = !(await Connectivity().checkConnectivity())
        .contains(ConnectivityResult.none);
    Connectivity().onConnectivityChanged.listen((result) {
      connected.value = !result.contains(ConnectivityResult.none);
    });
    super.onInit();
  }

  Future<dynamic> checkConnectivity(
      Future<dynamic> Function() connectedCallback,
      [VoidCallback? notConnectedCallback]) async {
    try {
      connected.value = !(await Connectivity().checkConnectivity())
          .contains(ConnectivityResult.none);
      if (!connected.value) {
        if (notConnectedCallback == null) {
          CommonFunctions.showErrorMessage("انت غير متصل بالانترنت".tr);
        } else {
          notConnectedCallback();
        }
      } else {
        return await connectedCallback();
      }
    } catch (e) {
      LoadingService.instance.hide();
      if (notConnectedCallback == null) {
        CommonFunctions.showErrorMessage("حدث خطاء غير معروف".tr);
      } else {
        notConnectedCallback();
      }
    }
  }

  void addListener(Function(bool value) callback) {
    connected.listen(callback);
  }
}
