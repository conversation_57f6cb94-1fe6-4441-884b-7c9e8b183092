import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

import '../../core/utils/common_functions.dart';

class LoadingService extends GetxService {
  Future<LoadingService> init() async {
    return this;
  }

  static LoadingService instance = Get.find<LoadingService>();
  var isLoading = false.obs;

  void show() {
    isLoading.value = true;
  }

  void hide() {
    isLoading.value = false;
  }

  @override
  void onInit() {
    isLoading.listen((value) {
      if (value) {
        CommonFunctions.showDialog(
            contentPadding: EdgeInsets.zero,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20.0),
                  child: SpinKitThreeBounce(
                    size: 30,
                    color: Get.theme.primaryColor.withValues(alpha: 0.5),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0, bottom: 10.0),
                  child: Text(
                    "loadingMessage".tr,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Get.theme.primaryColor,
                    ),
                  ),
                )
              ],
            ),
            barrierDismissible: false,
            onWillPop: () => Future(() => !isLoading.value));
      } else {
        Get.closeAllSnackbars();
        Get.back();
      }
    });
    super.onInit();
  }
}
