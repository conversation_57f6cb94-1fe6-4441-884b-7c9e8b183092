import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/values/colors.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
// import '/src/values/colors.dart';

class PermissionWarningOverlayService {
  static final PermissionWarningOverlayService instance =
      PermissionWarningOverlayService._();
  PermissionWarningOverlayService._();

  OverlayEntry? _overlayEntry;

  void showAlert() {
    if (_overlayEntry != null) return;
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: Get.currentRoute != Routes.HOME_PAGE ? 20 : 100,
        left: 0,
        right: 0,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              AppSettings.openAppSettings(type: AppSettingsType.notification);
            },
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16),
              padding: EdgeInsetsDirectional.only(top: 8, bottom: 8, start: 16),
              decoration: BoxDecoration(
                color: AppColors.thirdColor,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.notifications_active, color: Colors.white),
                  SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      'notification permission alert'.tr,
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                          height: 1.3,
                          fontSize: 14,
                          color: Colors.white),
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close, color: Colors.white),
                    onPressed: hideOverlay,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    Overlay.of(Get.overlayContext!).insert(_overlayEntry!);
  }

  void hideOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
  }

  void dispose() {
    hideOverlay();
  }

  void showPermissionWarning() {
    if (!OneSignal.Notifications.permission) {
      showAlert();

      OneSignal.Notifications.addPermissionObserver((permission) {
        if (permission) {
          hideOverlay();
        } else {
          showAlert();
        }
      });
    }
  }
}
