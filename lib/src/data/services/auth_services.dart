import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart' hide User;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:hive/hive.dart';
import 'package:matjari/src/core/routes/app_pages.dart';
import 'package:matjari/src/core/utils/common_functions.dart';
import 'package:matjari/src/core/values/app_config.dart';
import 'package:matjari/src/core/values/boxes.dart';
import 'package:matjari/src/core/values/hive_values.dart';
import 'package:matjari/src/data/models/country.dart';
import 'package:matjari/src/data/models/responses/login_response.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';
import 'package:matjari/src/data/vms/auth/google_login_vm.dart';
import 'package:matjari/src/data/vms/auth/mobile_login_vm.dart';
import 'package:matjari/src/data/vms/auth/profile_vm.dart';
import 'package:matjari/src/data/vms/auth/sms_activation_vm.dart';
import 'package:matjari/src/data/vms/phone_number_vm.dart';
import 'package:matjari/src/view/components/bottom_sheets/code_verification_bottom_sheet.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import '../../controllers/about_app_controller.dart';
import '../models/user.dart';
import '../providers/api/api_provider.dart';
import 'loading_service.dart';
import 'network_service.dart';

class AuthService extends GetxService {
  static AuthService get instance => Get.find();
  ApiProvider apiProvider = ApiProvider(Dio(), baseUrl: AppConfigs.apiUrl);

  bool get isLoggedIn => user.value != null;

  Rx<List<Country>> loginCountries = Rx<List<Country>>([]);
  Future<AuthService> init() async {
    await getUserFromStorage();
    await getDeviceId();
    await getPlayerId();
    apiProvider =
        ApiProvider(AppConfigs.authDioInstance, baseUrl: AppConfigs.apiUrl);
    // await getLoginCountries();
    return this;
  }

  var token = ''.obs;
  Rx<User?> user = Rx<User?>(null);
  Rx<String?> deviceId = Rx<String?>(null);
  Rx<String?> playerId = Rx<String?>(null);
  Future<void> signInWithGoogle2() async {
    // Trigger the authentication flow
    final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();

    // Obtain the auth details from the request
    final GoogleSignInAuthentication? googleAuth =
        await googleUser?.authentication;

    // Create a new credential
    final credential = GoogleAuthProvider.credential(
      accessToken: googleAuth?.accessToken,
      idToken: googleAuth?.idToken,
    );

    // Once signed in, return the UserCredential
    var user = await FirebaseAuth.instance.signInWithCredential(credential);
    user.user?.getIdToken().then((token) {
      this.token.value = token ?? "";
    });
    Get.log(user.user?.uid ?? "");
  }

  // Future<void> signInWithFacebook2() async {
  //   FacebookLogin fb = FacebookLogin();
  //   // get facebook login result
  //   FacebookLoginResult res = await fb.logIn(permissions: [
  //     FacebookPermission.publicProfile,
  //     FacebookPermission.email,
  //   ]);
  //   // if (result.status == FacebookLoginStatus.success) {
  //   //   // Create a credential from the access token
  //   //   final OAuthCredential facebookAuthCredential =
  //   //       FacebookAuthProvider.credential(result.accessToken!.token);

  //   //   // Once signed in, return the UserCredential
  //   //   var credential = await FirebaseAuth.instance
  //   //       .signInWithCredential(facebookAuthCredential);
  //   //   Get.log(credential.user?.uid.toString() ?? "");
  //   // }

  //   // Check result status
  //   switch (res.status) {
  //     case FacebookLoginStatus.success:
  //       // Logged in

  //       // Send access token to server for validation and auth
  //       final FacebookAccessToken? accessToken = res.accessToken;
  //       print('Access token: ${accessToken?.token}');

  //       // Get profile data
  //       final profile = await fb.getUserProfile();
  //       print('Hello, ${profile?.name}! You ID: ${profile?.userId}');

  //       // Get user profile image url
  //       final imageUrl = await fb.getProfileImageUrl(width: 100);
  //       print('Your profile image: $imageUrl');

  //       // Get email (since we request email permission)
  //       final email = await fb.getUserEmail();
  //       // But user can decline permission
  //       if (email != null) print('And your email is $email');

  //       break;
  //     case FacebookLoginStatus.cancel:
  //       // User cancel log in
  //       break;
  //     case FacebookLoginStatus.error:
  //       // Log in failed
  //       print('Error while log in: ${res.error}');
  //       break;
  //   }
  // }

  Future<void> firbaseLogin(PhoneNumberVM phoneNumber) async {
    // check connectivity
    NetworkService.instance.checkConnectivity(() async {
      // show loading
      LoadingService.instance.show();
      FirebaseAuth auth = FirebaseAuth.instance;
      await auth.verifyPhoneNumber(
        phoneNumber: phoneNumber.countryCode + phoneNumber.phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) {
          // LoadingService.instance.hide();
        },
        verificationFailed: (FirebaseAuthException e) {
          LoadingService.instance.hide();

          printError(info: e.toString());
          print("code: $e.code");
        },
        codeSent: (String verificationId, int? resendToken) {
          LoadingService.instance.hide();
          print(verificationId);
          print("code sent");
          Get.bottomSheet(
            CodeVerificationBottomSheet(
              onConfirm: (code) async {
                try {
                  LoadingService.instance.show();
                  print(code);
                  PhoneAuthCredential credential = PhoneAuthProvider.credential(
                      verificationId: verificationId, smsCode: code);

                  var user = await auth.signInWithCredential(credential);
                  user.user?.getIdToken().then((token) {
                    printError(info: "idToken${token ?? ""}");
                  });

                  // Sign the user in (or link) with the credential

                  // get player id
                  var playerId = (OneSignal.User.pushSubscription.id) ?? "";
                  var fcmToken =
                      (await FirebaseMessaging.instance.getToken()) ?? "";
                  // create login vm
                  var vm = MobileLoginVm();
                  vm.deviceId = await CommonFunctions.getDeviceId() ?? "";
                  vm.playerId = playerId;
                  vm.fcmToken = fcmToken;
                  vm.mobile = phoneNumber.phoneNumber;
                  vm.smsActivationType = 1;
                  vm.firebaseUid = user.user?.uid ?? "";
                  vm.countryCode = phoneNumber.countryCode;
                  // call api
                  var response = await apiProvider.mobileLogin(vm.toFormData());

                  if (response.data["status"] == 0) {
                    var loginResponse = LoginResponse.fromJson(response.data);
                    User? userData = loginResponse.user;
                    if (userData != null) {
                      this.user.value = userData;
                      HiveProvider.addUserToStorage(userData);
                    }
                    backToHome();
                  }

                  // get token
                  LoadingService.instance.hide();
                } catch (e) {
                  if (kDebugMode) {
                    print((e as DioError).response?.data);
                  }

                  LoadingService.instance.hide();
                  CommonFunctions.handleError(e);
                }
              },
            ),
          );
        },
        codeAutoRetrievalTimeout: (String verificationId) {},
      );
    });
  }

  firebaseSigninAnonymously() async {
    try {
      await FirebaseAuth.instance.signInAnonymously();

      debugPrint("Signed in with temporary account.");
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case "operation-not-allowed":
          debugPrint("Anonymous auth hasn't been enabled for this project.");
          break;
        default:
          debugPrint("Unknown error.");
      }
    }
  }

  Future<void> login(PhoneNumberVM phoneNumber) async {
    // bool mobilefirebaseLogin = AboutAppController
    //         .instance.configurations.value?.appSignInWith?.mobileFirebaseLogin ??
    //     false;

    // bool mobileSmsLogin = AboutAppController
    //         .instance.configurations.value?.appSignInWith?.mobileLogin ??
    //     false;
    // print(mobileSmsLogin);
    // if (mobileSmsLogin && !mobilefirebaseLogin) {
    //   print("wqeq");
    //   smsLogin(phoneNumber);
    // } else if (!mobileSmsLogin && mobilefirebaseLogin) {
    //   NetworkService.instance.checkConnectivity(() async {
    //     LoadingService.instance.show();
    //     try {
    //       // get player id
    //       var playerId = (OneSignal.User.pushSubscription.id) ?? "";
    //       var fcmToken = (await FirebaseMessaging.instance.getToken()) ?? "";
    //       // create login vm
    //       var vm = MobileLoginVm();
    //       vm.deviceId = await CommonFunctions.getDeviceId() ?? "";
    //       vm.playerId = playerId;
    //       vm.fcmToken = fcmToken;
    //       vm.mobile = phoneNumber.phoneNumber;
    //       vm.smsActivationType = 1;
    //       vm.firebaseUid = "";
    //       vm.countryCode = phoneNumber.countryCode;
    //       // call api
    //       var response = await apiProvider.mobileLogin(vm.toFormData());
    //       LoadingService.instance.hide();

    //       if (response.data["status"] == 0) {
    //         var loginResponse = LoginResponse.fromJson(response.data);
    //         User? userData = loginResponse.user;
    //         if (userData != null) {
    //           user.value = userData;
    //           HiveProvider.addUserToStorage(userData);
    //         }
    //         backToHome();
    //       } else {
    //         firbaseLogin(phoneNumber);
    //       }
    //     } catch (e) {
    //       LoadingService.instance.hide();
    //       CommonFunctions.handleError(e);
    //     }
    //   });
    // } else if (mobileSmsLogin && mobilefirebaseLogin) {
    //   if (phoneNumber.countryCode == "+967") {
        // print("fdgdfrg");
        smsLogin(phoneNumber);
    //   } else {
    //     firbaseLogin(phoneNumber);
    //   }
    // }
  }

  Future<void> smsLogin(PhoneNumberVM phoneNumber) async {
    // check connectivity
    NetworkService.instance.checkConnectivity(() async {
      // show loading
      LoadingService.instance.show();

      try {
        // get player id
        var playerId = (OneSignal.User.pushSubscription.id) ?? "";
        var fcmToken = (await FirebaseMessaging.instance.getToken()) ?? "";
        // create login vm
        var vm = MobileLoginVm();
        vm.deviceId = await CommonFunctions.getDeviceId() ?? "";
        vm.playerId = playerId;
        vm.fcmToken = fcmToken;
        vm.mobile = phoneNumber.phoneNumber;
        vm.smsActivationType = 2;
        vm.countryCode = phoneNumber.countryCode;
        // call api
        var response = await apiProvider.mobileLogin(vm.toFormData());
        LoadingService.instance.hide();
        if (response.data["status"] == 0) {
          var loginResponse = LoginResponse.fromJson(response.data);
          User? userData = loginResponse.user;
          if (userData != null) {
            user.value = userData;
            HiveProvider.addUserToStorage(userData);
            //sign in anonymously firebase
            firebaseSigninAnonymously();
          }
          backToHome();
        }

        if (response.data["status"] == 1) {
          CommonFunctions.showErrorMessage(response.data["message"]);
        }

        if (response.data["status"] == 3) {
          Get.bottomSheet(
            CodeVerificationBottomSheet(
              onConfirm: (code) async {
                var vm = SmsActivationVM();
                vm.deviceId = await CommonFunctions.getDeviceId() ?? "";
                vm.token = response.data["result"]["token"];
                vm.code = code;
                vm.language = Get.locale?.languageCode ?? "ar";
                // call api
                try {
                  LoadingService.instance.show();
                  var activationResponse =
                      await apiProvider.checkActivation(vm.toFormData());
                  LoadingService.instance.hide();

                  if (activationResponse.status == 0) {
                    User? userData = activationResponse.user;
                    if (userData != null) {
                      user.value = userData;
                      HiveProvider.addUserToStorage(userData);
                    }
                    backToHome();
                  }

                  if (activationResponse.status == 1) {
                    CommonFunctions.showErrorMessage(
                        activationResponse.message ?? "");
                  }
                } catch (e) {
                  LoadingService.instance.hide();
                  CommonFunctions.handleError(e);
                }
              },
            ),
            isDismissible: false,
          );
        }
      } catch (e) {
        // if (kDebugMode) {
        print((e as DioException).response?.data);
        // }

        LoadingService.instance.hide();
        CommonFunctions.handleError(e);
      }
    });
  }

  Future<void> signInWithGoogle() async {
    // check connectivity
    NetworkService.instance.checkConnectivity(() async {
      // show loading
      LoadingService.instance.show();
      try {
        // Trigger the authentication flow
        final GoogleSignInAccount? googleUser = await GoogleSignIn(
          scopes: [
            'email',
            'https://www.googleapis.com/auth/contacts.readonly',
          ],
        ).signIn();

        // Obtain the auth details from the request
        final GoogleSignInAuthentication? googleAuth =
            await googleUser?.authentication;

        // Create a new credential
        final credential = GoogleAuthProvider.credential(
          accessToken: googleAuth?.accessToken,
          idToken: googleAuth?.idToken,
        );

        // Once signed in, return the UserCredential
        var userCredential =
            await FirebaseAuth.instance.signInWithCredential(credential);

        Get.log(userCredential.user.toString());
        var user = userCredential.user;
        // String idToken = auth.idToken ?? "";
        // get player id
        var playerId = (OneSignal.User.pushSubscription.id) ?? "";
        var fcmToken = (await FirebaseMessaging.instance.getToken()) ?? "";
        // create login vm
        var vm = GoogleLoginVm();
        vm.email = user?.email ?? "";
        vm.name = user?.displayName ?? "";
        vm.playerId = playerId;
        vm.fcmToken = fcmToken;
        vm.googleId = "";
        vm.accessToken = "";
        vm.firebaseUid = user?.uid ?? "";
        vm.deviceId = await CommonFunctions.getDeviceId() ?? "";
        var response = await ApiProvider(Dio()).googleLogin(vm.toFormData());
        LoadingService.instance.hide();
        if (response.status == 0) {
          User? userData = response.user;
          if (userData != null) {
            this.user.value = userData;
            HiveProvider.addUserToStorage(userData);
          }
          backToHome();
        } else if (response.status == 1) {
          CommonFunctions.showErrorMessage(response.message ?? "");
        }
      } catch (error) {
        LoadingService.instance.hide();
        if (kDebugMode) {
          print(
            "error--------------- $error",
          );

          print("errrrrrrrrrror");
          print((error as PlatformException).message);
        }
        CommonFunctions.showErrorMessage(
            "حدث خطأ في تسجيل الدخول بواسطة Google");
      }
    });
  }

  // Future<void> signInWithFacebook() async {
  //   // check connectivity
  //   NetworkService.instance.checkConnectivity(() async {
  //     // show loading
  //     LoadingService.instance.show();
  //     // create instance of facebook login
  //     FacebookAuthProvider facebookLogin = FacebookAuthProvider();
  //     // get facebook login result
  //     FacebookLoginResult result = await facebookLogin.(permissions: [
  //       FacebookPermission.publicProfile,
  //       FacebookPermission.email,
  //     ]);
  //     if (result.status == FacebookLoginStatus.success) {
  //       // Create a credential from the access token
  //       final OAuthCredential facebookAuthCredential =
  //           FacebookAuthProvider.credential(result.accessToken!.token);

  //       // Once signed in, return the UserCredential
  //       var credential = await FirebaseAuth.instance
  //           .signInWithCredential(facebookAuthCredential);
  //       Get.log(credential.user?.uid.toString() ?? "");

  //       final String? accessToken = result.accessToken?.token;
  //       // Get profile data
  //       final profile = await facebookLogin.getUserProfile();

  //       // Get email (since we request email permission)
  //       final email = await facebookLogin.getUserEmail();
  //       // get player id
  //       var playerId = (await OneSignal.shared.getDeviceState())?.userId ?? "";
  //       // create login vm
  //       var vm = FacebookLoginVm();
  //       vm.email = email ?? "";
  //       vm.name = profile?.name ?? "";
  //       vm.playerId = playerId;
  //       vm.facebookId = profile?.userId ?? "";
  //       vm.fbAccessToken = accessToken ?? "";
  //       vm.firebaseUid = credential.user?.uid ?? "";
  //       vm.deviceId = await CommonFunctions.getDeviceId() ?? "";
  //       printError(info: vm.toJson().toString());
  //       var response = await ApiProvider(Dio()).facebookLogin(vm.toFormData());
  //       LoadingService.instance.hide();
  //       print(response.toJson());
  //       if (response.status == 0) {
  //         User? userData = response.user;
  //         if (userData != null) {
  //           user.value = userData;
  //           addUserToSecureStorage(userData);
  //         }
  //         backToHome();
  //       } else if (response.status == 1) {
  //         CommonFunctions.showErrorMessage(response.message ?? "");
  //       }
  //     }
  //   });
  // }

  void signInWithApple() async {
    // check connectivity
    NetworkService.instance.checkConnectivity(() async {
      // show loading
      LoadingService.instance.show();
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        webAuthenticationOptions: WebAuthenticationOptions(
            clientId: '', redirectUri: Uri.parse('matjari://apple-sign-in')),
      );

      print(credential);
      LoadingService.instance.hide();
    });
  }

  // get user profile
  Future<void> getUserProfile() async {
    // check connectivity
    NetworkService.instance.checkConnectivity(() async {
      try {
        LoadingService.instance.show();
        var response = await AppConfigs.apiProvider.getProfile();
        print(response.toJson());

        if (response.status == 0) {
          user.value = response.user;
          HiveProvider.addUserToStorage(response.user!);
          LoadingService.instance.hide();
        } else if (response.status == 1) {
          CommonFunctions.showErrorMessage(response.message ?? "");
        } else if (response.status == 2) {
          CommonFunctions.showErrorMessage(response.message ?? "");
          Get.toNamed(Routes.LOGIN_PAGE);
        }
      } catch (e) {
        LoadingService.instance.hide();
      }
      // show loading
    });
  }

  // update user profile
  Future<void> updateUserProfile(ProfileVM profileVM) async {
    // check connectivity
    NetworkService.instance.checkConnectivity(() async {
      // show loading
      LoadingService.instance.show();
      var response =
          await AppConfigs.apiProvider.updateProfile(profileVM.toFormData());
      print(response.toJson());
      LoadingService.instance.hide();
      if (response.status == 0) {
        user.value = response.user;
        HiveProvider.addUserToStorage(response.user!);
        CommonFunctions.showSuccessMessage(response.message ?? "");
      } else if (response.status == 1) {
        CommonFunctions.showErrorMessage(response.message ?? "");
      } else if (response.status == 2) {
        CommonFunctions.showErrorMessage(response.message ?? "");
        Get.toNamed(Routes.LOGIN_PAGE);
      }
    });
  }

  // logout
  Future<void> logout() async {
    // TODO: call api to logout
    ApiProvider _apiProvider =
        ApiProvider(AppConfigs.dioInstance, baseUrl: AppConfigs.apiUrl);

    try {
      var response = _apiProvider.logout();
      if (kDebugMode) {
        print(response);
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }

    user.value = null;
    HiveProvider.removeUserFromStorage();
    HiveProvider.emptyCart();
    HiveProvider.emptyFavorite();
  }

  // get user from  storage
  Future<void> getUserFromStorage() async {
    var user = HiveProvider.getUserFromStorage();
    if (user != null) {
      this.user.value = user;
    }
  }

  // get device id
  Future<void> getDeviceId() async {
    deviceId.value = await CommonFunctions.getDeviceId();
  }

  // get player id
  Future<void> getPlayerId() async {
    print("getPlayerId.........................................");
    var playerId = (OneSignal.User.pushSubscription.id) ?? "";
    Get.log("playerId: $playerId");
    print("getPlayerId..............Done............................");
  }

  // get login countries
  Future<void> getLoginCountries() async {
    // check connectivity
    NetworkService.instance.checkConnectivity(() async {
      try {
        var response = await AppConfigs.apiProvider.getLoginCountries();
        if (response.status == 0) {
          loginCountries.value = response.countries;
        } else if (response.status == 1) {
          CommonFunctions.showErrorMessage(response.message ?? "");
        } else if (response.status == 2) {
          CommonFunctions.showErrorMessage(response.message ?? "");
        }
      } catch (e) {
        if (kDebugMode) {
          print('hello');
          print(e);
        }
      }
    });
  }

  void backToHome() {
    Get.back();
    if (AuthService.instance.user.value != null &&
        (AuthService.instance.user.value?.name == null ||
            AuthService.instance.user.value?.name == "")) {
      CommonFunctions.showProfileComplatenessAlertDialog();
    }
  }

  void accountTermination(int type, String reason) {
    NetworkService.instance.checkConnectivity(() async {
      try {
        Get.back();
        LoadingService.instance.show();
        var response =
            await AppConfigs.apiProvider.accountTermination(type, reason);
        LoadingService.instance.hide();

        if (response.data["status"] == 0) {
          CommonFunctions.showSuccessMessage(response.data["message"]);
          user.value = null;
          HiveProvider.removeUserFromStorage();
        } else {
          CommonFunctions.showErrorMessage(response.data["message"]);
        }
      } catch (e) {
        LoadingService.instance.hide();
        CommonFunctions.handleError(e);
      }
    });
  }
}
