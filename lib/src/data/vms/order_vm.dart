import 'dart:convert';

import 'package:dio/dio.dart';

class OrderVM {
  late int adressId;
  late int deliveryTime;
  late int paymentId;
  String? voucher;
  String? paymentAccountId;
  late String notes;
  String? transferNumber;
  String? transferCompany;
  String? coupon;
  bool couponCodeApplied = false;

  late List<Map<String, int>> items;
  // to json
  Map<String, dynamic> toJson() {
    var data = {
      'address_id': adressId,
      'delivery_time': deliveryTime,
      'payment_id': paymentId,
      'notes': notes,
      'voucher': voucher,
      "payment_account_id": paymentAccountId,
      'items': jsonEncode(items),
      'transfer_number': transferNumber,
      'transfer_company': transferCompany,
      'coupon': coupon,
    };
    data.removeWhere((key, value) => value == null);
    return data;
  }

  // to form data
  FormData toFormData() {
    return FormData.fromMap(toJson());
  }
}
