import 'package:dio/dio.dart';

class AddressVm {
  int? countryId;
  int? addressId;
  late String description;
  late double latitude;
  late double longitude;
  late int cityId;
  late int areaId;
  // to json
  Map<String, dynamic> toJson() {
    return {
      'country_id': countryId ?? 235,
      'address_id': addressId,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'city_id': cityId,
      'area_id': areaId,
    };
  }

  // to form data
  FormData toFormData() {
    return FormData.fromMap(toJson());
  }
}
