import 'package:dio/dio.dart';

class AddPaymentInfoVm {
  late int orderId;
  late int paymentId;
  String? voucher;
  String? paymentAccountId;
  String? transferNumber;
  String? transferCompany;

  // to json
  Map<String, dynamic> toJson() {
    return {
      'order_id': orderId,
      'payment_id': paymentId,
      'voucher': voucher,
      "payment_account_id": paymentAccountId,
      'transfer_number': transferNumber,
      'transfer_company': transferCompany,
    };
  }

  // to form data
  FormData toFormData() {
    return FormData.fromMap(toJson());
  }
}
