import 'package:dio/dio.dart';

class SmsActivationVM {
  late String deviceId;
  late String token;
  late String code;
  late String language;

  // to json
  Map<String, dynamic> toJson() => {
        'device_id': deviceId,
        'token': token,
        'activation_code': code,
        'language': language,

        // "password": "123456"
      };
  // to form data
  FormData toFormData() => FormData.fromMap(toJson());
}
