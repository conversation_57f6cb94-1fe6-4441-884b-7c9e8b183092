import 'package:dio/dio.dart';

class FacebookLoginVm {
  late String deviceId;
  late String playerId;
  late String facebookId;
  late String name;
  late String email;
  late String fbAccessToken;
  late String firebaseUid;
  late String fcmToken;
  // to json
  Map<String, dynamic> toJson() => {
        'device_id': deviceId,
        'player_id': playerId,
        'facebook_id': facebookId,
        'name': name,
        'email': email,
        'fb_access_token': fbAccessToken,
        'firebase_uid': firebaseUid,
        'fcm_token': fcmToken,
      };
  // to form data
  FormData toFormData() => FormData.fromMap(toJson());
}
