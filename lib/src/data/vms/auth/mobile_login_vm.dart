import 'package:dio/dio.dart';

class MobileLoginVm {
  late String deviceId;
  late String playerId;
  late String fcmToken;
  late String mobile;
  late int smsActivationType;
  String? firebaseUid;
  late String countryCode;
  // to json
  Map<String, dynamic> toJson() => {
        'device_id': deviceId,
        'fcm_token': fcmToken,
        'player_id': playerId,
        'mobile': mobile,
        'sms_activation_type': smsActivationType,
        'firebase_uid': firebaseUid,
        'country_code': countryCode,
        // "password": "123456"
      };
  // to form data
  FormData toFormData() => FormData.fromMap(toJson());
}
