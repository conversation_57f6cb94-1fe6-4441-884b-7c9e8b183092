class RegisterVM {
  late int? id;
  late String name;
  late String email;
  late String phone;
  late String image;
  late String password;
  late String passwordConfirmation;
  late int type;
  late String address;
  // toJson
  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'email': email,
        'phone': phone,
        'image': image,
        'password': password,
        'passwordConfirmation': passwordConfirmation,
        'type': type,
        'address': address,
      };
}
