import 'package:dio/dio.dart';

class GoogleLoginVm {
  late String deviceId;
  late String playerId;
  late String fcmToken;
  late String name;
  late String email;
  late String googleId;
  late String accessToken;
  late String firebaseUid;
  // to json
  Map<String, dynamic> toJson() => {
        'device_id': deviceId,
        'player_id': playerId,
        'name': name,
        'email': email,
        'google_id': googleId,
        'access_token': accessToken,
        'firebase_uid': firebaseUid,
        'fcm_token': fcmToken,
      };
  // to form data
  FormData toFormData() => FormData.fromMap(toJson());
}
