import 'package:dio/dio.dart';

class AppleLoginVm {
  late String deviceId;
  late String playerId;
  late String appleId;
  late String name;
  late String email;
  late String appleToken;
  late String firebaseUid;
  // to json
  Map<String, dynamic> toJson() => {
        'device_id': deviceId,
        'player_id': playerId,
        'apple_id': appleId,
        'name': name,
        'email': email,
        'apple_token': appleToken,
        'firebase_uid': firebaseUid,
      };
  // to form data
  FormData toFormData() => FormData.fromMap(toJson());
}
