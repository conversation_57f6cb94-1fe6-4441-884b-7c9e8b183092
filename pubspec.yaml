name: matjari
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+2beta #android
# version: 1.0.0+10   #IOS
environment:
  sdk: ">=2.17.1 <3.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  get: ^4.6.6
  flutter_svg: ^2.0.7
  carousel_slider: ^5.0.0
  marquee: ^2.2.3
  hive: ^2.2.2
  hive_flutter: ^1.1.0
  google_maps_flutter: ^2.5.0
  map_picker: ^0.0.3
  badges: ^3.1.2
  retrofit: ^4.0.2
  dio: ^5.3.3
  flutter_spinkit: ^5.2.0
  connectivity_plus: ^6.0.3
  google_fonts: ^6.1.0
  package_info_plus: ^8.0.0 
  share_plus: ^10.0.0
  location: ^8.0.0
  flutter_slidable: ^4.0.0  
  # libphonenumber: ^2.0.2
  url_launcher: ^6.1.14
  google_sign_in: ^6.1.5
  sign_in_with_apple: ^6.1.0
  # onesignal_flutter: ^5.0.0
  # firebase_core: ^2.9.0
  firebase_auth: ^5.2.0
  # firebase_remote_config: ^4.2.6
  # firebase_analytics: ^10.4.5
  cached_network_image: ^3.3.0
  # platform_device_id: ^1.0.1
  firebase_messaging: ^15.1.0
  # firebase_crashlytics: ^3.3.5
  overlay_support: ^2.1.0
  flutter_ringtone_player: ^4.0.0+2
  firebase_crashlytics: ^4.1.0
  firebase_core: ^3.4.0
  firebase_remote_config: ^5.1.0
  firebase_analytics: ^11.3.0
  onesignal_flutter: ^5.0.3
  dio_smart_retry: ^7.0.1
  device_info_plus: ^11.2.0
  flutter_polyline_points: ^2.0.0
  # cloud_firestore: ^4.17.4
  firebase_database: ^11.1.1
  tuple: ^2.0.2
  lottie: ^3.2.0
  # device_info_plus: ^9.0.3
  # flutter_facebook_auth: ^6.0.1
  unique_identifier: ^0.4.0
  app_settings: ^6.1.1



dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.14.1
  hive_generator: ^2.0.1
  build_runner: ^2.4.6
  retrofit_generator: ^9.1.2
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
flutter_launcher_icons:
  ios: true
  android: true
  image_path_ios: "assets/launcher/icon.png"
  image_path_android: "assets/launcher/icon.png"
  
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/launcher/forground.png"


# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/vectors/
    - assets/images/
    - assets/lotties/
    - assets/lotties/order_status/
    - assets/vectors/order_status/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: MatjariIcons
      fonts:
        - asset: assets/fonts/MatjariIcons.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
