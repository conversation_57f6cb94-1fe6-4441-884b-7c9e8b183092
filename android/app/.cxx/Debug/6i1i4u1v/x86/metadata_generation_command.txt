                        -H/Users/<USER>/Documents/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-<PERSON><PERSON><PERSON>O<PERSON>_PLATFORM=android-23
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/flutter_projects/matjari/build/app/intermediates/cxx/Debug/6i1i4u1v/obj/x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/flutter_projects/matjari/build/app/intermediates/cxx/Debug/6i1i4u1v/obj/x86
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/flutter_projects/matjari/android/app/.cxx/Debug/6i1i4u1v/x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2