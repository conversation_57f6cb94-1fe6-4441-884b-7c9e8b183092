{"v": "5.10.2", "fr": 25, "ip": 0, "op": 75, "w": 500, "h": 500, "nm": "5 Courier Delivery", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Main Stroke width - Color Ctrl", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [50, 50, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Stroke width", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 16, "ix": 1}}]}, {"ty": 5, "nm": "Base Color", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0, 0, 0, 1], "ix": 1}}]}, {"ty": 5, "nm": "Highlight", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.518, 0.322, 0.259, 1], "ix": 1}}]}], "ip": 0, "op": 75.8333333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Lines 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [132, 367, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[150, -140], [-6, -140]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000001496, 0.800000059838, 0.800000059838, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Highlight')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.69], "y": [1]}, "o": {"x": [0.31], "y": [0]}, "t": 36.333, "s": [0]}, {"t": 44.3333333333333, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.69], "y": [1]}, "o": {"x": [0.31], "y": [0]}, "t": 30.333, "s": [0]}, {"t": 39.3333333333333, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 30.3333333333333, "op": 45, "st": 30.3333333333333, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Lines 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [84, 399, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[120, -140], [-6, -140]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000001496, 0.800000059838, 0.800000059838, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Highlight')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.69], "y": [1]}, "o": {"x": [0.31], "y": [0]}, "t": 33.333, "s": [0]}, {"t": 41.3333333333333, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.69], "y": [1]}, "o": {"x": [0.31], "y": [0]}, "t": 27.333, "s": [0]}, {"t": 36.3333333333333, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 27.3333333333333, "op": 42, "st": 27.3333333333333, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Lines 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [63, 431, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[150, -140], [-6, -140]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000001496, 0.800000059838, 0.800000059838, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Highlight')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.69], "y": [1]}, "o": {"x": [0.31], "y": [0]}, "t": 41.667, "s": [0]}, {"t": 49.6666666666667, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.69], "y": [1]}, "o": {"x": [0.31], "y": [0]}, "t": 35.667, "s": [0]}, {"t": 44.6666666666667, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 35.6666666666667, "op": 50, "st": 35.6666666666667, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "light 2", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -1.33, "ix": 10}, "p": {"a": 0, "k": [47.532, -1.654, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [186.103, -43.036, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[178, -43], [222, -43]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000001496, 0.800000059838, 0.800000059838, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Highlight')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [144, -54.5], "ix": 2}, "a": {"a": 0, "k": [144, -43.5], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": -22, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[178, -43], [222, -43]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000001496, 0.800000059838, 0.800000059838, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Highlight')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [144, -43.5], "ix": 2}, "a": {"a": 0, "k": [144, -43.5], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[178, -43], [222, -43]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000001496, 0.800000059838, 0.800000059838, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Highlight')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [144, -32.5], "ix": 2}, "a": {"a": 0, "k": [144, -43.5], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 22, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 27.995, "s": [0]}, {"t": 36.9996744791667, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.167, "s": [0]}, {"t": 31.0250651041667, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 22.1666666666667, "op": 37, "st": 22.1666666666667, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "light", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [-8.551]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 29, "s": [5.726]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [-8.551]}, {"t": 55, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [317.841, 148.669, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-16.187, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 13.749], [0, 0], [-13.749, 0], [0, 0]], "o": [[0, 0], [-13.749, 0], [0, 0], [0, -13.749], [0, 0], [0, 0]], "v": [[16.187, 24.895], [8.709, 24.895], [-16.187, 0], [-16.187, 0], [8.709, -24.895], [16.187, -24.895]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Highlight')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 75, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "mask", "parent": 6, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 13.749], [0, 0], [-13.749, 0], [0, 0]], "o": [[0, 0], [-13.749, 0], [0, 0], [0, -13.749], [0, 0], [0, 0]], "v": [[16.187, 24.895], [8.709, 24.895], [-16.187, 0], [-16.187, 0], [8.709, -24.895], [16.187, -24.895]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0.011764706112, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 75, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "scooter", "parent": 10, "tt": 2, "tp": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [247.3, 251.368, 0], "to": [0, -1.5, 0], "ti": [0, 0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 33, "s": [247.3, 242.368, 0], "to": [0, -0.167, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [247.3, 250.368, 0], "to": [0, -0.333, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 47, "s": [247.3, 240.368, 0], "to": [0, 0.167, 0], "ti": [0, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 54, "s": [247.3, 251.368, 0], "to": [0, 0.5, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61, "s": [247.3, 243.368, 0], "to": [0, 0, 0], "ti": [0, -1.333, 0]}, {"t": 67.5, "s": [247.3, 251.368, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [247.3, 251.368, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 5.271], [0, 0], [-5.271, 0], [0, 0], [0, -5.271], [0, 0], [5.271, 0], [0, 0]], "o": [[0, 0], [0, 0], [-5.271, 0], [0, 0], [0, -5.271], [0, 0], [5.271, 0], [0, 0], [0, 5.271], [0, 0], [0, 0]], "v": [[-32.32, 17.085], [-45.603, 17.085], [-54.168, 17.085], [-63.712, 7.541], [-63.712, -7.541], [-54.168, -17.085], [54.168, -17.085], [63.712, -7.541], [63.712, 7.541], [54.168, 17.085], [45.02, 17.085]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Base Color')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [178.229, 218.572], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 6.094], [0, 0], [-10.522, 0], [0, 0]], "o": [[0, 0], [-6.094, 0], [0, 0], [0, -10.522], [0, 0], [0, 0]], "v": [[51.01, 15.043], [-39.975, 15.043], [-51.01, 4.009], [-51.01, 4.009], [-31.958, -15.043], [51.01, -15.043]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Base Color')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [129.51, 339.649], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-11.255, 0], [11.255, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Base Color')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [275.22, 148.669], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-29.272, 0], [0, 0], [0, -10.797], [0, 0], [0, 0], [2.375, 16.172], [0, 0], [0, 0]], "o": [[0, 0], [0, -29.272], [0, 0], [10.797, 0], [0, 0], [0, 0], [16.345, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.193, 87.153], [-112.193, 52.865], [-59.191, -0.137], [18.149, -0.137], [37.699, 19.413], [37.699, 59.239], [72.491, 59.239], [98.997, 28.557], [82.006, -87.153], [112.193, -87.153]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Base Color')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [205.101, 235.821], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [16.993, -13.226], [0, 0], [0, 0]], "o": [[0, 0], [8.301, 19.869], [0, 0], [0, 0], [0, 0]], "v": [[45.808, -95.803], [91.873, 12.271], [77.149, 68.334], [41.858, 95.803], [-95.566, 95.803]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Base Color')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [277.496, 243.846], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 6.094], [0, 0], [10.522, 0], [0, 0]], "o": [[0, 0], [6.094, 0], [0, 0], [0, -10.522], [0, 0], [0, 0]], "v": [[-25.915, 15.043], [14.88, 15.043], [25.915, 4.009], [25.915, 4.009], [6.863, -15.043], [-15.642, -15.043]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Base Color')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [390.184, 289.353], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 75, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "mask", "parent": 8, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [247.289, 251.687, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [247.288, 251.687, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, -10.565], [0, 0], [-6.119, 0]], "o": [[0, 0], [0, 0], [-10.565, 0], [0, 0], [0, 6.119], [0, 0]], "v": [[51.218, 15.105], [51.218, -15.105], [-32.088, -15.105], [-51.218, 4.025], [-51.218, 4.025], [-40.138, 15.105]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0.011764706112, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [129.018, 340.015], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[17.063, -13.28], [0, 0], [0, 0], [0, 0], [-29.392, 0], [0, 0], [0, -10.841], [0, 0], [0, 0], [2.384, 16.238], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -29.392], [0, 0], [10.841, 0], [0, 0], [0, 0], [16.412, 0], [0, 0], [0, 0], [0, 0], [8.335, 19.95]], "v": [[122.158, 68.299], [86.722, 95.88], [-140.649, 95.88], [-140.649, 44.709], [-87.43, -8.509], [-9.775, -8.509], [9.855, 11.121], [9.855, 51.109], [44.789, 51.109], [71.403, 20.302], [54.343, -95.88], [91.866, -95.88], [136.941, 12.007]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0.011764706112, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [232.915, 244.135], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.853, 0], [0, 0], [0, 5.34], [0, 0], [4.853, 0], [0, 0], [0, -5.34], [0, 0]], "o": [[0, 0], [4.853, 0], [0, 0], [0, -5.34], [0, 0], [-4.853, 0], [0, 0], [0, 5.34]], "v": [[-55.185, 17.155], [55.185, 17.155], [63.972, 7.486], [63.972, -7.486], [55.185, -17.155], [-55.185, -17.155], [-63.972, -7.486], [-63.972, 7.486]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0.011764706112, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [177.936, 217.989], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 6.119], [0, 0], [10.565, 0], [0, 0]], "o": [[0, 0], [6.119, 0], [0, 0], [0, -10.565], [0, 0], [0, 0]], "v": [[-55.608, 15.105], [44.529, 15.105], [55.608, 4.025], [55.608, 4.025], [36.479, -15.105], [-55.608, -15.105]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0.011764706112, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [361.169, 289.513], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 75, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "wheels", "tt": 2, "tp": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [1], "y": [0]}, "t": 14, "s": [-8.551]}, {"t": 27, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [176.99, 340.649, 0], "to": [-3.333, 0, 0], "ti": [-3.333, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [156.99, 340.649, 0], "to": [3.333, 0, 0], "ti": [-1.667, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [196.99, 340.649, 0], "to": [1.667, 0, 0], "ti": [1.667, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [166.99, 340.649, 0], "to": [-1.667, 0, 0], "ti": [-1.667, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 55, "s": [186.99, 340.649, 0], "to": [1.667, 0, 0], "ti": [1.667, 0, 0]}, {"t": 67, "s": [176.99, 340.649, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [176.99, 340.649, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [0, 0], [0, 0], [0, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Highlight')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [370.475, 339.284], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -27.689], [28.295, 0], [0, 27.689], [-28.295, 0]], "o": [[0, 27.689], [-28.295, 0], [0, -27.689], [28.295, 0]], "v": [[51.233, 0], [0, 50.136], [-51.233, 0], [0, -50.136]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Highlight')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [177.013, 339.649], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -27.689], [28.295, 0], [0, 27.689], [-28.295, 0]], "o": [[0, 27.689], [-28.295, 0], [0, -27.689], [28.295, 0]], "v": [[51.233, 0], [0, 50.136], [-51.233, 0], [0, -50.136]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Highlight')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [370.967, 339.649], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 75, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "mask 2", "parent": 8, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [247.289, 251.687, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [247.288, 251.687, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, -10.565], [0, 0], [-6.119, 0]], "o": [[0, 0], [0, 0], [-10.565, 0], [0, 0], [0, 6.119], [0, 0]], "v": [[51.218, 15.105], [51.218, -15.105], [-32.088, -15.105], [-51.218, 4.025], [-51.218, 4.025], [-40.138, 15.105]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0.011764706112, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [129.018, 340.015], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[17.063, -13.28], [0, 0], [0, 0], [0, 0], [-29.392, 0], [0, 0], [0, -10.841], [0, 0], [0, 0], [2.384, 16.238], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -29.392], [0, 0], [10.841, 0], [0, 0], [0, 0], [16.412, 0], [0, 0], [0, 0], [0, 0], [8.335, 19.95]], "v": [[122.158, 68.299], [86.722, 95.88], [-140.649, 95.88], [-140.649, 44.709], [-87.43, -8.509], [-9.775, -8.509], [9.855, 11.121], [9.855, 51.109], [44.789, 51.109], [71.403, 20.302], [54.343, -95.88], [91.866, -95.88], [136.941, 12.007]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0.011764706112, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [232.915, 244.135], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.853, 0], [0, 0], [0, 5.34], [0, 0], [4.853, 0], [0, 0], [0, -5.34], [0, 0]], "o": [[0, 0], [4.853, 0], [0, 0], [0, -5.34], [0, 0], [-4.853, 0], [0, 0], [0, 5.34]], "v": [[-55.185, 17.155], [55.185, 17.155], [63.972, 7.486], [63.972, -7.486], [55.185, -17.155], [-55.185, -17.155], [-63.972, -7.486], [-63.972, 7.486]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0.011764706112, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [177.936, 217.989], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 6.119], [0, 0], [10.565, 0], [0, 0]], "o": [[0, 0], [6.119, 0], [0, 0], [0, -10.565], [0, 0], [0, 0]], "v": [[-55.608, 15.105], [44.529, 15.105], [55.608, 4.025], [55.608, 4.025], [36.479, -15.105], [-55.608, -15.105]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0.011764706112, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [361.169, 289.513], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 75, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "box", "parent": 8, "tt": 2, "tp": 11, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 37, "s": [3.2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [-2.316]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 51, "s": [3.2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 58, "s": [-2.316]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 65, "s": [3.2]}, {"t": 72, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 28, "s": [177.464, 210.826, 0], "to": [0, -1.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 35, "s": [177.464, 203.826, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 42, "s": [177.464, 210.826, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 49, "s": [177.464, 203.826, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 56, "s": [177.464, 210.826, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 63, "s": [177.464, 203.826, 0], "to": [0, 0, 0], "ti": [0, -1.167, 0]}, {"t": 70, "s": [177.464, 210.826, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [177.464, 210.826, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0.919], [0, 0], [-2.55, 0], [0, 0], [0, -2.55], [0, 0], [0.707, 0.586]], "o": [[0, 0], [-0.707, 0.586], [0, 0], [0, -2.55], [0, 0], [2.55, 0], [0, 0], [0, 0.919], [0, 0]], "v": [[0, 13.147], [-14.437, 25.112], [-16.214, 24.276], [-16.214, -20.745], [-11.597, -25.363], [11.597, -25.363], [16.214, -20.745], [16.214, 24.276], [14.437, 25.112]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Highlight')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [177.464, 136.147], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -1.16], [0, 0], [0, 0], [0, 0], [-1.16, 0], [0, 0]], "o": [[0, 0], [1.16, 0], [0, 0], [0, 0], [0, 0], [0, -1.16], [0, 0], [0, 0]], "v": [[17.147, -45.694], [47.317, -45.694], [49.417, -43.594], [49.417, 45.533], [-49.417, 45.694], [-49.417, -43.594], [-47.317, -45.694], [-17.042, -45.694]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Highlight')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [177.464, 165.132], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 75, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 3, "nm": "Zero | troke width - Color Ctrl", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250, 250, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 75.8333333333333, "st": 0, "bm": 0}], "markers": []}